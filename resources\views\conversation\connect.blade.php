@extends('layouts.new')
@section('title')
	{{$title ?? 'Vira WhatsApp Automation'}}
@endsection

@section('content')
	<section class="container text-center margin_top" x-data="data()">
	    <h2>Connect Waba Numbers</h2>
	    <div class="container-sm py-2">
	    		<template x-if="failed">
				<div class="text-center">
					<div class="alert alert-danger" role="alert" x-text="failed"></div>
				</div>
			</template>
			<template x-if="success">
				<div class="text-center">
					<div class="alert alert-success" role="alert" x-text="success"></div>
				</div>
			</template>
			<template x-if="loading">
				<div class="text-center">
					<div class="spinner-border text-primary" role="status">
						<span class="visually-hidden">Loading...</span>
					</div>
				</div>
			</template>
		</div>
	    <div class="d-flex flex-column align-items-center">
	        @foreach($accounts as $account)
	            <div class="m-2">
	                <button class="btn btn-primary w-100"
                        @click="connectWaba('{{ encryptSodium($account->id) }}')"
	                >
	                    <strong>Connect</strong> {{ $account->waba_phone }}
	                </button>
	            </div>
	        @endforeach
	        <div class="m-2">
               <a href="{{ env('APP_URL').'?scope=conversation&inboxRedirectUrl='.urlencode($inboxRedirectUrl) }}" class="btn btn-primary w-100">
                    <strong>Connect New Number</strong>
                </a>
	        </div>
	    </div>
	</section>

	<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
	<script src="//unpkg.com/alpinejs" defer></script>
	<script type="text/javascript">
		window.axios.defaults.headers.common = {
			"mode": "no-cors",
			"Content-Type": "application/json",
			"X-CSRF-TOKEN" : document.querySelector('meta[name="csrf-token"]').getAttribute("content")
		};

		function data() {
			return {
				loading: false,
				failed: "",
				success: "",
				appUrl: '<?=env('APP_URL')?>',
				portalId: "{{$input['portalId']}}",
				inboxId: "{{$input['inboxId']}}",
				channelId: "{{$input['channelId']}}",
				accountToken: "{{$input['accountToken']}}",
				redirectUrl: "{{urldecode($input['redirectUrl'])}}",
				resetMessage() {
					var self = this;
					setTimeout(function() {
						self.failed  = "";
						self.success  = "";
					}, 2000)
				},
				connectWaba(accountId) {
				    let data = {
				        accountId,
				        channelId: this.channelId,
				        inboxId: this.inboxId,
				        accountToken: this.accountToken,
				    };

				    this.loading = true;
				    let url = this.appUrl + '/api/hubspot/conversation/save';

				    axios.post(url, data)
				        .then(response => {
				            this.loading = false;
				            let res = response.data;
				            if (!res.ok) {
				                this.failed = res.message;
				            } else {
				                window.location.assign(this.redirectUrl);
				            }
				        })
				        .catch(error => {
				            this.loading = false;
				            console.log(error);
				        });
				}


			}
		}


	</script>

@endsection('content')
