import _ from 'lodash';
import axios from 'axios'
import * as bootstrap from 'bootstrap'
import * as popper from '@popperjs/core'
import jquery from 'jquery'


try {
	window._ = _;
	window.$ = window.jQuery = jquery;
	window.Popper = popper;
	window.bootstrap = bootstrap
} catch (exception) {
	console.error(exception);
}

/**
 * We'll load the axios HTTP library which allows us to easily issue requests
 * to our Laravel back-end. This library automatically handles sending the
 * CSRF token as a header based on the value of the "XSRF" token cookie.
 */

window.axios = axios

window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

/**
 * Echo exposes an expressive API for subscribing to channels and listening
 * for events that are broadcast by Laravel. Echo and event broadcasting
 * allows your team to easily build robust real-time web applications.
 */

// import Echo from 'laravel-echo';

// window.Pusher = require('pusher-js');

// window.Echo = new Echo({
//     broadcaster: 'pusher',
//     key: process.env.MIX_PUSHER_APP_KEY,
//     wsHost: process.env.MIX_PUSHER_HOST,
//     wsPort: process.env.MIX_PUSHER_PORT,
//     forceTLS: false,
//     encrypted: true,
//     disableStats: true,
//     enabledTransports: ['ws', 'wss'],
//     authEndpoint: process.env.MIX_APP_URL + '/pusher/auth'
// });

// window.Echo = Echo;
// window.Pusher = Pusher;
// window.EchoCreds = {
//     key: process.env.MIX_PUSHER_APP_KEY,
//     wsHost: process.env.MIX_PUSHER_HOST,
//     wsPort: process.env.MIX_PUSHER_PORT,
//  authEndpoint: process.env.MIX_APP_URL + '/pusher/auth',
// }
