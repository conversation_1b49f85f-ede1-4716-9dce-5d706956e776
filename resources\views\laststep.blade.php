@extends('layouts.new')
@section('title')
	{{$title}}
@endsection

@section('content')
	<style type="text/css">
		.acontent p, .acontent label {
			color: #516f90;
			font-size: 18px;
		}
		.consent-check {
			font-size: 14px !important;
			line-height: 1.5em;
			margin-left: 10px;
		}
		.modal-header {
			min-height: 250px;
		    background-image: url(/img/success-bg.png);
		    background-size: cover;
		    background-repeat: no-repeat;
		}
		.modal p {
			font-size: 18px;
		}
	</style>
	<header>
	    <div class="container">
	        <div class="logo_main">
	            <a href="/">
	            	<img src="/img/niswey-logo.png" width="60" alt="niswey-logo" />
	            </a>
	        </div>
	    </div>
	</header>

	<section id="content" class="container">
	    <div class="flex-container">
	        <div class="leftcontent">
	            <h1>{{env('APP_NAME')}}: Create the buzz around your business</h1>
	            <p class="paratext">
	            	Integrate your WhatsApp Business Accounts (WABA) with HubSpot. Send out WhatsApp marketing campaigns to your HubSpot contacts lists, track all WhatsApp conversations in HubSpot, and do it all on the free HubSpot plan or enterprise, or anything in between
				</p>
	        </div>
	        <div class="rightcontent">
	        	<img src="/img/authorize.png" alt="{{env('APP_NAME')}}" />
	        </div>
	    </div>
	</section>
	<section class="container margin_top pb-5">
	    <div class="custom-progress-bar">
	        <div class="custom-progress-step  custom-progress-step-active custom-progress-step-complete" data-title="AUTHORIZE"></div>
	        <div class="custom-progress-connector active"></div>
	        <div class="custom-progress-step custom-progress-step-active" data-title="WHATSAPP CREDENTIALS"></div>
	        <div class="custom-progress-connector"></div>
	        <div class="custom-progress-step" data-title="Register"></div>
	    </div>
	    <div class="accwrap">
	        <button class="accordion disabled completed">Authorize your HubSpot portal</button>
	        <button class="accordion disabled completed">Connect your WhatsApp Business account</button>
	        <button class="accordion active">Registration</button>
	        <div class="panel active" x-data="data()">
	            <div class="panelbox">
	            	<div class="acontent">
	            		<div class="container-sm py-2">
		            		<template x-if="failed">
								<div class="text-center">
									<div class="alert alert-danger" role="alert" x-text="failed"></div>
								</div>
							</template>
							<template x-if="success">
								<div class="text-center">
									<div class="alert alert-success" role="alert" x-text="success"></div>
								</div>
							</template>
							<template x-if="loading">
								<div class="text-center">
									<div class="spinner-border text-primary" role="status">
										<span class="visually-hidden">Loading...</span>
									</div>
								</div>
							</template>
	            		</div>

	            		<div class="portal-info mb-4 mt-2">
							<div class="d-flex">
								<div class="mr-2">
									<strong>Register your number</strong>
								</div>
							</div>
						</div>

						<p>
							<div>
								If your verified business phone number already has two-step verification enabled, set this value to your number's 6-digit two-step verification PIN. If you cannot recall your PIN, you can update it.
							</div>
							<br />
							<div>
								If your verified business phone number does not have two-step verification enabled, set this value to a 6-digit number. This will be the newly verified business phone number's two-step verification PIN.
							</div>
							<div>
								read more <a href="https://developers.facebook.com/docs/whatsapp/cloud-api/reference/registration/" target="_blank">here</a>
							</div>
							<div class="row">
								<div class="col-md-6">
									<form class="mt-3" @submit.prevent="register($event)">
										@csrf
										<div class="form-group mb-3">
											<label class="form-label" for="2pin">PIN</label>
											<input required type="number" class="form-control" id="2pin" name="pin">
										</div>
										<input type="hidden" name="portalId" :x-model="portalId" />
										<input type="hidden" name="wabaPhoneId" :x-model="wabaPhoneId" />
										<div class="form-group text-center">
											<input :disabled="connected" type="submit" class="btn btn-primary" value="Register">
										</div>
									</form>
								</div>
							</div>
						</p>
	            	</div>
	            </div>
	        </div>
	    </div>

		<!-- Modal -->
		<div class="modal fade" id="connectedModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
		    <div class="modal-dialog modal-lg modal-dialog-centered">
		        <div class="modal-content">
		            {{-- <div class="modal-header"></div> --}}
		            <div class="modal-body p-0 m-0">
		            	<img src="/img/success-bg-2.png">
		            </div>
		        </div>
		    </div>
		</div>

	</section>

	<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
	<script src="//unpkg.com/alpinejs" defer></script>
	<script type="text/javascript">
		window.onload = () => {
			let activeAccordion = document.querySelector('.accordion.active');
			activeAccordion && activeAccordion.scrollIntoView({ block: 'start',  behavior: 'smooth' });
		}

		window.axios.defaults.headers.common = {
			"mode": "no-cors",
			"Content-Type": "application/json",
			"X-CSRF-TOKEN" : document.querySelector('meta[name="csrf-token"]').getAttribute("content")
		};

		function data() {
			return {
				loading: false,
				connected: false,
				failed: "",
				success: "",
				portalId: "{{$portalId}}",
				wabaPhoneId: "{{$wabaPhoneId}}",
				appUrl: '<?=env('APP_URL')?>',

				resetMessage() {
					var self = this;
					setTimeout(function() {
						self.failed  = "";
						self.success  = "";
					}, 2000)
				},

				register(event) {
					let pin = event.target.querySelector('input[name="pin"]').value;
					let data = {
						pin: pin,
						portalId: this.portalId,
						wabaPhoneId: this.wabaPhoneId,
					};

					this.loading = true;
					var self = this;
					let url = self.appUrl+'/api/facebook/register';
					axios.post(url, data)
					.then(function (response) {
						self.loading = false;
						let res = response.data;
						if(!res.ok) {
							self.failed = res.message;
						} else {
							console.log("success");
                            self.connected = true;

                            // Retrieve the inboxRedirectUrl from localStorage
                            let inboxRedirectUrl = localStorage.getItem('inboxRedirectUrl');

                            if (inboxRedirectUrl) {
                                console.log("Redirecting to:", inboxRedirectUrl);

                                // Remove the URL from localStorage after retrieving it
                                localStorage.removeItem('inboxRedirectUrl');

                                // Open the URL in the same window
                                window.location.href = inboxRedirectUrl;
                            } else {
                                const connectedModal = new bootstrap.Modal('#connectedModal', {})
                                connectedModal.show();
                            }
						}
						self.resetMessage();
				    })
				    .catch(function (error) {
				    	this.loading = false;
				        console.log(error);
				    });
				},

				checkConsentAccepted(event) {
					this.disableRegister = !event.currentTarget.checked;
				},

				checkConsentProperty(event) {
					if(this.hasConsentValue == 'yes') {
						this.showPropertyCreateMessage = false;
						this.hasConsentProperty = true;
					}

					if(this.hasConsentValue == 'no') {
						this.hasConsentProperty = false;
						this.showPropertyCreateMessage = true;
						this.consentPropertyName = null;
					}
					console.log("here");
					console.log(this.hasConsentValue)
				},

				whatsAppSignup(event) {
					let data = {
						portalId: this.portalId,
						consentProperty: this.hasConsentProperty ? this.consentPropertyName : null,
					}
					launchWhatsAppSignup(data, this.fbAuthUrl);
				}

				// functions ends
			}
		}

		function launchWhatsAppSignup(data = null, fbAuthUrl) {
			let state = '&state='+encodeURIComponent(JSON.stringify(data));
			let url = fbAuthUrl+state;

			var width = 650;
			var height = 1063;
			var left = (window.innerWidth - width) / 2;
			var top = (window.innerHeight - height) / 2;

			// Ensure the top position is at the middle
			if (top < 0) {
			    top = 0;
			}
			var features = 'width=' + width + ',height=' + height + ',top=' + top + ',left=' + left + ',resizable=yes,scrollbars=yes,toolbar=no,menubar=no,location=no,directories=no,status=no';

			// Open the popup window
			var popup = window.open(url, 'Facebook Authentication', features);

			// Focus the window if it's already opened
			if (window.focus) {
			    popup.focus();
			}
		}

		function processChildMessage(json) {
			let data = JSON.parse(json);
			if(data.success) {
				window.location.href = '<?=env('APP_URL')?>'+'/success?portalId='+data.portalId+'&waba_phone_id='+data.wabaPhoneId
			}
		}
	</script>

@endsection('content')
