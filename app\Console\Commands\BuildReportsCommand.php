<?php

namespace App\Console\Commands;

use App\Models\HubList;
use App\Models\Campaign;
use App\Models\HsObject;
use Illuminate\Console\Command;

class BuildReportsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'build:reports';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Build reports stats';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->print('started');
        $countAsId = 'COUNT(id) as count';

        $listCampaigns = HubList::select('id')->get();
        foreach ($listCampaigns as $listCampaign) {
            $this->print('Running campaign: '.$listCampaign->id);
            $sent = Campaign::selectRaw($countAsId)
                ->where('campaign_id', $listCampaign->id)
                ->where('status', '!=', 'failed')
                ->first();
            $failed = Campaign::selectRaw($countAsId)
                ->where('campaign_id', $listCampaign->id)
                ->where('status', '=', 'failed')
                ->first();
            $delivered = Campaign::selectRaw($countAsId)
                ->where('campaign_id', $listCampaign->id)
                ->where('status', '=', 'delivered')
                ->first();
            $read = Campaign::selectRaw($countAsId)
                ->where('campaign_id', $listCampaign->id)
                ->where('status', '=', 'read')
                ->first();

            $contactOpportunities = HsObject::selectRaw($countAsId)
                ->where('campaign_id', $listCampaign->id)
                ->where('object_type', '=', 'contact')
                ->where('property_name', '=', 'opportunity')
                ->first();
            $contactCustomers = HsObject::selectRaw($countAsId)
                ->where('campaign_id', $listCampaign->id)
                ->where('object_type', '=', 'contact')
                ->where('property_name', '=', 'customer')
                ->first();
            $dealCreated = HsObject::selectRaw($countAsId)
                ->where('campaign_id', $listCampaign->id)
                ->where('object_type', '=', 'deal')
                ->first();
            $dealWon = HsObject::selectRaw($countAsId)
                ->where('campaign_id', $listCampaign->id)
                ->where('object_type', '=', 'deal')
                ->where('won', 1)
                ->first();
            // deals
            $listCampaign->deal_won = $dealWon->count;
            $listCampaign->deal_created = $dealCreated->count;

            // contacts
            $listCampaign->sent = $sent->count;
            $listCampaign->viewed = $read->count;
            $listCampaign->failed = $failed->count;
            $listCampaign->delivered = $delivered->count + $read->count;
            $listCampaign->contact_opportunity = $contactOpportunities->count;
            $listCampaign->contact_customer = $contactCustomers->count;
            $listCampaign->save();
        }
    }

    public function print($message)
    {
        echo $message.PHP_EOL;
    }
}
