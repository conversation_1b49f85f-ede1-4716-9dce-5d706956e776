<?php

namespace App\Services\Whatsapp;

use Log;
use Exception;
use App\Helpers\Func;
use App\Helpers\Waba;
use App\Models\Dialog;
use App\Models\Setting;
use App\Hubspot\Hubspot;
use App\Models\Campaign;
use App\Models\Workflow;
use App\Models\Blacklist;
use App\Helpers\HelperTrait;
use App\Jobs\AiAssistantJob;
use App\Jobs\InboxPublishJob;
use App\Jobs\Pusher\StatusPusherJob;
use App\Jobs\Pusher\MessagePusherJob;
use App\Jobs\UpdateHsMessageStatusJob;

class EventService
{
    use HelperTrait;

    protected $waba;

    protected $account;

    protected $event;

    protected $hsApp;

    protected $dialog;

    protected $message;

    protected $requestId;

    protected $properties = [];

    protected $skipZeroPortals = [7222284, ********, ********];

    protected $aiAssistantPhone = [
        ************,
        ************,
        ************,
        ************,
        ************,
        ************,
    ];

    public function __construct(Waba $waba)
    {
        $this->waba = $waba;
        $this->account = $this->waba->account;
        $this->requestId = $this->waba->requestId;
        $this->hsApp = new Hubspot($this->account->portal_id, $this->requestId);
    }

    public function __invoke($event)
    {
        $this->event = $event;
        if ($event instanceof \Netflie\WhatsAppCloudApi\WebHook\Notification\Unknown) {
            Log::info("[EventService:invoke] $this->requestId, Unknown payload: ".json_encode($this->event));
        } elseif ($event instanceof \Netflie\WhatsAppCloudApi\WebHook\Notification\MessageNotification) {
            if ($event instanceof \Netflie\WhatsAppCloudApi\WebHook\Notification\Reaction) {
                Log::info("[EventService:invoke] $this->requestId, Reaction payload: ".json_encode($this->event));
            } else {
                $this->messageEvent();
            }
        } elseif ($event instanceof \Netflie\WhatsAppCloudApi\WebHook\Notification\StatusNotification) {
            $this->statusEvent();
        } elseif ($event instanceof \Netflie\WhatsAppCloudApi\WebHook\Notification\PhoneNotification) {
            $this->phoneEvent();
        } elseif ($event instanceof \Netflie\WhatsAppCloudApi\WebHook\Notification\TemplateNotification) {
            $this->templateEvent();
        }
    }

    private function phoneEvent()
    {
        try {
            match (true) {
                $this->event->getName() instanceof \Netflie\WhatsAppCloudApi\WebHook\Notification\Phone\Name => $this->updatePhoneName(),
                default => Log::warning("[EventService:nameEvent] $this->requestId, Unknown phone event type")
            };
        } catch (Exception $e) {
            $trace = $e->getTraceAsString();
            Log::error("[EventService:nameEvent] $this->requestId, Message: ".$e->getMessage().', trace: '.$trace);
        }
    }

    private function updatePhoneName()
    {
        if ($this->event->decision() == 'APPROVED') {
            $this->account->waba_display_name = $this->event->verifiedName();
            $this->account->save();
            Log::info("[EventService:updatePhoneName] $this->requestId, display name updated");
        } else {
            Log::info("[EventService:updatePhoneName] $this->requestId, display name not approved.");
        }
    }

    private function templateEvent()
    {
        try {
            match (true) {
                $this->event->getStatus() instanceof \Netflie\WhatsAppCloudApi\WebHook\Notification\Template\Status => $this->updateTemplateStatus(),
                default => Log::warning("[EventService:nameEvent] $this->requestId, Unknown phone event type")
            };
        } catch (Exception $e) {
            $trace = $e->getTraceAsString();
            Log::error("[EventService:nameEvent] $this->requestId, Message: ".$e->getMessage().', trace: '.$trace);
        }
    }

    private function updateTemplateStatus()
    {
        $this->waba->getTemplateStore()->clearCache();
    }

    private function messageEvent()
    {
        if ($this->isBlacklisted()) {
            Log::info("[EventService:messageEvent] $this->requestId, Number is blacklisted");

            return false;
        }

        $event = new MessageEvent($this->event, $this->account, $this->requestId);

        try {
            if ($event->isMedia()) {
                $event->handleMedia($this->waba->getMedia($this->event, $this->hsApp));
            } elseif ($event->isInteractive()) {
                $this->properties = $event->handleInteractive();
            } else {
                if ($this->event instanceof \Netflie\WhatsAppCloudApi\WebHook\Notification\Button) {
                    $event->message()->body = $this->event->text();
                    $event->message()->reply_id = $this->event->replyingToMessageId();
                    $this->updateCampaignReply($event);
                } else {
                    $event->message()->body = $this->event->message();
                }
            }

            $this->dialog = $this->saveDialog($event);

            $event->message()->save();

            $this->dispatchLiveMessage($event);
            if ($this->account->hs_channel_id) {
                $this->dispatchInboxMessage($this->waba, $event->message());
            }

            // search users
            $phone = $event->message()->chatId;
            $users = $this->searchPhone($phone);
            if (! $users) {
                $users = [$this->createUser($phone)];
            }
            if($users) {
	            $this->updateFlowProperties($users);
            }

            // update object id to dialog table
            $this->dialog->object_id = $users[0]->id ?? null;
            $this->dialog->save();

            // if ($this->account->waba_phone == '************' && in_array($event->message()->chatId, $this->aiAssistantPhone)) {
            //     dispatch(new AiAssistantJob(
            //         $this->account->toArray(),
            //         $event->message()->chatId,
            //         $event->message()->body,
            //         $users[0]->id ?? null
            //     ))->onQueue('wabad');
            // }

            $this->updateTimeline($event, $users);
            Log::info("[EventService:messageEvent] Success for $this->requestId");
        } catch (Exception $e) {
            $trace = $e->getTraceAsString();
            Log::error("[EventService:messageEvent] $this->requestId, Message: ".$e->getMessage().', trace: '.$trace);

            return false;
        }
    }

    private function statusEvent()
    {
        $event = new StatusEvent($this->event, $this->requestId);

        try {
            $event->updateMessage();
            if (! $event->message()) {
                throw new Exception('Message not found', 1); // NOSONAR
            }

            // live update status
            if ($event->message()->status != 'sent') {
                dispatch(new StatusPusherJob(
                    $this->account->portal_id.'.'.$this->account->waba_phone,
                    $event->message(),
                    $this->requestId
                ))->onQueue('wabad');
            }

            if (in_array($event->message()->status, ['read', 'failed']) && $this->account->hs_channel_id) {
                dispatch(new UpdateHsMessageStatusJob(
                    (int) $this->account->hs_channel_id,
                    $event->message()->id,
                    $this->requestId,
                    $event->message()->status,
                    $event->message()->status_reason
                ))->onQueue('wabad');
            }

            $this->updateCampaign($event);
            $this->updateWorkflow($event);

            $status = $event->message()->status;
            $allowedStatuses = ['failed', 'delivered'];
            if (! in_array($status, $allowedStatuses)) {
                Log::info("[EventService:statusEvent] $this->requestId, Skipping: ".$status);

                return;
            }

            // update timeline
            $events = [];
            $users = $this->searchPhone($this->event->customerId());
            foreach ($users as $user) {
                $events[] = [
                    'id' => count($users) > 1 ? $event->message()->id.$status.$user->id : $event->message()->id.$status,
                    'objectId' => $user->id,
                    'data' => [
                        'status' => $status,
                        'phone' => $this->account->waba_phone,
                        'message' => Func::messageToTimeline($event->message()),
                    ],
                ];
            }

            if (! $events) {
                throw new Exception('No event found to update', 1); // NOSONAR
            }

            if (count($events) > 1) {
                $this->hsApp->timeline()->updateBatch($events, true);
            } else {
                $this->hsApp->timeline()->update($events[0], true);
            }
            // $this->hsApp->legacy()->updateStatusTimeline($this->account->portal_id, $events);
            Log::info("[EventService:statusEvent] Success for $this->requestId");
        } catch (Exception $e) {
            $trace = $e->getTraceAsString();
            Log::error("[EventService:statusEvent] $this->requestId, Message: ".$e->getMessage().', trace: '.$trace);
        }
    }

    private function updateCampaign($event)
    {
        Campaign::where(['message_id' => $event->message()->id, 'account_id' => $this->account->id])
            ->update([
                'status' => $event->message()->status,
                'status_reason' => $event->message()->status_reason,
            ]);
    }

    private function updateCampaignReply($event)
    {
        Campaign::where(['message_id' => $event->message()->reply_id, 'account_id' => $this->account->id])
            ->update([
                'reply_message' => $event->message()->body ?? null,
            ]);
    }

    private function updateWorkflow($event)
    {
        Workflow::where(['messageId' => $event->message()->id, 'account_id' => $this->account->id])
            ->update([
                'status' => $event->message()->status,
                'status_reason' => $event->message()->status_reason,
            ]);
    }

    private function isBlacklisted()
    {
        return Blacklist::where([
            'account_id' => $this->account->id,
            'phone' => $this->event->customer()->phoneNumber(),
        ])->first();
    }

    private function updateTimeline($event, $users)
    {
        $events = [];

        foreach ($users as $user) {
            $events[] = [
                'id' => $event->message()->id,
                'objectId' => $user->id,
                'data' => [
                    'user' => $event->message()->sender,
                    'phone' => $this->account->waba_phone,
                    'message' => Func::messageToTimeline($event->message()),
                ],
            ];
        }

        Log::info("[EventService:updateTimeline] $this->requestId, events: ".json_encode($events));
        if (count($events) > 1) {
            $this->hsApp->timeline()->updateBatch($events, false);
        } else {
            $this->hsApp->timeline()->update($events[0], false);
        }
    }

    private function updateFlowProperties($users)
    {
    	Log::info("[EventService:updateFlowProperties] {$this->requestId}, properties: ".json_encode($this->properties));
        if (! $this->properties) {
            return [];
        }

        if (count($users) > 1) {
            $this->hsApp->contacts()->updateBatch($users, $this->properties);
        } else {
            $this->hsApp->contacts()->update($users[0]->id, $this->properties);
        }
    }

    private function searchPhone($phone)
    {
        $filters = [];
        $nationalNumber = Func::getNationalNumber($phone);
        Log::info("[EventService:searchPhone] $this->requestId, nationalNumber: $nationalNumber");
        $phoneCountryCode = Func::findCountryCode($phone);
        Log::info("[EventService:searchPhone] $this->requestId, phoneCountryCode: $phoneCountryCode");
        foreach (['phone', 'mobilephone', 'hs_calculated_phone_number'] as $name) {
            $phoneValue = $phone;
            if ($name == 'phone') {
                $phoneCountryCode && $nationalNumber && ($phoneValue = '+'.$phoneCountryCode.'0'.$nationalNumber);
            }
            $filters[] = [
                'operator' => 'CONTAINS_TOKEN',
                'propertyName' => $name,
                'value' => $phoneValue,
            ];
        }
        // skip zero from portals
        // in_array($this->account->portal_id, $this->skipZeroPortals)
        $phoneProps = ['phone', 'hs_searchable_calculated_phone_number'];
        foreach ($phoneProps as $name) {
            $phoneValue = $phone;
            if ($name == 'phone' && ! $phoneCountryCode) {
                $phoneValue = '0' + $phone;
            }

            if ($name == 'hs_searchable_calculated_phone_number') {
                $phoneValue = $nationalNumber;
            }

            $filters[] = [
                'operator' => 'CONTAINS_TOKEN',
                'propertyName' => $name,
                'value' => $phoneValue,
            ];
        }
        $response = $this->hsApp->contacts()->search([
            'filters' => $filters,
            'properties' => ['firstname', 'lastname', 'email', 'hubspot_owner_id'],
        ]);

        return $response->results ?? [];
    }

    private function createUser($phone)
    {
        $setting = Setting::where(['account_id' => $this->account->id])->first();
        if ($setting && ! $setting->create_user) {
            throw new Exception('User has turned off new user contact creation', 1); // NOSONAR
        }

        // $time = strtotime($this->dialog->created_at);
        // if ((time() - $time) < 600) {
        //     throw new Exception("Avoiding creating duplicate", 1); // NOSONAR
        // }

        $prepareContactProps = $this->prepareContact($phone, preg_replace('/[^\p{L}\p{N}\s]/u', '', $this->event->customer()->name()));
        $prepareContactProps = array_unique(array_merge($this->properties, $prepareContactProps));
        $user = $this->hsApp->contacts()->create($prepareContactProps);
        if (! $user) {
            throw new Exception('Unable to create contact', 1); // NOSONAR
        }

        return $user;
    }

    private function saveDialog($messageEvent)
    {
        return Dialog::updateOrCreate(
            ['account_id' => $this->account->id, 'chatId' => $messageEvent->message()->chatId],
            [
                'phone' => $messageEvent->message()->from,
                'time' => $messageEvent->message()->time,
                'name' => $messageEvent->message()->sender,
            ]
        );
    }

    private function dispatchLiveMessage($messageEvent)
    {
        $liveMessage = $messageEvent->message();
        $liveMessage->name = $liveMessage->sender;
        $liveMessage->phone = $liveMessage->from;
        if ($messageEvent->isMedia()) {
            $liveMessage->files = [$messageEvent->getFile()];
        }
        dispatch(new MessagePusherJob(
            $this->account->portal_id.'.'.$this->account->waba_phone,
            $liveMessage->toArray(),
            $this->requestId
        ))->onQueue('wabad');
    }

    private function dispatchInboxMessage($waba, $message)
    {
        Log::info("[EventService:dispatchInboxMessage] $this->requestId, account: ".json_encode($this->account->toArray()));
        dispatch(new InboxPublishJob(
            (object) $this->account->toArray(),
            (object) $message->toArray(),
            $this->requestId
        ))->onQueue('wabad');
    }
}
