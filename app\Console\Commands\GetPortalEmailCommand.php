<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Hubspot\Hubspot;
use App\Models\PortalToken;
use Illuminate\Console\Command;

class GetPortalEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'portal:get-emails';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get email from access token and save it in DB';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Fetch all portal tokens
        $portals = PortalToken::all();

        if ($portals->isEmpty()) {
            $this->info('No portal tokens found.');

            return;
        }

        $this->info('Processing portal tokens...');

        foreach ($portals as $portal) {
            try {
                $this->info("Processing portal ID: {$portal->portal_id}");

                // Initialize Hubspot helper with portal ID
                $hubspot = new Hubspot($portal->portal_id, uniqid());

                // Fetch user details from HubSpot
                $userInfo = $hubspot->oauth()->get($hubspot->getAccessToken());

                if (! $userInfo) {
                    $this->error("Failed to fetch user info for portal ID: {$portal->portal_id}");

                    continue;
                }

                // Save the user info to the portal record
                $portal->user = $userInfo->user;
                $portal->save();

                $this->info("Successfully updated user info for portal ID: {$portal->portal_id}");
            } catch (Exception $e) {
                Log::error("Error processing portal ID: {$portal->portal_id}, Exception: ".$e->getMessage());
                $this->error("An error occurred while processing portal ID: {$portal->portal_id}. Check logs for details.");
            }
        }

        $this->info('Portal tokens processing completed.');
    }
}
