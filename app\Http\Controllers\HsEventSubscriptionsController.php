<?php

namespace App\Http\Controllers;

use Log;
use Illuminate\Http\Request;
use App\Jobs\HubspotWebhookJob;

class HsEventSubscriptionsController extends Controller
{
    public function __construct()
    {
        parent::__construct('waba');
    }

    public function webhook(Request $request)
    {
        $events = $request->input();
        Log::info("[HsEventSubscriptionsController:webhook] $this->requestId, payload: ".json_encode($events));

        foreach ($events as $event) {
            $source = $event['changeSource'] ?? null;
            if ($source == 'IMPORT') {
                continue;
            }
            HubspotWebhookJob::dispatch($event, $this->requestId)->onQueue('wabah');
        }

        return response()->json(['status' => 'queued']);
    }
}
