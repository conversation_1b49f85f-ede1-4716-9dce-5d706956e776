<?php

namespace App\Console\Commands;

use DB;
use Log;
use Exception;
use App\Models\Account;
use App\Hubspot\Hubspot;
use App\Models\Workflow;
use App\Models\WorkflowReport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class WorkflowReportsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:reports';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate workflow reports statistics by processing only changed workflows';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Started workflow report generation');

        try {
            $accounts = Account::select('id', 'portal_id')->where('paid', '!=', 0)->get();

            foreach ($accounts as $account) {
                $this->updateWorkflowReport($account->id, $account->portal_id);
            }

            $this->info('Workflow report generation completed');

            return 0;
        } catch (Exception $e) {
            $this->error('Error: '.$e->getMessage());
            Log::error('[WorkflowReportsCommand] '.$e->getMessage());

            return 1;
        }
    }

    /**
     * Update workflow reports for an account
     */
    public function updateWorkflowReport($accountId, $portalId)
    {
        $this->info("Processing changed workflows for account {$accountId}");

        // Current timestamp for updating the last processed time
        $currentTimestamp = now();

        // Get workflows that need processing based on last update time
        $workflowQuery = DB::table('workflows as w')
            ->select('w.workflowId')
            ->where('w.account_id', $accountId)
            ->leftJoin('workflow_reports as wr', function ($join) use ($accountId) {
                $join->on('w.workflowId', '=', 'wr.id')
                    ->where('wr.account_id', '=', $accountId);
            })
            ->where(function ($query) {
                $query->whereNull('wr.last_processed_at') // New workflows never processed
                    ->orWhere('w.updated_at', '>', DB::raw('wr.last_processed_at')); // Changed workflows
            });

        // Get unique workflow IDs that need processing
        $workflowIds = $workflowQuery->distinct()
            ->pluck('workflowId')
            ->toArray();

        if (empty($workflowIds)) {
            $this->info("No workflows to process for account {$accountId}");

            return;
        }

        $this->info('Processing '.count($workflowIds)." workflows for account {$accountId}");

        // Process in batches for memory efficiency
        foreach (array_chunk($workflowIds, 100) as $batch) {
            $this->processWorkflowBatch($batch, $accountId, $portalId, $currentTimestamp);
        }
    }

    /**
     * Process a batch of workflows efficiently
     */
    protected function processWorkflowBatch(array $workflowIds, $accountId, $portalId, $processedTimestamp = null)
    {
        // Get all workflow stats in one query
        $stats = DB::table('workflows')
            ->select(DB::raw('workflowId, status, COUNT(*) as count'))
            ->whereIn('workflowId', $workflowIds)
            ->where('account_id', $accountId)
            ->whereIn('status', ['sent', 'failed', 'delivered', 'read'])
            ->groupBy('workflowId', 'status')
            ->get();
        $workflowStats = [];
        foreach ($stats as $stat) {
            $workflowStats[$stat->workflowId][$stat->status] = $stat->count;
        }

        // Get contact stats for all workflows in batch
        $contactStats = $this->getContactStatsForBatch($workflowIds);

        // Get deal stats for all workflows in batch
        $dealStats = $this->getDealStatsForBatch($workflowIds);

        $apiNames = $this->fetchWorkFlowDataBatch($workflowIds, $portalId);

        // Combine database names with API names, prioritizing database names
        $workflowNames = [];
        foreach ($apiNames as $workflowId => $name) {
            $workflowNames[$workflowId] = $name;
        }

        // Create/update reports for each workflow
        foreach ($workflowIds as $workflowId) {
            $stats = isset($workflowStats[$workflowId]) ? $workflowStats[$workflowId] : [];

            // Default values if no records found
            $sent = $stats['sent'] ?? 0;
            $failed = $stats['failed'] ?? 0;
            $delivered = $stats['delivered'] ?? 0;
            $read = $stats['read'] ?? 0;

            $reportData = [
                'name' => $workflowNames[$workflowId] ?? "Workflow #$workflowId",
                'account_id' => $accountId,
                'portal_id' => $portalId,
                'sent' => $sent + $delivered + $read,
                'failed' => $failed,
                'viewed' => $read,
                'delivered' => $delivered + $read,
                'deal_won' => $dealStats[$workflowId]['won'] ?? 0,
                'deal_created' => $dealStats[$workflowId]['created'] ?? 0,
                'contact_opportunity' => $contactStats[$workflowId]['opportunity'] ?? 0,
                'contact_customer' => $contactStats[$workflowId]['customer'] ?? 0,
            ];

            // Update or create the report, and update the last_processed_at timestamp
            $reportData['last_processed_at'] = $processedTimestamp ?? now();

            WorkflowReport::updateOrCreate(
                ['id' => $workflowId, 'account_id' => $accountId],
                $reportData
            );
        }
    }

    /**
     * Get workflow names from the database
     */
    protected function getWorkflowNamesFromDB(array $workflowIds, $accountId)
    {
        $results = [];

        $names = DB::table('workflow_reports')
            ->select('id', 'name')
            ->whereIn('id', $workflowIds)
            ->where('account_id', $accountId)
            ->whereNotNull('name')
            ->groupBy('id', 'name')
            ->get();

        foreach ($names as $name) {
            $results[$name->workflowId] = $name->name;
        }

        return $results;
    }

    /**
     * Get contact statistics for a batch of workflows
     */
    protected function getContactStatsForBatch(array $workflowIds)
    {
        $results = [];

        // Initialize with default values
        foreach ($workflowIds as $workflowId) {
            $results[$workflowId] = [
                'opportunity' => 0,
                'customer' => 0,
            ];
        }

        $stats = DB::table('hs_objects')
            ->select(DB::raw('campaign_id, property_name, COUNT(*) as count'))
            ->whereIn('campaign_id', $workflowIds)
            ->where('object_type', 'contact')
            ->whereIn('property_name', ['opportunity', 'customer'])
            ->groupBy('campaign_id', 'property_name')
            ->get();

        foreach ($stats as $stat) {
            $results[$stat->campaign_id][$stat->property_name] = $stat->count;
        }

        return $results;
    }

    /**
     * Get deal statistics for a batch of workflows
     */
    protected function getDealStatsForBatch(array $workflowIds)
    {
        $results = [];

        // Initialize with default values
        foreach ($workflowIds as $workflowId) {
            $results[$workflowId] = [
                'created' => 0,
                'won' => 0,
            ];
        }

        // Get all deals created
        $dealStats = DB::table('hs_objects')
            ->select(DB::raw('campaign_id, COUNT(*) as count'))
            ->whereIn('campaign_id', $workflowIds)
            ->where('object_type', 'deal')
            ->groupBy('campaign_id')
            ->get();

        foreach ($dealStats as $stat) {
            $results[$stat->campaign_id]['created'] = $stat->count;
        }

        // Get won deals
        $wonDealStats = DB::table('hs_objects')
            ->select(DB::raw('campaign_id, COUNT(*) as count'))
            ->whereIn('campaign_id', $workflowIds)
            ->where('object_type', 'deal')
            ->where('won', 1)
            ->groupBy('campaign_id')
            ->get();

        foreach ($wonDealStats as $stat) {
            $results[$stat->campaign_id]['won'] = $stat->count;
        }

        return $results;
    }

    /**
     * Fetch multiple workflow data from HubSpot API using the batch endpoint
     */
    public function fetchWorkFlowDataBatch(array $workflowIds, $portalId)
    {
        $results = [];

        // Set default fallback names first
        foreach ($workflowIds as $workflowId) {
            $results[$workflowId] = "Workflow #$workflowId";
        }

        if (empty($workflowIds)) {
            return $results;
        }

        try {
            $hubspot = new Hubspot($portalId, uniqid());

            // Prepare the batch request body
            $requestBody = [
                'inputs' => array_map(function ($id) {
                    return [
                        'type' => 'FLOW_ID',
                        'flowId' => (string) $id,
                    ];
                }, $workflowIds),
            ];

            $response = Http::withToken($hubspot->getAccessToken())
                ->post('https://api.hubapi.com/automation/v4/flows/batch/read', $requestBody);

            if ($response->successful()) {
                $data = $response->object();
                // Process the response and extract names
                if (isset($data->results) && is_array($data->results)) {
                    foreach ($data->results as $result) {
                        // Extract the id from the result - based on the sample response
                        $flowId = $result->id ?? null;
                        $name = $result->name ?? null;

                        if ($flowId && $name) {
                            $results[$flowId] = $name;
                        }
                    }
                }
            } else {
                Log::error('[WorkflowReportsCommand] Batch API error: '.$response->body());
            }
        } catch (Exception $e) {
            Log::error('[WorkflowReportsCommand] Error fetching workflows batch: '.$e->getMessage());
        }

        return $results;
    }

    /**
     * Fetch workflow data from HubSpot API (single workflow fallback method)
     */
    public function fetchWorkFlowData($workflowId, $portalId)
    {
        try {
            $hubspot = new Hubspot($portalId, uniqid());
            $response = Http::withToken($hubspot->getAccessToken())
                ->get("https://api.hubapi.com/automation/v4/flows/$workflowId");

            if ($response->successful()) {
                $data = $response->object();

                return $data->name ?? "Workflow #$workflowId";
            } else {
                return "Workflow #$workflowId";
            }
        } catch (Exception $e) {
            Log::info('[WorkflowReportsCommand] Error fetching workflow: '.$e->getMessage());

            return "Workflow #$workflowId";
        }
    }
}
