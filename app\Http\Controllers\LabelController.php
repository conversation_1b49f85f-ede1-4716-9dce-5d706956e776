<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use App\Models\Label;
use App\Models\Dialog;
use Illuminate\Http\Request;

class LabelController extends Controller
{
    protected $errorMessage = 'Unexpected error';

    public function __construct()
    {
        parent::__construct('waba');
    }

    public function index(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
        ]);

        $accountId = readUserId($input['user_id']);

        try {
            $blacklist = Label::where('account_id', $accountId)->latest()->get();

            return $this->jsonOk(['data' => $blacklist]);
        } catch (Exception $e) {
            Log::error("[LabelController:index] $this->requestId, Exception ".$e->getMessage());

            return $this->jsonError(['message' => $this->errorMessage]);
        }
    }

    public function store(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'name' => 'required',
            'color' => 'required',
        ]);

        $accountId = readUserId($input['user_id']);
        $portalId = readUserId($input['user_id'], 'portalId');

        try {
            $label = Label::create([
                'account_id' => $accountId,
                'portal_id' => $portalId,
                'name' => $input['name'],
                'color' => $input['color'],
            ]);

            return $this->jsonOk(['data' => $label]);
        } catch (Exception $e) {
            Log::error("[LabelController:store] $this->requestId, Exception ".$e->getMessage());

            return $this->jsonError(['message' => $this->errorMessage]);
        }
    }

    public function delete(Request $request, $id)
    {
        $input = $request->validate([
            'user_id' => 'required',
        ]);

        $accountId = readUserId($input['user_id']);

        try {
            Label::where(['id' => $id, 'account_id' => $accountId])->delete();

            return $this->jsonOk();
        } catch (Exception $e) {
            Log::error("[LabelController:delete] $this->requestId, Exception ".$e->getMessage());

            return $this->jsonError(['message' => $this->errorMessage]);
        }
    }

    public function userLabels(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'chatId' => 'required',
        ]);
        $accountId = readUserId($input['user_id']);

        try {
            $labels = Dialog::select('labels')->where(
                [
                    'account_id' => $accountId,
                    'chatId' => $input['chatId']]
            )->first();

            return $this->jsonOk(['labels' => $labels->labels]);
        } catch (Exception $e) {
            Log::error("[LabelController:userLabels] $this->requestId, Exception ".$e->getMessage());

            return $this->jsonError(['message' => $this->errorMessage]);
        }
    }

    public function assignLabel(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'chatId' => 'required',
            'labels' => 'nullable',
        ]);
        $accountId = readUserId($input['user_id']);
        $labels = $input['labels'] ?? null;
        $labels = $input['labels'] ? $input['labels'] : null;

        try {
            Dialog::where([
                'account_id' => $accountId,
                'chatId' => $input['chatId'],
            ])->update(['labels' => $labels]);

            return $this->jsonOk(['message' => 'Successfully updated']);
        } catch (Exception $e) {
            Log::error("[LabelController:assignLabel] $this->requestId, Exception ".$e->getMessage());

            return $this->jsonError(['message' => $this->errorMessage]);
        }
    }
}
