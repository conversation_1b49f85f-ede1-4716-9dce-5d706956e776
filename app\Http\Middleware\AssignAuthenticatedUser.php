<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;


class AssignAuthenticatedUser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            if ($request->bearerToken()) {
                $user = JWTAuth::parseToken()->authenticate();
                $request->attributes->set('auth_user', $user);
            }
        } catch (\Exception $e) {
            $request->attributes->set('auth_user', null);
        }

        return $next($request);

    }
}
