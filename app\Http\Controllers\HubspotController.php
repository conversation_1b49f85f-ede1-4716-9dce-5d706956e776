<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use App\Helpers\Func;
use App\Helpers\Waba;
use App\Models\Account;
use App\Models\HubList;
use App\Models\PortalToken;
use Illuminate\Http\Request;
use App\Models\Subscriptions;
use App\Hubspot\Crm\CrmService;
use App\Jobs\Waba\WhatsAppTextJob;
use App\Jobs\Waba\WhatsAppMediaJob;
use App\Jobs\Waba\WhatsAppMediaProJob;
use App\Services\Whatsapp\TemplateService;

class HubspotController extends Controller
{
    protected $nisweyPortals = [7222284, 2720327];

    protected $naOption = [
        'options' => [
            [
                'label' => 'NA',
                'value' => 'NA',
                'readOnly' => true,
                'description' => 'There are no parameters in this template',
            ],
        ],
    ];

    public function __construct()
    {
        parent::__construct('waba');
    }

    public function auth(Request $request)
    {
        $input = $request->validate([
            'code' => 'required',
        ]);

        $hsApp = $this->hubspotApp;

        try {
            $tokens = $hsApp->createTokens($input['code']);
            if (! isset($tokens->access_token)) {
                throw new Exception('Code is expired please try again', 1); // NOSONAR
            }

            $portal = $hsApp->getPortalId($tokens->access_token);
            if (! isset($portal->portalId)) {
                throw new Exception('There was an error, please try again', 1); // NOSONAR
            }

            // check if there is a subscription for this portal
            $subsription = Subscriptions::where('portal_id', $portal->portalId)
                ->where('status', '!=', 'cancelled')->first();

            if (! $subsription && ! in_array($portal->portalId, $this->nisweyPortals)) {
                return view('message', [
                    'message' => "You don't have any Subscriptions. Please Click ".
                    "<a href='https://www.niswey.com/whatsapp-automation-for-hubspot#pricing'>".
                    'Here</a> to start a trial',
                ]);
            }

            PortalToken::updateOrCreate(['portal_id' => $portal->portalId], [
                'refresh_token' => $tokens->refresh_token,
                'access_token' => $tokens->access_token,
                'expires_in' => $tokens->expires_in,
                'auth_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);

            $properties = $this->hubspotApp->forPortal($portal->portalId)->contacts()->getProperties();
            if (isset($properties->results)) {
                $properties = property_consent($properties->results);
            }

            $fbApp = $this->waba->fbApp;

            return view('register', [
                'app' => $this->app,
                'properties' => $properties,
                'portalId' => $portal->portalId,
                'fbVersion' => $fbApp['version'],
                'fbAppId' => $fbApp['client_id'],
                'title' => 'Connect '.$this->app,
                'fbConfigId' => $fbApp['config_id'],
                'fbAuthUrl' => $this->waba->getAuthUrl(),
            ]);
        } catch (Exception $e) {
            Log::error("[HubspotController:auth] for $this->requestId, Exception: ".$e->getMessage());

            return view('error', ['message' => 'There was an error, please try again']);
        }
    }

    public function crm(Request $request, CrmService $crm)
    {
        $input = $request->input();

        // Parse phone number
        $objectId = $this->hubspotApp->hsApp['config']['objectId'] ?? null;
        $phone = $request->input('phone') ?: $request->input('mobilephone') ?: '';
        $phone = Func::parsePhone($phone);

        // Get response from the service
        $response = $crm->handleRequest($input, $phone, $objectId);

        return response()->json($response);
    }

    public function owners(Request $request)
    {
        $input = $request->validate(['user_id' => 'required']);
        $portalId = readUserId($input['user_id'], 'portalId');
        $owners = $this->hubspotApp->forPortal($portalId)->owners()->getOwners();

        return $this->jsonOk([
            'owners' => $owners,
        ]);
    }

    public function accounts(Request $request)
    {
        $input = $request->input();
        Log::info("[HubspotController:accounts] $this->requestId. payload: ".json_encode($input));

        $options = [];
        $accounts = Account::where('portal_id', $input['portalId'])
            ->where('paid', '!=', 0)
            ->get();

        foreach ($accounts as $account) {
            $from = $account->waba_phone;
            if ($account->waba_name) {
                $from = $account->waba_name.' ('.$account->waba_phone.')';
            }
            $options[] = ['label' => $from, 'value' => $account->id];
        }

        return response()->json(['options' => $options]);
    }

    public function mediaType(Request $request)
    {
        $input = $request->input();
        Log::info("[HubspotController:mediaType] $this->requestId, payload:".json_encode($input));

        $options = [];
        $accountId = $request->input('account.fieldValue.value');
        $templateId = $request->input('template.fieldValue.value');
        if (! $accountId || ! $templateId) {
            return response()->json(['options' => []]);
        }

        $waba = new Waba($accountId, $this->requestId);
        $template = $waba->fetchTemplateById($templateId);
        $template = $this->buildTemplateParams($template);
        if ($template->type == 'media') {
            $options[] = ['label' => 'NA', 'value' => 'NA'];
        } elseif ($template->type == 'buttons' && $template->buttons) {
            $options[] = ['label' => 'Buttons', 'value' => 'buttons'];
        } else {
            $options[] = ['label' => ucfirst($template->type), 'value' => $template->type];
        }
        $data['options'] = $options;

        return response()->json($data);
    }

    public function mediaTypeV1(Request $request)
    {
        $input = $request->input();
        Log::info("[HubspotController:mediaTypeV1] $this->requestId, payload:".json_encode($input));

        $options[] = ['label' => 'NA', 'value' => 'NA'];
        $accountId = $request->input('account.fieldValue.value');
        $templateId = $request->input('template.fieldValue.value');
        if (! $accountId || ! $templateId) {
            return response()->json(['options' => $options]);
        }

        $waba = new Waba($accountId, $this->requestId);
        $template = $waba->fetchTemplateById($templateId);

        $templateService = new TemplateService($template);
        $template = $templateService->analyzeTemplate();

        $allowedTypes = ['image', 'video', 'document', 'location'];
        $templateType = explode('_', $template->type)[0] ?? null;
        if (! in_array($templateType, $allowedTypes)) {
            return response()->json(['options' => $options]);
        }

        $options = []; // reset so we don;t append to already existing NA option
        $options[] = ['label' => ucfirst($templateType), 'value' => strtolower($templateType)];

        return response()->json(['options' => $options]);
    }

    public function buttonType(Request $request)
    {
        $input = $request->input();
        Log::info("[HubspotController:buttonType] $this->requestId, payload:".json_encode($input));

        $options[] = ['label' => 'NA', 'value' => 'NA'];

        $accountId = $request->input('account.fieldValue.value');
        $templateId = $request->input('template.fieldValue.value');
        if (! $accountId || ! $templateId) {
            return response()->json(['options' => $options]);
        }

        $waba = new Waba($accountId, $this->requestId);
        $template = $waba->fetchTemplateById($templateId);

        $templateService = new TemplateService($template);
        $template = $templateService->analyzeTemplate();

        if (! str_starts_with($template->type, 'button')) {
            return response()->json(['options' => $options]);
        }

        $options = []; // reset so we don;t append to already existing NA option
        $options[] = ['label' => 'Buttons', 'value' => $template->type];

        return response()->json(['options' => $options]);
    }

    public function workflowTemplates(Request $request)
    {
        $input = $request->input();
        $type = $input['type'] ?? 'text';
        Log::info("[HubspotController:workflowTemplates] for $this->requestId, payload: ".json_encode($input));

        $accountId = $request->input('account.fieldValue.value');
        if (! $accountId) {
            return response()->json(['options' => []]);
        }

        $waba = new Waba($accountId, $this->requestId);
        $templates = $waba->fetchTemplates();

        $data['options'] = $this->workflowAbleTemplates($templates, $type);

        return response()->json($data);
    }

    public function workflowTemplatesV1(Request $request)
    {
        $input = $request->input();
        $type = $input['type'] ?? 'text';
        Log::info('[HubspotController:workflowTemplatesV1] Request URL: '.$request->fullUrl()." | Request ID: $this->requestId, Payload: ".json_encode($input));

        $accountId = $request->input('account.fieldValue.value');
        Log::info('[HubspotController:workflowTemplatesV1] Request URL: '.$request->fullUrl()." | Request ID: $this->requestId, accountId: ".$accountId);
        if (! $accountId) {
            return response()->json(['options' => [], 'searchable' => true]);
        }

        $filters = [];

        // If search query exists, add it to filters
        if (! empty($request->input('q'))) {
            $filters['name'] = $request->input('q');
            $filters['status'] = 'APPROVED';
        }

        $waba = new Waba($accountId, $this->requestId);
        $templates = $waba->fetchTemplatesWithFilters($filters);

        $filtered = [];
        foreach ($templates->data as $template) {
            $templateService = new TemplateService($template);
            $template = $templateService->analyzeTemplate();
            if ($template->name == 'wa_autoresponse_yes') {
                // print_r($template);
                // die;
            }

            if ($type == 'text' && $template->type != 'text') {
                continue;
            }

            if ($type != 'text' && $template->type == 'text') {
                continue;
            }

            $value = $template->id;
            if ($template->type == 'text') {
                $value = $template->id.'_'.$accountId;
            }
            $filtered[] = ['label' => $template->name, 'value' => $value];
        }

        $data['options'] = $filtered;
        $data['searchable'] = true;

        return response()->json($data);
    }

    public function messageTypes(Request $request)
    {
        $input = $request->input();
        Log::info("[HubspotController:messageTypes] for $this->requestId, payload: ".json_encode($input));
        $messageOptions['options'] = [
            ['label' => 'Template', 'value' => 'template'],
            // ['label' => 'Text Message', 'value' => 'textmessage']
        ];

        return response()->json($messageOptions);
    }

    public function workflowParams(Request $request)
    {
        $input = $request->input();
        Log::info("[HubspotController:workflowParams] for $this->requestId, payload: ".json_encode($input));

        $accountId = $request->input('account.fieldValue.value');
        $templateId = $request->input('template.fieldValue.value');

        // tempfix for issues in "Send WhatsApp Text Message", because hubspot isn't sending inputfields of other filled inputs
        if (! $accountId && strpos($templateId, '_') !== false) {
            [$templateId, $accountId] = explode('_', $templateId, 2);
        }

        try {
            if (! $accountId || ! $templateId) {
                throw new Exception('Invalid acount or templateId', 1); // NOSONAR
            }

            $waba = new Waba($accountId, $this->requestId);
            $template = $waba->fetchTemplateById($templateId);

            if (! $template) {
                throw new Exception('Template not found in DB', 1); // NOSONAR
            }

            $template = $this->buildTemplateParams($template);

            if (! $template->params) {
                return $this->naOption;
            }

            $options = [
                [
                    'label' => 'Parameter',
                    'value' => $template->params,
                    'description' => $template->body,
                ],
            ];

            return response()->json(['options' => $options]);
        } catch (Exception $e) {
            Log::error("[HubspotController:workflowParams] $this->requestId, Exception: ".$e->getMessage());

            return response()->json(['options' => []]);
        }
    }

    public function workflowParamsHeader(Request $request)
    {
        $input = $request->input();
        Log::info("[HubspotController:workflowParamsHeader] for $this->requestId, payload: ".json_encode($input));

        $accountId = $request->input('account.fieldValue.value');
        $templateId = $request->input('template.fieldValue.value');

        // tempfix for issues in "Send WhatsApp Text Message", because hubspot isn't sending inputfields of other filled inputs
        if (! $accountId && strpos($templateId, '_') !== false) {
            [$templateId, $accountId] = explode('_', $templateId, 2);
        }

        try {
            if (! $accountId || ! $templateId) {
                throw new Exception('Invalid acount or templateId', 1); // NOSONAR
            }

            $waba = new Waba($accountId, $this->requestId);
            $template = $waba->fetchTemplateById($templateId);

            if (! $template) {
                throw new Exception('Template not found in DB', 1); // NOSONAR
            }

            $template = $this->buildTemplateParams($template);
            $header = $template->header ?? null;

            if (! $header) {
                return $this->naOption;
            }

            $headerType = $header->format ?? null;
            if ($headerType != 'TEXT') {
                return $this->naOption;
            }

            $paramcount = preg_match_all('/\{\{\d+\}\}/', $header->text);
            if ($paramcount < 1) {
                return $this->naOption;
            }

            $options = [
                [
                    'label' => 'Header Param text',
                    'value' => '1',
                    'description' => 'Header Param text Input',
                ],
            ];

            return response()->json(['options' => $options]);
        } catch (Exception $e) {
            Log::error("[HubspotController:workflowParams] $this->requestId, Exception: ".$e->getMessage());

            return response()->json(['options' => []]);
        }
    }

    public function hubspotLists(Request $request)
    {
        $input = $request->validate(['user_id' => 'required']);
        $query = $request->input('query', '');
        $portalId = readUserId($input['user_id'], 'portalId');

        $lists = $this->hubspotApp->forPortal($portalId)->lists()->fetchContactLists($portalId, $query);

        return $this->jsonOk(['data' => $lists]);
    }

    public function hubspotProperties(Request $request)
    {
        $input = $request->validate(['user_id' => 'required', 'filtered' => 'nullable']);
        $portalId = readUserId($input['user_id'], 'portalId');
        $filtered = $input['filtered'] ?? false;

        $properties = $this->hubspotApp->forPortal($portalId)->contacts()->getProperties();
        if ($filtered) {
            $new = [];
            foreach ($properties->results as $property) {
                $prop = (object) [
                    'name' => $property->name ?? null,
                    'label' => $property->label ?? null,
                    'type' => $property->type ?? null,
                    'field_type' => $property->fieldType ?? null,
                ];
                $new[] = $prop;
            }
            $new && $properties = $new;
        }

        return $this->jsonOk(['data' => $properties]);
    }

    public function workflow(Request $request)
    {
        $input = $request->input();
        Log::info("[HubspotController:workflow] $this->requestId, payload: ".json_encode($input));

        // tempfix for issues in "Send WhatsApp Text Message", because hubspot isn't sending inputfields of other filled inputs
        if (! empty($input['fields']['template']) && strpos($input['fields']['template'], '_') !== false) {
            $input['fields']['template'] = explode('_', $input['fields']['template'], 2)[0];
        }

        try {
            dispatch(new WhatsAppTextJob($input, $this->requestId))->onQueue('wabaw');

            return response()->json([
                'status' => 'ok',
                'requestId' => $this->requestId,
                'message' => 'request has been queued',
                'status_link' => env('APP_URL').'/workflows/enrollment/status/'.$this->requestId,
            ]);
        } catch (Exception $e) {
            Log::error("[HubspotController:workflow] $this->requestId, Exception: ".$e->getMessage());

            return res('error', 'duplicate workflow execution');
        }
    }

    public function workflowMedia(Request $request)
    {
        $input = $request->input();
        Log::info("[HubspotController:workflowMedia] $this->requestId, payload: ".json_encode($input));

        try {
            dispatch(new WhatsAppMediaJob($input, $this->requestId))->onQueue('wabaw');

            return response()->json([
                'status' => 'ok',
                'requestId' => $this->requestId,
                'message' => 'request has been queued',
                'status_link' => env('APP_URL').'/workflows/enrollment/status/'.$this->requestId,
            ]);
        } catch (Exception $e) {
            Log::error("[HubspotController:workflow] $this->requestId, Exception: ".$e->getMessage());

            return $this->jsonError(['error' => 'something went wrong']);
        }
    }

    public function workflowMediaV1(Request $request)
    {
        $input = $request->input();
        Log::info("[HubspotController:workflowMediaV1] $this->requestId, payload: ".json_encode($input));

        try {
            dispatch(new WhatsAppMediaProJob($input, $this->requestId))->onQueue('wabaw');

            return response()->json([
                'status' => 'ok',
                'requestId' => $this->requestId,
                'message' => 'request has been queued',
                'status_link' => env('APP_URL').'/workflows/enrollment/status/'.$this->requestId,
            ]);
        } catch (Exception $e) {
            Log::error("[HubspotController:workflowMediaV1] $this->requestId, Exception: ".$e->getMessage());

            return $this->jsonError(['error' => 'something went wrong']);
        }
    }

    public function createContactList(Request $request)
    {
        $input = $request->validate([
            'campaign' => 'required|integer',
            'status' => 'required|string',
            'listName' => 'required|string',
        ]);

        // Get the statuses to include based on the requested status
        $statusConditions = $this->getStatusConditions($input['status']);

        // Check if campaign exists before fetching full data (Faster)
        $hubList = HubList::where('id', $input['campaign'])
            ->with(['campaign_user_details' => function ($query) use ($statusConditions) {
                $query->whereIn('status', $statusConditions)
                    ->select('object_id', 'campaign_id'); // Fetch only needed fields
            }])
            ->select('id', 'portal_id') // Select only required columns
            ->first();

        if (! $hubList) {
            return response()->json([
                'status' => 'error',
                'message' => 'Campaign not found',
            ]);
        }

        // Create the contact list in HubSpot
        $properties = $this->hubspotApp->forPortal($hubList->portal_id)->lists()->createContactList(
            $hubList->portal_id,
            $input['listName']
        );

        // Add contacts only if list creation was successful
        if (! empty($properties['list']['listId'])) {
            $contactIds = $hubList->campaign_user_details->pluck('object_id')->toArray();

            if (! empty($contactIds)) {
                $this->hubspotApp->forPortal($hubList->portal_id)->lists()->addMembersInList(
                    $hubList->portal_id,
                    $properties['list']['listId'],
                    $contactIds
                );
            }

            return response()->json([
                'status' => 'ok',
                'requestId' => $this->requestId,
                'message' => $properties,
            ]);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => $properties['message'],
            ]);
        }
    }
}
