<?php

namespace App\Services\Whatsapp;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class CacheService
{
    private const CACHE_PREFIX = 'waba:';

    private const DEFAULT_TTL = 3600; // 1 hour cache TTL

    // Cache types
    public const TEMPLATES = 'templates';

    public const MESSAGES = 'messages';

    public const ANALYTICS = 'analytics';

    public const SETTINGS = 'settings';

    // TTL configurations for different cache types
    private const TTL_CONFIG = [
        self::TEMPLATES => 3600,    // 1 hour
        self::MESSAGES => 300,      // 5 minutes
        self::ANALYTICS => 86400,   // 24 hours
        self::SETTINGS => 7200,     // 2 hours
    ];

    public function __construct(private string $requestId = '') {}

    /**
     * Get data from cache
     */
    public function get(string $wabaId, string $type, ?string $subKey = null): mixed
    {
        $cacheKey = $this->getCacheKey($wabaId, $type, $subKey);
        $compressed = Cache::get($cacheKey);

        if (! $compressed) {
            return null;
        }

        $data = $this->decompress($compressed);

        Log::info("[CacheService:get] {$this->requestId}, Retrieved {$type} from cache for WABA ID: {$wabaId}".($subKey ? ", subKey: {$subKey}" : ''));

        return $data;
    }

    /**
     * Store data in cache
     */
    public function set(string $wabaId, string $type, mixed $data, ?string $subKey = null, ?int $ttl = null): void
    {
        $cacheKey = $this->getCacheKey($wabaId, $type, $subKey);
        $compressed = $this->compress($data);

        $ttl = $ttl ?? self::TTL_CONFIG[$type] ?? self::DEFAULT_TTL;
        Cache::put($cacheKey, $compressed, $ttl);

        // Track subkeys if present
        if ($subKey) {
            $this->addSubkeyToTracker($wabaId, $type, $subKey);
        }

        Log::info("[CacheService:set] {$this->requestId}, Cached {$type} for WABA ID: {$wabaId}".($subKey ? ", subKey: {$subKey}" : ''));
    }

    /**
     * Update existing cache with new data
     */
    public function update(string $wabaId, string $type, mixed $newData, ?string $subKey = null): void
    {
        $existingData = $this->get($wabaId, $type, $subKey);

        if (! $existingData) {
            $this->set($wabaId, $type, $newData, $subKey);

            return;
        }

        // Use type-specific merge logic
        $updatedData = $this->mergeData($existingData, $newData, $type);

        $this->set($wabaId, $type, $updatedData, $subKey);

        Log::info("[CacheService:update] {$this->requestId}, Updated {$type} cache for WABA ID: {$wabaId}".($subKey ? ", subKey: {$subKey}" : ''));
    }

    /**
     * Clear specific cache
     */
    public function clear(string $wabaId, string $type, ?string $subKey = null): void
    {
        if ($subKey) {
            // Clear specific subkey
            $cacheKey = $this->getCacheKey($wabaId, $type, $subKey);
            Cache::forget($cacheKey);
            $this->removeSubkeyFromTracker($wabaId, $type, $subKey);

            Log::info("[CacheService:clear] {$this->requestId}, Cleared {$type} subkey cache for WABA ID: {$wabaId}, subKey: {$subKey}");
        } else {
            // Clear main cache and all subkeys
            $this->clearAllSubkeys($wabaId, $type);

            // Clear main cache
            $cacheKey = $this->getCacheKey($wabaId, $type);
            Cache::forget($cacheKey);

            Log::info("[CacheService:clear] {$this->requestId}, Cleared all {$type} cache for WABA ID: {$wabaId}");
        }
    }

    /**
     * Check if cache exists
     */
    public function has(string $wabaId, string $type, ?string $subKey = null): bool
    {
        $cacheKey = $this->getCacheKey($wabaId, $type, $subKey);

        return Cache::has($cacheKey);
    }

    /**
     * Add subkey to tracker
     */
    private function addSubkeyToTracker(string $wabaId, string $type, string $subKey): void
    {
        $trackerKey = $this->getSubkeyTrackerKey($wabaId, $type);
        $subkeys = Cache::get($trackerKey, []);

        if (! in_array($subKey, $subkeys)) {
            $subkeys[] = $subKey;
            Cache::put($trackerKey, $subkeys, $this->getTtl($type));
        }
    }

    /**
     * Remove subkey from tracker
     */
    private function removeSubkeyFromTracker(string $wabaId, string $type, string $subKey): void
    {
        $trackerKey = $this->getSubkeyTrackerKey($wabaId, $type);
        $subkeys = Cache::get($trackerKey, []);

        $subkeys = array_filter($subkeys, fn ($key) => $key !== $subKey);

        if (empty($subkeys)) {
            Cache::forget($trackerKey);
        } else {
            Cache::put($trackerKey, array_values($subkeys), $this->getTtl($type));
        }
    }

    /**
     * Clear all tracked subkeys
     */
    private function clearAllSubkeys(string $wabaId, string $type): void
    {
        $trackerKey = $this->getSubkeyTrackerKey($wabaId, $type);
        $subkeys = Cache::get($trackerKey, []);

        foreach ($subkeys as $subKey) {
            $cacheKey = $this->getCacheKey($wabaId, $type, $subKey);
            Cache::forget($cacheKey);
        }

        // Clear the tracker itself
        Cache::forget($trackerKey);

        Log::info("[CacheService:clearAllSubkeys] {$this->requestId}, Cleared ".count($subkeys)." subkeys for {$type}, WABA ID: {$wabaId}");
    }

    /**
     * Get subkey tracker cache key
     */
    private function getSubkeyTrackerKey(string $wabaId, string $type): string
    {
        return self::CACHE_PREFIX.$wabaId.':'.$type.'_subkeys';
    }

    /**
     * Get TTL for cache type
     */
    private function getTtl(string $type): int
    {
        return self::TTL_CONFIG[$type] ?? self::DEFAULT_TTL;
    }

    /**
     * Get cache key
     */
    private function getCacheKey(string $wabaId, string $type, ?string $subKey = null): string
    {
        $key = self::CACHE_PREFIX.$wabaId.':'.$type;
        if ($subKey) {
            $key .= ':'.$subKey;
        }

        return $key;
    }

    /**
     * Compress data to reduce memory usage
     * Uses lighter compression for better performance
     */
    private function compress(mixed $data): string
    {
        $jsonString = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        // Use compression level 6 for better balance of compression vs performance
        // For small data, compression might not be worth it, but keeping for consistency
        return gzencode($jsonString, 6);
    }

    /**
     * Decompress data from cache
     */
    private function decompress(string $compressedData): mixed
    {
        $jsonString = gzdecode($compressedData);

        if ($jsonString === false) {
            Log::error("[CacheService:decompress] {$this->requestId}, Failed to decompress cache data");

            return null;
        }

        $data = json_decode($jsonString);

        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error("[CacheService:decompress] {$this->requestId}, JSON decode error: ".json_last_error_msg());

            return null;
        }

        return $data;
    }

    /**
     * Merge data based on cache type
     */
    private function mergeData(mixed $existingData, mixed $newData, string $type): mixed
    {
        // For now, just replace with new data
        // You can implement type-specific merge logic here if needed
        return $newData;
    }
}
