<?php

namespace App\Services\Whatsapp;

use App\Helpers\Waba;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class TemplateStore
{
    private Waba $waba;

    private CacheService $cacheService;

    public function __construct(Waba $waba)
    {
        $this->waba = $waba;
        $this->cacheService = new CacheService($waba->requestId);
    }

    public function fetchTemplates(array $options = []): mixed
    {
        // Generate a unique cache key based on all parameters
        $cacheKey = $this->getCacheKey($options);

        // Try cache first
        if ($cached = $this->getFromCache($cacheKey)) {
            Log::info("[TemplateStore:fetchTemplates] {$this->waba->requestId}, Fetched from cache with key: ".$cacheKey);

            return $cached;
        }

        // Fetch from API
        $response = $this->fetchFromApi($options);

        // Cache the response
        if ($response) {
            $this->saveToCache($cacheKey, $response);
        }

        return $response;
    }

    private function getCacheKey(array $options): string
    {
        // Create a unique key based on filters, pagination, and other params
        $keyData = [
            'filters' => $options['filters'] ?? [],
            'limit' => $options['limit'] ?? 200, // Match default limit from fetchFromApi
            'before' => $options['before'] ?? '',
            'after' => $options['after'] ?? '',
        ];

        // Check if options are effectively empty (default values)
        $isEmpty = empty($keyData['filters']) &&
                   $keyData['limit'] == 200 &&
                   empty($keyData['before']) &&
                   empty($keyData['after']);

        if ($isEmpty) {
            // For default/empty options, don't use subKey
            if (! $this->waba->account?->waba_id) {
                return 'templates';
            }

            return 'waba:'.$this->waba->account->waba_id.':templates';
        }

        // Generate hash for the options - use consistent JSON encoding
        $optionsHash = md5(json_encode($keyData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE));

        // Return the full cache key format that matches CacheService
        if (! $this->waba->account?->waba_id) {
            return 'templates:'.$optionsHash;
        }

        return 'waba:'.$this->waba->account->waba_id.':templates:'.$optionsHash;
    }

    private function getFromCache(string $cacheKey): mixed
    {
        if (! $this->waba->account?->waba_id) {
            return null;
        }

        // Check if this is a default cache key (no subKey needed)
        $baseKey = 'waba:'.$this->waba->account->waba_id.':templates';
        if ($cacheKey === $baseKey) {
            // No subKey for default options
            $cached = $this->cacheService->get(
                $this->waba->account->waba_id,
                CacheService::TEMPLATES
            );
        } else {
            // Extract the options hash from the full cache key
            $optionsHash = substr($cacheKey, strrpos($cacheKey, ':') + 1);
            $cached = $this->cacheService->get(
                $this->waba->account->waba_id,
                CacheService::TEMPLATES,
                $optionsHash
            );
        }

        if ($cached) {
            Log::info("[TemplateStore:getFromCache] {$this->waba->requestId}, wabaId:{$this->waba->account->waba_id}, Retrieved from cache: {$cacheKey}");
        }

        return $cached;
    }

    private function fetchFromApi(array $options): mixed
    {
        $baseUrl = $this->waba->baseUrl.$this->waba->account->waba_id.'/message_templates';

        // Build query params
        $queryParams = [
            'limit' => $options['limit'] ?? 200,
            'fields' => 'name,components,category,status,rejected_reason,language',
        ];

        // Add filters
        if (! empty($options['filters'])) {
            $queryParams = array_merge($queryParams, array_filter($options['filters']));
        }

        // Add pagination
        if (! empty($options['before'])) {
            $queryParams['before'] = $options['before'];
        }
        if (! empty($options['after'])) {
            $queryParams['after'] = $options['after'];
        }

        $apiUrl = $baseUrl.'?'.http_build_query($queryParams);

        try {
            $response = Http::withToken(env('WABA_TOKEN'))->get($apiUrl);

            if (! $response->successful()) {
                Log::error("[TemplateStore:fetchFromApi] {$this->waba->requestId}, HTTP Error: Status {$response->status()}, Body: {$response->body()}");
                throw new \Exception('API request failed with status: '.$response->status());
            }

            $responseData = $response->object();

            Log::info("[TemplateStore:fetchFromApi] {$this->waba->requestId}, wabaId:{$this->waba->account->waba_id}, response: ".json_encode($responseData));

            if (isset($responseData->error)) {
                Log::error("[TemplateStore:fetchFromApi] {$this->waba->requestId}, API Error: ".json_encode($responseData));
                throw new \Exception($this->waba->sendErrorResponse($responseData));
            }

            return $responseData;

        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error("[TemplateStore:fetchFromApi] {$this->waba->requestId}, Request Exception: ".$e->getMessage());
            throw new \Exception('Failed to fetch templates from API: '.$e->getMessage());
        } catch (\Exception $e) {
            Log::error("[TemplateStore:fetchFromApi] {$this->waba->requestId}, Exception: ".$e->getMessage());
            throw $e;
        }
    }

    private function saveToCache(string $cacheKey, $data): void
    {
        if (! $this->waba->account?->waba_id) {
            return;
        }

        // Check if this is a default cache key (no subKey needed)
        $baseKey = 'waba:'.$this->waba->account->waba_id.':templates';
        if ($cacheKey === $baseKey) {
            // No subKey for default options
            $this->cacheService->set(
                $this->waba->account->waba_id,
                CacheService::TEMPLATES,
                $data
            );
        } else {
            // Extract the options hash from the full cache key
            $optionsHash = substr($cacheKey, strrpos($cacheKey, ':') + 1);
            $this->cacheService->set(
                $this->waba->account->waba_id,
                CacheService::TEMPLATES,
                $data,
                $optionsHash
            );
        }
    }

    public function clearCache(): void
    {
        if ($this->waba->account?->waba_id) {
            $this->cacheService->clear(
                $this->waba->account->waba_id,
                CacheService::TEMPLATES
            );
        }
    }
}
