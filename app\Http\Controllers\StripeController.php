<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use App\Helpers\Func;
use App\Models\Account;
use App\Helpers\Hubspot;
use Illuminate\Http\Request;
use App\Models\Subscriptions;
use Illuminate\Support\Facades\DB;
use App\Jobs\HubspotPropertiesSyncJob;

class StripeController extends Controller
{
    protected $stripe;

    protected $stripeMode;

    protected $stripeKey;

    protected $plans = [];

    protected $hubspotProperties = [];

    protected $view = 'stripe/';

    public function __construct()
    {
        $this->stripeMode = env('STRIPE_MODE') ?? 'TEST';
        $this->plans = config('stripe.'.strtolower($this->stripeMode));

        $this->stripeKey = 'STRIPE_'.$this->stripeMode.'_';
        $this->stripe = new \Stripe\StripeClient(env($this->stripeKey.'SECRET_KEY'));
    }

    public function config()
    {
        $pubKey = env('STRIPE_'.$this->stripeMode.'_PUBLISHABLE_KEY');

        return response()->json([
            'publishableKey' => $pubKey,
            'stripeCountry' => 'IN',
            'country' => 'US',
            'currency' => 'usd',
            'paymentMethods' => 'card',
        ]);
    }

    public function createSubscription(Request $request)
    {
        $input = $this->validate($request, [
            'name' => 'required',
            'phone' => 'required',
            'email' => 'required',
            'priceId' => 'required',
            'address' => 'required',
            'portalId' => 'required',
            'referred_by' => 'nullable',
            'hubspotEmail' => 'required',
            'paymentMethodId' => 'required',
        ]);
        $stripe = $this->stripe;
        $referredBy = $input['referred_by'] ?? '';
        $interval = explode('_', $input['priceId'])[1] ?? 'monthly';

        $metadata = [
            'portalId' => $input['portalId'],
            'phone' => Func::makeChatId($input['phone']),
            'hubspotEmail' => $input['hubspotEmail'],
            'email' => $input['email'],
            'plan' => $input['priceId'],
        ];

        // Create a new customer object
        $customer = null;

        try {
            $searchQuery = 'email:\''.$metadata['email'].'\'';
            $results = $stripe->customers->search(['query' => $searchQuery]);
            Log::info('[StripeController:createSubscription] customer search response '.json_encode($results));
            $customer = $results[0] ?? null;
            if (! $customer) {
                throw new Exception('customer not found', 1); // NOSONAR
            }
        } catch (Exception $e) {
            Log::error('[StripeController:createSubscription] Exception: '.$e->getMessage());
            $customer = $stripe->customers->create([
                'name' => $input['name'],
                'email' => $input['email'],
                'address' => $input['address'],
                'metadata' => $metadata,
                'payment_method' => $input['paymentMethodId'],
            ]);
        }

        $customerId = $customer->id;
        // Set the default payment method on the customer
        $stripe->customers->update($customerId, [
            'invoice_settings' => [
                'default_payment_method' => $input['paymentMethodId'],
            ],
        ]);

        // Create the subscription
        $referredBy && $metadata['referred_by'] = $referredBy;

        $subscription = $stripe->subscriptions->create([
            'customer' => $customerId,
            'items' => [
                [
                    'price' => $this->plans['prices'][$input['priceId']][$interval]['id'],
                ],
            ],
            'metadata' => $metadata,
            'expand' => ['latest_invoice.payment_intent'],
            'trial_end' => strtotime('+7 Days'),
        ]);

        try {
            Subscriptions::updateOrCreate(['sub_id' => $subscription->id], [
                'portal_id' => $metadata['portalId'],
                'email' => $metadata['email'],
                'phone' => $metadata['phone'],
                'hubspot_email' => $metadata['hubspotEmail'],
                'plan' => $metadata['plan'],
                'plan_interval' => $interval,
                'status' => 'trial',
            ]);
        } catch (Exception $e) {
            Log::error('[StripeController:createSubscription] Error when saving to db: '.$e->getMessage());
        }

        return response()->json($subscription);
    }

    public function buy($plan)
    {
        $planName = explode('_', $plan)[0] ?? '';
        $stripePlan = $this->plans['prices'][$planName] ?? [];
        if (! $stripePlan) {
            return view('error', ['message' => 'Invalid plan']);
        }

        $interval = explode('_', $plan)[1] ?? 'monthly';

        return view($this->view.'buy', [
            'plan' => $planName,
            'interval' => $interval,
            'amount' => $stripePlan[$interval]['amount'],
            'id' => $stripePlan[$interval]['id'],
        ]);
    }

    public function save(Request $request)
    {
        $input = $this->validate($request, [
            'sub_id' => 'required',
            'portal_id' => 'required',
        ]);

        try {
            $data = Subscriptions::create($input);

            return res('ok', 'Successfully saved', ['data', $data]);
        } catch (Exception $e) {
            Log::error('[StripeController:save] Exception: '.$e->getMessage());

            return res('error', 'unable to save');
        }
    }

    public function webhook(Request $request)
    {
        $data = $request->input();
        $payload = @file_get_contents('php://input');

        $livemode = $data['livemode'] ?? '';
        if ($livemode) {
            $endpoint_secret = env('WEBHOOK_LIVE_SECRET');
            \Stripe\Stripe::setApiKey(env('STRIPE_LIVE_SECRET_KEY'));
        } else {
            $endpoint_secret = env('WEBHOOK_TEST_SECRET');
            \Stripe\Stripe::setApiKey(env('STRIPE_TEST_SECRET_KEY'));
        }

        $stripeKey = $livemode ? 'LIVE' : 'TEST';
        $this->stripe = new \Stripe\StripeClient(env('STRIPE_'.$stripeKey.'_'.'SECRET_KEY'));

        $event = null;

        $sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];

        try {
            $event = \Stripe\Webhook::constructEvent($payload, $sig_header, $endpoint_secret);
        } catch (\UnexpectedValueException $e) {
            Log::error('[StripeController:stripeWebhook] UnexpectedValueException '.$e->getMessage());
            // Invalid payload
            http_response_code(400);
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            Log::error('[StripeController:stripeWebhook] SignatureVerificationException '.$e->getMessage());
            // Invalid signature
            http_response_code(400);
        }

        $eventType = $event->type;
        $object = $event->data->object;
        $subId = $object->subscription ?? '';
        $lineItem = $object->lines->data[0] ?? [];
        $metadata = $lineItem->metadata ?? [];

        try {
            DB::table('stripe')->insert(['data' => $payload]);
        } catch (Exception $e) {
            Log::error("[StripeController:customer] can't save to DB: ".$e->getMessage());
        }

        // check if product belongs to whatswey
        if ($eventType == 'invoice.paid') {
            // get billing date
            $billingDate = null;
            $stripeSubscription = $this->stripe->subscriptions->retrieve($subId, []);
            if ($stripeSubscription->status == 'trialing') {
                $trialEndUtc = Func::timestampToUtcMidnight($stripeSubscription->trial_end);
                $trialStartUtc = Func::timestampToUtcMidnight($stripeSubscription->trial_start);
                $this->hubspotProperties['vira_account_in_trial_'] = 'Yes';
                $this->hubspotProperties['vira_account_trial_start_date'] = $trialStartUtc;
                $this->hubspotProperties['vira_account_trial_end_date'] = $trialEndUtc;
                $billingDate = $stripeSubscription->trial_end;
            } else {
                $this->hubspotProperties['vira_account_in_trial_'] = 'No';
                $billingDate = $stripeSubscription->current_period_end;
            }

            $this->hubspotProperties['vira_account_cancelled'] = '';
            $this->hubspotProperties['vira_account_cancelled_date'] = '';
            $this->hubspotProperties['vira_payment_status'] = $stripeSubscription->status;

            $subscription = Subscriptions::where('sub_id', $subId)->first();
            if (! $subscription) {
                return ['status' => 'ok', 'message' => 'Subscription not found in database'];
            }

            if ($subscription->status == 'paid') {
                // save billing data in this case
                $this->hubspotProperties['vira_next_payment_due_date'] = Func::timestampToUtcMidnight($billingDate);
                dispatch(new HubspotPropertiesSyncJob(
                    $subscription->email,
                    $this->hubspotProperties
                ))->onQueue('wabad');

                return ['status' => 'ok', 'message' => 'No action needed, Billing date updated in HubSpot'];
            }

            // if user has paid then change their status from demo to paid
            if ($event->data->object->total) {
                $subscription->status = 'paid';
                $subscription->save();
                Account::where('subscription_id', $subscription->id)
                    ->update(['paid' => 2, 'billing_date' => $billingDate]);
                $this->hubspotProperties['vira_account_in_trial_'] = 'No';
            }

            // add these properties in hubspot
            $this->hubspotProperties['vira_plan_subscribed'] = $metadata->plan;
            $this->hubspotProperties['vira_customer_email_id'] = $metadata->email;
            $this->hubspotProperties['vira_portal_id_activated'] = $metadata->portalId;
            $this->hubspotProperties['vira_customer_hubspot_email_id'] = $metadata->hubspotEmail;
            $this->hubspotProperties['vira_next_payment_due_date'] = Func::timestampToUtcMidnight($billingDate);

            if ($this->hubspotProperties) {
                dispatch(new HubspotPropertiesSyncJob($subscription->email, $this->hubspotProperties))
                    ->onQueue('wabad');
            }

            return ['status' => 'ok', 'message' => 'Success'];
        }

        if ($eventType == 'customer.subscription.deleted') {
            $subscription = Subscriptions::where('sub_id', $object->id)->first();
            if (! $subscription) {
                return ['status' => 'ok', 'message' => 'subscription not found in db'];
            }

            $subscription->cancelled_by = $object->metadata->email ?? null;
            $subscription->status = 'cancelled';
            $subscription->save();
            Account::where('subscription_id', $subscription->id)->update(['paid' => 0]);

            $this->hubspotProperties['vira_account_cancelled'] = 'Yes';
            $this->hubspotProperties['vira_account_cancelled_date'] = Func::timestampToUtcMidnight(time());
            dispatch(new HubspotPropertiesSyncJob($subscription->email, $this->hubspotProperties))->onQueue('wabad');

            return ['status' => 'ok', 'message' => 'account was closed'];
        }

        return ['status' => 'ok', 'eventType' => $eventType];
    }

    public function dummySub(Request $request)
    {
        $validatedData = $request->validate([
            'portal_id' => 'required|integer',
            'sub_id' => 'required|string|max:100',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'hubspot_email' => 'nullable|email|max:255',
            'plan' => 'required|string|in:starter,professional,enterprise',
        ]);

        // Create a new subscription record
        $subscription = Subscriptions::create($validatedData);

        // Redirect with a success message
        return $this->jsonOk();
    }

    public function test()
    {
        echo 'ok';
    }
}
