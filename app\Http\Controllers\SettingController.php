<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use App\Models\Setting;
use Illuminate\Http\Request;

class SettingController extends Controller
{
    public function __construct()
    {
        parent::__construct('waba');
    }

    public function index(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
        ]);

        $accountId = readUserId($input['user_id']);

        try {
            $setting = Setting::where('account_id', $accountId)->first();

            return $this->jsonOk(['data' => $setting]);
        } catch (Exception $e) {
            Log::error("[SettingController:index] $this->requestId, Exception ".$e->getMessage());

            return $this->jsonError(['message' => 'unable to fetch settings']);
        }
    }

    public function store(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'is_owner' => 'nullable',
            'create_user' => 'nullable',
            'country_code' => 'nullable',
            'consent_property' => 'nullable',
            'consent_property_value' => 'nullable',
        ]);

        $accountId = readUserId($input['user_id']);
        $input['portal_id'] = readUserId($input['user_id'], 'portalId');

        try {
            unset($input['user_id']);
            $setting = Setting::updateOrCreate(['account_id' => $accountId], $input);

            return $this->jsonOk(['data' => $setting]);
        } catch (Exception $e) {
            Log::error("[SettingController:store] $this->requestId, Exception ".$e->getMessage());

            return $this->jsonError(['message' => 'unable add number to blacklist']);
        }
    }
}
