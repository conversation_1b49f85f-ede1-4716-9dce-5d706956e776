<?php

namespace App\Services;

use Illuminate\Support\Str;

class ExportDataService
{
    protected $filename;

    protected $headers;

    protected $dataQuery;

    public function __construct(string $filename, array $headers, $dataQuery, $type)
    {
        $this->filename = $this->sanitizeFilename($filename);
        $this->headers = $headers;
        $this->dataQuery = $dataQuery;
        $this->type = $type;
    }

    public function streamCsv()
    {
        return response()->streamDownload(function () {
            $output = fopen('php://output', 'w');

            // Add UTF-8 BOM for Excel compatibility
            fprintf($output, "\xEF\xBB\xBF");

            // Write CSV Headers
            fputcsv($output, $this->headers);

            // Process data in chunks to avoid memory overload
            $this->dataQuery->chunk(500, function ($chunk) use ($output) {
                foreach ($chunk as $row) {
                    fputcsv($output, $this->formatRow($row->toArray()));
                }
            });

            fclose($output);
        }, $this->filename, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="'.$this->filename.'"',
        ]);
    }

    protected function sanitizeFilename($filename)
    {
        return Str::slug($filename, '_').'.csv';
    }

    protected function formatRow($row)
    {
        if ($this->type === 'campaign') {
            if (! empty($row['body'])) {
                $row['body'] = Crypt::decryptString($row['body']);
            } else {
                $row['body'] = 'N/A';
            }
        }

        // Convert all values to UTF-8 and ensure no issues with special characters
        return array_map(fn ($value) => mb_convert_encoding($value, 'UTF-8', 'auto'), $row);
    }
}
