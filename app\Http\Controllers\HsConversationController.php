<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use App\Models\Account;
use App\Hubspot\Hubspot;
use App\Helpers\HelperTrait;
use Illuminate\Http\Request;
use App\Models\HubspotChannel;
use App\Services\Hubspot\ConversationWebhookService;

class HsConversationController extends Controller
{
    use HelperTrait;

    protected $hsApp;

    public function __construct()
    {
        parent::__construct('waba');
        $this->hsApp = $this->hubspotApp;
    }

    public function auth(Request $request)
    {
        $input = $request->validate([
            'portalId' => 'required',
            'inboxId' => 'required',
            'channelId' => 'required',
            'redirectUrl' => 'required',
            'accountToken' => 'required',
        ]);

        Log::info("[HsConversationController:auth] $this->requestId, payload", $input);

        try {
            $accounts = Account::where('portal_id', $input['portalId'])->whereNull('hs_channel_id')->get();

            return view('conversation/connect', [
                'input' => $input,
                'accounts' => $accounts,
                'inboxRedirectUrl' => env('APP_URL').'/hubspot/conversation/auth?'.http_build_query($input),
                'fbAuthUrl' => $this->waba->getAuthUrl(),
            ]);
        } catch (Exception $e) {
            Log::error("[HsConversationController:auth] $this->requestId, Error during authentication", ['error' => $e->getMessage()]);

            return $this->jsonError(['error' => 'Internal server error'], 500);
        }
    }

    public function save(Request $request)
    {
        $input = $request->validate([
            'accountId' => 'required',
            'inboxId' => 'required',
            'channelId' => 'required',
            'accountToken' => 'required',
        ]);
        Log::info("[HsConversationController:save] $this->requestId, payload: ", $input);

        $accountId = decryptSodium($input['accountId']);

        try {
            $account = Account::where('id', $accountId)->first();

            $hsApp = new Hubspot($account->portal_id, $this->requestId);

            $updated = $hsApp->conversations()->updateChannel((object) [
                'channelId' => $input['channelId'],
                'accountToken' => $input['accountToken'],
                'accountName' => $account->waba_name ? $account->waba_name : $account->waba_phone,
                'deliveryIdentifier' => [
                    'type' => 'HS_PHONE_NUMBER',
                    'value' => '+'.$account->waba_phone,
                ],
            ]);
            if (! $updated) {
                throw new Exception('Unable to update channel to hubspot', 1);
            }

            $channel = HubspotChannel::updateOrCreate([
                'portal_id' => $account->portal_id,
                'deliveryIdentifier' => $account->waba_phone,
            ], [
                'inbox_id' => $input['inboxId'],
                'channel_id' => $input['channelId'],
                'account_token' => $input['accountToken'],
            ]);
            $account->update(['hs_channel_id' => $channel->id]);

            return $this->jsonOk();
        } catch (Exception $e) {
            Log::error("[HsConversationController:save] $this->requestId, Exception: ".$e->getMessage());

            return $this->jsonError(['message' => 'unable to save channel']);
        }
    }

    public function webhook(Request $request)
    {
        $input = $request->input();
        Log::info("[HsConversationController:webhook] $this->requestId, payload: ".json_encode($input));

        try {
            $service = new ConversationWebhookService($this->requestId);

            return $service->handle($input);
        } catch (Exception $e) {
            Log::error("[HsConversationController:webhook] $this->requestId, Exception: ".$e->getMessage());

            return $this->jsonError(['message' => "Something went wrong for {$this->requestId}"]);
        }
    }
}
