<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Hubspot\Hubspot;
use App\Models\HubspotChannel;
use Illuminate\Console\Command;

class SyncChannelCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:channel';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync hubspot channel account Id';

    /**
     * Execute the console command.
     */
    protected $requestId;

    public function handle()
    {

        try {
            $channels = HubspotChannel::whereNull('channel_account_id')->get();
            foreach ($channels as $channel) {
                $this->requestId = round(microtime(true) * 1000).'-comm-'.$channel->id;
                Log::info("[SyncChannelCommand:handle] $this->requestId, running");

                $hsApp = new Hubspot($channel->portal_id, $this->requestId);
                $channelAccountId = $hsApp->conversations()->getChannelAccountId($channel->channel_id, $channel->deliveryIdentifier);
                if (! $channelAccountId) {
                    throw new Exception('channelAccountId not found', 1);
                }

                $channel->update(['channel_account_id' => $channelAccountId]);
                Log::info("[SyncChannelCommand:handle] $this->requestId, success");
            }
        } catch (Exception $e) {
            Log::error("[SyncChannelCommand:handle] $this->requestId, Exception: ".$e->getMessage());
        }
    }
}
