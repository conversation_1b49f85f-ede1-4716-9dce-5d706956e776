<?php

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use App\Models\TemplateFileLinks;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;

class SheetTemplateDataMappingCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:template-data-from-sheet';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync template data from google sheet and put in database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->print('started');

        $accountId = 23;
        $sheetName = 'Sheet1';
        $sheetId = '1j1tGkVTfzM7NZF_XGG6WAJNtYzBEpZv5Dv1RFg4g1sg';
        $url = "https://docs.google.com/spreadsheets/d/$sheetId/gviz/tq?tqx=out:csv&sheet=$sheetName";

        $response = Http::get($url);
        if ($response->successful()) {
            $csvData = $response->body();

            $rows = array_map('str_getcsv', explode("\n", $csvData));
            foreach ($rows as $key => $value) {
                if ($key && ! empty($value[4])) {
                    $parsed_url = parse_url($value[5]);
                    $query_string = $parsed_url['query'];
                    parse_str($query_string, $query_params);
                    $id = $query_params['id'] ?? null;
                    if (! $id) {
                        continue;
                    }
                    $path = parse_url($value[4], PHP_URL_PATH);
                    TemplateFileLinks::updateOrCreate(['account_id' => $accountId, 'template_id' => $id], [
                        'template_id' => $id,
                        'account_id' => 23,
                        'file_type' => $this->getFileCategory($value[4]),
                        'file_url' => $value[4],
                    ]);
                }
            }
        } else {
            $this->print('Failed to fetch data. HTTP Status: '.$response->status());
        }
        $this->print('done');
    }

    public function getFileCategory(string $fileUrl): string
    {
        $mimeType = null;

        // Check if the URL is a local file
        if (File::exists($fileUrl)) {
            $mimeType = File::mimeType($fileUrl);
        } else {
            // Handle remote file URL
            try {
                $response = Http::head($fileUrl); // Get the file headers
                if ($response->successful() && $response->hasHeader('Content-Type')) {
                    $mimeType = $response->header('Content-Type');
                }
            } catch (Exception $e) {
                return 'NA'; // Return 'NA' if fetching headers fails
            }
        }

        // If MIME type is null or cannot be determined
        if (! $mimeType) {
            return 'NA';
        }

        // Determine the file category
        $type = explode('/', $mimeType)[0] ?? 'NA';
        if ($type === 'application') {
            $type = 'document';
        }

        return $type;
    }

    public function print($message)
    {
        echo $message.PHP_EOL;
    }
}
