<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use App\Models\Blacklist;
use Illuminate\Http\Request;

class BlacklistController extends Controller
{
    public function __construct()
    {
        parent::__construct('waba');
    }

    public function index(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
        ]);

        $accountId = readUserId($input['user_id']);

        try {
            $blacklist = Blacklist::where('account_id', $accountId)->get();

            return $this->jsonOk(['data' => $blacklist]);
        } catch (Exception $e) {
            Log::error("[LabelController:index] $this->requestId, Exception ".$e->getMessage());

            return $this->jsonError(['message' => 'unablet of fetch blacklist numbers']);
        }
    }

    public function store(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'phone' => 'required',
        ]);

        $accountId = readUserId($input['user_id']);
        $portalId = readUserId($input['user_id'], 'portalId');

        try {
            $blacklist = Blacklist::updateOrCreate([
                'account_id' => $accountId,
                'portal_id' => $portalId,
                'phone' => ltrim($input['phone'], '+'),
            ]);

            return $this->jsonOk(['data' => $blacklist]);
        } catch (Exception $e) {
            Log::error("[LabelController:store] $this->requestId, Exception ".$e->getMessage());

            return $this->jsonError(['message' => 'unable add number to blacklist']);
        }
    }

    public function delete(Request $request, $id)
    {
        $input = $request->validate([
            'user_id' => 'required',
        ]);

        $accountId = readUserId($input['user_id']);

        try {
            Blacklist::where(['id' => $id, 'account_id' => $accountId])->delete();

            return $this->jsonOk();
        } catch (Exception $e) {
            Log::error("[LabelController:store] $this->requestId, Exception ".$e->getMessage());

            return $this->jsonError(['message' => 'unable of delete blacklist number']);
        }
    }
}
