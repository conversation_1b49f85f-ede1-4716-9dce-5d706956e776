<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('sync:template-analytics')->daily();
        $schedule->command('sync:channel')->everyThirtySeconds();
        $schedule->command('sync:template-data-from-sheet')->hourly();
        $schedule->command('sync:workflow-names')->hourly();
        // reports
        $schedule->command('workflow:reports')->everyThirtyMinutes();
        $schedule->command('build:reports')->everyThirtyMinutes();

        // campaigns
        $schedule->command('schedule:campaign')->everyFiveMinutes();
        $schedule->command('recover:campaigns')->everyFifteenMinutes();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php'); // NOSONAR
    }
}
