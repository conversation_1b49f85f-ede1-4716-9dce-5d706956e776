<?php

namespace App\Http\Controllers;

use Exception;
use App\Models\Role;
use Illuminate\Http\Request;


class RoleController extends Controller
{
    protected $user;
    public function __construct(Request $request)
    {
        
        parent::__construct('waba');
        $this->user = $request->attributes->get('auth');
        dd($this->user);
    }

    /**
     * Get all roles for the account associated with the provided user ID.
     */
    public function index(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|string',
        ]);
        
        try {
            $accountId = readUserId($validated['user_id']);
            $roles = Role::where('account_id', $accountId)->get();

            return $this->jsonOk(['roles' => $roles]);
        } catch (Exception $e) {
            return $this->jsonError(['message' => $e->getMessage()], 400);
        }
    }

    /**
     * Get details of a specific role by ID, ensuring it belongs to the user's account.
     */
    public function show(Request $request, $id)
    {
        $validated = $request->validate([
            'user_id' => 'required|string',
        ]);

        try {
            $accountId = readUserId($validated['user_id']);
            $role = Role::where('id', $id)
                ->where('account_id', $accountId)
                ->firstOrFail();

            return $this->jsonOk(['role' => $role]);
        } catch (Exception $e) {
            return $this->jsonError(['message' => $e->getMessage()], 404);
        }
    }

    /**
     * Create a new role associated with the user's account.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|string',
            'name' => 'required|string|max:255',
        ]);

        try {
            $accountId = readUserId($validated['user_id']);
            $role = Role::create([
                'name' => $validated['name'],
                'account_id' => $accountId,
            ]);

            return $this->jsonOk(['message' => 'Role created successfully.', 'role' => $role], 201);
        } catch (Exception $e) {
            return $this->jsonError(['message' => $e->getMessage()], 400);
        }
    }

    /**
     * Update an existing role by ID, ensuring it belongs to the user's account.
     */
    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'user_id' => 'required|string',
            'name' => 'required|string|max:255',
        ]);

        try {
            $accountId = readUserId($validated['user_id']);
            $role = Role::where('id', $id)
                ->where('account_id', $accountId)
                ->firstOrFail();

            $role->update(['name' => $validated['name']]);

            return $this->jsonOk(['message' => 'Role updated successfully.', 'role' => $role]);
        } catch (Exception $e) {
            return $this->jsonError(['message' => $e->getMessage()], 400);
        }
    }

    /**
     * Delete a role by ID, ensuring it belongs to the user's account.
     */
    public function destroy(Request $request, $id)
    {
        $validated = $request->validate([
            'user_id' => 'required|string',
        ]);

        try {
            $accountId = readUserId($validated['user_id']);
            $role = Role::where('id', $id)
                ->where('account_id', $accountId)
                ->firstOrFail();

            $role->delete();

            return $this->jsonOk(['message' => 'Role deleted successfully.']);
        } catch (Exception $e) {
            return $this->jsonError(['message' => $e->getMessage()], 400);
        }
    }
}
