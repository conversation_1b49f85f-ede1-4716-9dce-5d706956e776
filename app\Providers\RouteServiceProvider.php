<?php

namespace App\Providers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to your application's "home" route.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/home';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        $this->configureRateLimiting();

        $this->routes(function () {
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware('web')
                ->group(base_path('routes/web.php'));
        });
    }

    /**
     * Configure the rate limiters for the application.
     */
    protected function configureRateLimiting(): void
    {
        RateLimiter::for('api', function (Request $request) {
            $workflowId = null;
            $path = $request->getRequestUri();
            $content = $request->getContent();

            // Check for the workflow routes
            if (preg_match('#/api/(v1/)?hubspot/workflow/(text|media)#', $path)) {
                $pattern = '/"workflowId":\s*(\d+)/';
                if (preg_match($pattern, $content, $matches)) {
                    $workflowId = (int) $matches[1] ?? null;
                }
            }

            if ($workflowId && is_int($workflowId)) {
                return Limit::perMinute(600)->by('hubspot-workflow-'.$workflowId);
            } else {
                return Limit::perMinute(300)->by(optional($request->user())->id ?: $request->ip());
            }
        });
    }
}
