<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use Carbon\Carbon;
use App\Helpers\Func;
use App\Models\HubList;
use App\Models\Message;
use Illuminate\Http\Request;
use App\Models\WorkflowReport;

class ReportController extends Controller
{
    protected $countIdString = 'COUNT(id)';

    public function __construct()
    {
        parent::__construct('waba');
    }

    public function index(Request $request)
    {
        $input = $request->validate([
            'to' => 'nullable',
            'from' => 'nullable',
            'user_id' => 'required',
            'offset' => 'required',
        ]);

        $data = [];
        $limit = 25;
        $dateFormat = 'm/d/Y';
        $offset = ($input['offset'] ?? 0) * $limit;
        $accountId = readUserId($input['user_id']);

        try {
            $to = ($input['to'] ?? null)
                    ? Carbon::createFromFormat($dateFormat, $input['to'])
                    : Carbon::now();
            $from = ($input['from'] ?? null)
                    ? Carbon::createFromFormat($dateFormat, $input['from'])
                    : Carbon::now()->subMonths(1);
        } catch (Exception $e) {
            return $this->jsonError(['message' => 'Invalid date provided']);
        }

        $total = HubList::selectRaw($this->countIdString)
            ->whereBetween('created_at', [$from, $to])
            ->where('account_id', $accountId)
            ->value($this->countIdString);
        if ($total < 1) {
            return $this->jsonOk(['total' => $total, 'data' => $data]);
        }

        $stats = ['deliveryRate' => 0, 'readRate' => 0, 'dealsCreated' => 0, 'dealsWon' => 0];
        $combined = ['sent' => 0, 'delivered' => 0, 'read' => 0, 'opportunities' => 0, 'customers' => 0];

        $campaigns = HubList::where('account_id', $accountId)
            ->whereBetween('created_at', [$from, $to])
            ->orderByDesc('created_at')
            ->limit($limit)
            ->offset($offset)
            ->get();

        foreach ($campaigns as $campaign) {
            $stats['dealsWon'] += $campaign->deal_won;
            $stats['dealsCreated'] += $campaign->deal_created;

            $combined['sent'] += $campaign->sent;
            $combined['read'] += $campaign->viewed;
            $combined['delivered'] += $campaign->delivered;
            $combined['customers'] += $campaign->contact_customer;
            $combined['opportunities'] += $campaign->contact_opportunity;
            $data[] = [
                'name' => $campaign->name,
                'campaign_name' => $campaign->campaign_name ?? $campaign->name,
                'sent' => $campaign->sent,
                'delivered' => $campaign->delivered,
                'read' => $campaign->viewed,
                'deliveryRate' => ($campaign->sent > 0) ? round(($campaign->delivered / $campaign->sent) * 100) : 0,
                'readRate' => ($campaign->sent > 0) ? round(($campaign->viewed / $campaign->sent) * 100) : 0,
                'opportunities' => $campaign->contact_opportunity,
                'customers' => $campaign->contact_customer,
                'deals' => $campaign->deal_created,
                'dealsWon' => $campaign->deal_won,
            ];
        }

        try {
            $readRate = ($campaign->sent > 0) ? round(($combined['read'] / $combined['sent']) * 100) : 0;
            $deliveryRate = ($campaign->sent > 0) ? round(($combined['delivered'] / $combined['sent']) * 100) : 0;

            $stats['readRate'] = $readRate;
            $stats['deliveryRate'] = $deliveryRate;
        } catch (Exception $e) {
            Log::info('[ReportController:index] Exception: '.$e->getMessage());
        }

        $rangeFormat = 'm/d/Y';

        return $this->jsonOk([
            'total' => $total,
            'stats' => $stats,
            'combined' => $combined,
            'data' => $data,
            'range' => [
                'format' => $rangeFormat,
                'to' => $to->format($rangeFormat),
                'from' => $from->format($rangeFormat),
            ],
        ]);
    }

    public function export(Request $request)
    {
        $input = $request->validate([
            'to' => 'required',
            'from' => 'required',
            'user_id' => 'required',
        ]);

        $dateFormat = 'd/m/Y';
        $accountId = readUserId($input['user_id']);
        $to = Carbon::createFromFormat($dateFormat, $input['to']);
        $from = Carbon::createFromFormat($dateFormat, $input['from']);

        $total = HubList::selectRaw($this->countIdString)
            ->whereBetween('created_at', [$from, $to])
            ->where('account_id', $accountId)
            ->value($this->countIdString);
        if ($total < 1) {
            return view('error', ['message' => 'No data found']);
        }

        $campaigns = HubList::where('account_id', $accountId)
            ->whereBetween('created_at', [$from, $to])
            ->orderByDesc('created_at')
            ->get();
        $data = [];
        $headers = [
            'Name',
            'Campaign Name',
            'Sent',
            'Delivered',
            'Read',
            'Delivery Rate (%)',
            'Read Rate (%)',
            'Opportunities',
            'Customers',
            'Deals Created',
            'Deals Won',
            'Created At',
        ];

        foreach ($campaigns as $campaign) {
            $row = [
                'name' => $campaign->name,
                'campaign_name' => $campaign->campaign_name ?? $campaign->name,
                'sent' => $campaign->sent,
                'delivered' => $campaign->delivered,
                'read' => $campaign->viewed,
                'deliveryRate' => ($campaign->sent > 0) ? round(($campaign->delivered / $campaign->sent) * 100) : 0,
                'readRate' => ($campaign->sent > 0) ? round(($campaign->viewed / $campaign->sent) * 100) : 0,
                'opportunities' => $campaign->contact_opportunity,
                'customers' => $campaign->contact_customer,
                'deals' => $campaign->deal_created,
                'dealsWon' => $campaign->deal_won,
                'created_at' => $campaign->created_at,
            ];
            $data[] = array_values($row);
        }

        $filename = 'vira-reports.csv';

        return response()->streamDownload(function () use ($headers, $data) {
            $output = fopen('php://output', 'w');
            fprintf($output, "\xEF\xBB\xBF");
            fputcsv($output, $headers);
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
            fclose($output);
        }, $filename, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="'.$filename.'"',
        ]);

    }

    public function getMessagesAnalytics(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'from' => 'nullable',
            'to' => 'nullable',
        ]);

        $timezone = Func::getTimeZone($request->header('TimeZoneOffset'));

        Log::info("[ReportController:getMessagesAnalytics] $this->requestId, timezone: $timezone");

        $accountId = readUserId($input['user_id']);

        $dateFormat = 'd/m/Y';

        $startTime = ! empty($input['from'])
            ? Carbon::createFromFormat($dateFormat, $input['from'])->setTimezone(config('app.timezone'))->startOfDay()->timestamp
            : Carbon::now()->subMonth()->startOfDay()->timestamp;

        $endTime = ! empty($input['to'])
            ? Carbon::createFromFormat($dateFormat, $input['to'])->setTimezone(config('app.timezone'))->endOfDay()->timestamp
            : Carbon::now()->endOfDay()->timestamp;

        $timeRanges = [
            '12 AM - 3 AM' => [0, 3],
            '3 AM - 6 AM' => [3, 6],
            '6 AM - 9 AM' => [6, 9],
            '9 AM - 12 PM' => [9, 12],
            '12 PM - 3 PM' => [12, 15],
            '3 PM - 6 PM' => [15, 18],
            '6 PM - 9 PM' => [18, 21],
            '9 PM - 12 AM' => [21, 24],
        ];

        try {
            $messages = Message::where('fromMe', 0)
                ->where('account_id', $accountId)
                ->whereBetween('time', [$startTime, $endTime])
                ->pluck('time');

            $totalCount = $messages->count();
            $hourlyCounts = array_fill(0, 24, 0);
            $workingHoursCount = 0;
            $nonWorkingHoursCount = 0;

            // Convert timestamps to the correct timezone and count occurrences per hour
            foreach ($messages as $timestamp) {
                $hour = Carbon::createFromTimestamp($timestamp, 'UTC')->setTimezone($timezone)->hour;
                $hourlyCounts[$hour]++;
            }

            $data = [];
            foreach ($timeRanges as $label => [$startHour, $endHour]) {
                $splitedTime = [];
                $rangeCount = 0;

                for ($hour = $startHour; $hour < $endHour; $hour++) {
                    $hourLabel = Carbon::createFromTime($hour)->format('g A').' - '.Carbon::createFromTime(($hour + 1) % 24)->format('g A');
                    $hourCount = $hourlyCounts[$hour];

                    // Categorize working and non-working hours
                    if ($hour >= 9 && $hour < 18) {
                        $workingHoursCount += $hourCount;
                    } else {
                        $nonWorkingHoursCount += $hourCount;
                    }

                    $splitedTime[] = [
                        'time_range' => $hourLabel,
                        'count' => $hourCount,
                    ];

                    $rangeCount += $hourCount;
                }

                $data[] = [
                    'time_range' => $label,
                    'count' => $rangeCount,
                    'splited_time' => $splitedTime,
                ];
            }

            Log::info("[ReportController:getMessagesAnalytics] $this->requestId, data: ".json_encode($data));

            // Calculate percentages
            $workingHoursPercentage = $totalCount > 0 ? round(($workingHoursCount / $totalCount) * 100) : 0;
            $nonWorkingHoursPercentage = $totalCount > 0 ? round(($nonWorkingHoursCount / $totalCount) * 100) : 0;

            return $this->jsonOk([
                'data' => [
                    'total_count' => $totalCount,
                    'working_hours_count' => $workingHoursCount,
                    'non_working_hours_count' => $nonWorkingHoursCount,
                    'working_hours_percentage' => $workingHoursPercentage,
                    'non_working_hours_percentage' => $nonWorkingHoursPercentage,
                    'messages' => $data,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error("[ReportController:getMessagesAnalytics] $this->requestId, Exception: ".$e->getMessage());

            return $this->jsonError();
        }
    }

    public function workflow(Request $request)
    {
        $input = $request->validate([
            'to' => 'nullable',
            'from' => 'nullable',
            'user_id' => 'required',
            'offset' => 'required',
        ]);

        $data = [];
        $limit = 25;
        $offset = ($input['offset'] ?? 0) * $limit;
        $dateFormat = 'd/m/Y';
        $accountId = readUserId($input['user_id']);

        try {
            $to = ($input['to'] ?? null)
                    ? Carbon::createFromFormat($dateFormat, $input['to'])
                    : Carbon::now();
            $from = ($input['from'] ?? null)
                    ? Carbon::createFromFormat($dateFormat, $input['from'])
                    : Carbon::now()->subMonths(1);
        } catch (Exception $e) {
            return $this->jsonError(['message' => 'Invalid date provided']);
        }
        $total = WorkflowReport::selectRaw($this->countIdString)
            ->whereBetween('created_at', [$from, $to])
            ->where('account_id', $accountId)
            ->value($this->countIdString);
        Log::info("[ReportController:workflow] $this->requestId, total: $total");
        if ($total < 1) {
            return $this->jsonOk(['total' => $total, 'data' => $data]);
        }

        $stats = ['deliveryRate' => 0, 'readRate' => 0, 'dealsCreated' => 0, 'dealsWon' => 0];
        $combined = ['sent' => 0, 'delivered' => 0, 'read' => 0, 'opportunities' => 0, 'customers' => 0];

        $workflows = WorkflowReport::where('account_id', $accountId)
            ->whereBetween('created_at', [$from, $to])
            ->orderByDesc('created_at')
            ->limit($limit)
            ->offset($offset)
            ->get();
        Log::info("[ReportController:workflow] $this->requestId, workflows: ".json_encode($workflows));
        foreach ($workflows as $workflow) {
            $stats['dealsWon'] += $workflow->deal_won;
            $stats['dealsCreated'] += $workflow->deal_created;

            $combined['sent'] += $workflow->sent;
            $combined['read'] += $workflow->viewed;
            $combined['delivered'] += $workflow->delivered;
            $combined['customers'] += $workflow->contact_customer;
            $combined['opportunities'] += $workflow->contact_opportunity;
            $data[] = [
                'name' => $workflow->name,
                'id' => $workflow->id,
                'sent' => $workflow->sent,
                'delivered' => $workflow->delivered,
                'read' => $workflow->viewed,
                'deliveryRate' => ($workflow->sent > 0) ? round(($workflow->delivered / $workflow->sent) * 100) : 0,
                'readRate' => ($workflow->sent > 0) ? round(($workflow->viewed / $workflow->sent) * 100) : 0,
                'opportunities' => $workflow->contact_opportunity,
                'customers' => $workflow->contact_customer,
                'deals' => $workflow->deal_created,
                'dealsWon' => $workflow->deal_won,
            ];
        }

        try {
            if ($combined['sent'] != 0) {
                $readRate = round(($combined['read'] / $combined['sent']) * 100);
            } else {
                $readRate = 0;
            }

            if ($combined['sent'] != 0) {
                $deliveryRate = round(($combined['delivered'] / $combined['sent']) * 100);
            } else {
                $deliveryRate = 0;
            }

            $stats['readRate'] = ($combined['sent'] > 0) ? $readRate : 0;
            $stats['deliveryRate'] = ($combined['sent'] > 0) ? $deliveryRate : 0;

        } catch (Exception $e) {
            Log::info('[ReportController:workflow] Exception: '.$e->getMessage());
        }
        $rangeFormat = 'm/d/Y';

        return $this->jsonOk([
            'total' => $total,
            'stats' => $stats,
            'combined' => $combined,
            'data' => $data,
            'range' => [
                'format' => $rangeFormat,
                'to' => $to->format($rangeFormat),
                'from' => $from->format($rangeFormat),
            ],
        ]);
    }

    public function workflowExport(Request $request)
    {
        $input = $request->validate([
            'to' => 'nullable',
            'from' => 'nullable',
            'user_id' => 'required',
        ]);

        $dateFormat = 'm/d/Y';
        $accountId = readUserId($input['user_id']);
        try {
            $to = ($input['to'] ?? null)
                    ? Carbon::createFromFormat($dateFormat, $input['to'])
                    : Carbon::now();
            $from = ($input['from'] ?? null)
                    ? Carbon::createFromFormat($dateFormat, $input['from'])
                    : Carbon::now()->subMonths(1);
        } catch (Exception $e) {
            return $this->jsonError(['message' => 'Invalid date provided']);
        }

        $workflowReports = WorkflowReport::where('account_id', $accountId)
            ->whereBetween('created_at', [$from, $to])
            ->get();
        if (count($workflowReports) < 1) {
            return view('error', ['message' => 'No data found']);
        }
        $data = [];
        $headers = [
            'Name',
            'Sent',
            'Delivered',
            'Read',
            'Delivery Rate (%)',
            'Read Rate (%)',
            'Opportunities',
            'Customers',
            'Deals Created',
            'Deals Won',
        ];

        foreach ($workflowReports as $workflow) {
            $row = [
                'name' => $workflow->name,
                'sent' => $workflow->sent,
                'delivered' => $workflow->delivered,
                'read' => $workflow->viewed,
                'deliveryRate' => ($workflow->sent > 0) ? round(($workflow->delivered / $workflow->sent) * 100) : 0,
                'readRate' => ($workflow->sent > 0) ? round(($workflow->viewed / $workflow->sent) * 100) : 0,
                'opportunities' => $workflow->contact_opportunity,
                'customers' => $workflow->contact_customer,
                'deals' => $workflow->deal_created,
                'dealsWon' => $workflow->deal_won,
            ];
            $data[] = array_values($row);
        }

        $filename = 'vira-workflow-reports.csv';

        return response()->streamDownload(function () use ($headers, $data) {
            $output = fopen('php://output', 'w');
            fprintf($output, "\xEF\xBB\xBF");
            fputcsv($output, $headers);
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
            fclose($output);
        }, $filename, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="'.$filename.'"',
        ]);
    }
}
