<!DOCTYPE html>
<html lang="en">
	<head>
		<title>WABA</title>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<link rel="stylesheet" type="text/css" href="/stripe/stylesheets/store.css" />
		<link rel="icon" type="image/png" sizes="104x80" href="/img/HW-logo.png" />
		<script type="text/javascript">
			const PRICE_ID = "starter";
			const APP_URL = "{{env('APP_URL')}}";
			const REFERRED_BY = '<?php echo ($_GET['referred_by'] ?? '') ?>';
		</script>
		<style type="text/css">
			input::-webkit-outer-spin-button,
			input::-webkit-inner-spin-button {
			  -webkit-appearance: none;
			  margin: 0;
			}

			/* Firefox */
			input[type=number] {
			  -moz-appearance: textfield;
			}
		</style>
	</head>
	<body>
		<main id="main" class="checkout">
			<header class="header">
				<a class="shop" href="/">WABA</a>
				<a class="shop" target="_blank" href="https://www.niswey.com/hubspot-whatsapp-integration/">More info</a>
			</header>
			<div id="checkout">
				<div id="payment-request">
					<div id="payment-request-button"></div>
				</div>
				<form id="payment-form" method="POST" action="/orders">
					<p class="instruction">Complete your payment details below</p>
					<section>
						<h2>Billing Information</h2>
						<fieldset class="with-state">
							<label>
								<span>Name</span>
								<input name="name" class="field" placeholder="Jenny Rosen" required />
							</label>
							<label>
								<span>Email</span>
								<input name="email" type="email" class="field" placeholder="<EMAIL>" required />
							</label>
							<label>
								<span>PortalId</span>
								<input name="portalId" type="number" class="field" placeholder="Enter HubSpot portalId" required />
							</label>
							<label>
								<span>HubSpot Email</span>
								<input name="hubspotEmail" type="email" class="field" placeholder="Enter HubSpot email" required />
							</label>
							<label>
								<span>Address</span>
								<input name="address" class="field" placeholder="185 Berry Street Suite 550" required/>
							</label>
							<label class="city">
								<span>City</span>
								<input name="city" class="field" placeholder="San Francisco" required/>
							</label>
							<label class="state">
								<span>State</span>
								<input name="state" class="field" placeholder="CA"/>
							</label>
							<label class="zip">
								<span>ZIP</span>
								<input name="postal_code" class="field" placeholder="94107" required/>
							</label>
							<label class="select">
								<span>Country</span>
								<div id="country" class="field US">
									<select name="country" required>
										<option value="AF">Afghanistan</option>
										<option value="AX">Åland Islands</option>
										<option value="AL">Albania</option>
										<option value="DZ">Algeria</option>
										<option value="AS">American Samoa</option>
										<option value="AD">Andorra</option>
										<option value="AO">Angola</option>
										<option value="AI">Anguilla</option>
										<option value="AQ">Antarctica</option>
										<option value="AG">Antigua and Barbuda</option>
										<option value="AR">Argentina</option>
										<option value="AM">Armenia</option>
										<option value="AW">Aruba</option>
										<option value="AU">Australia</option>
										<option value="AT">Austria</option>
										<option value="AZ">Azerbaijan</option>
										<option value="BS">Bahamas</option>
										<option value="BH">Bahrain</option>
										<option value="BD">Bangladesh</option>
										<option value="BB">Barbados</option>
										<option value="BY">Belarus</option>
										<option value="BE">Belgium</option>
										<option value="BZ">Belize</option>
										<option value="BJ">Benin</option>
										<option value="BM">Bermuda</option>
										<option value="BT">Bhutan</option>
										<option value="BO">Bolivia, Plurinational State of</option>
										<option value="BQ">Bonaire, Sint Eustatius and Saba</option>
										<option value="BA">Bosnia and Herzegovina</option>
										<option value="BW">Botswana</option>
										<option value="BV">Bouvet Island</option>
										<option value="BR">Brazil</option>
										<option value="IO">British Indian Ocean Territory</option>
										<option value="BN">Brunei Darussalam</option>
										<option value="BG">Bulgaria</option>
										<option value="BF">Burkina Faso</option>
										<option value="BI">Burundi</option>
										<option value="KH">Cambodia</option>
										<option value="CM">Cameroon</option>
										<option value="CA">Canada</option>
										<option value="CV">Cape Verde</option>
										<option value="KY">Cayman Islands</option>
										<option value="CF">Central African Republic</option>
										<option value="TD">Chad</option>
										<option value="CL">Chile</option>
										<option value="CN">China</option>
										<option value="CX">Christmas Island</option>
										<option value="CC">Cocos (Keeling) Islands</option>
										<option value="CO">Colombia</option>
										<option value="KM">Comoros</option>
										<option value="CG">Congo</option>
										<option value="CD">Congo, the Democratic Republic of the</option>
										<option value="CK">Cook Islands</option>
										<option value="CR">Costa Rica</option>
										<option value="CI">Côte d'Ivoire</option>
										<option value="HR">Croatia</option>
										<option value="CU">Cuba</option>
										<option value="CW">Curaçao</option>
										<option value="CY">Cyprus</option>
										<option value="CZ">Czech Republic</option>
										<option value="DK">Denmark</option>
										<option value="DJ">Djibouti</option>
										<option value="DM">Dominica</option>
										<option value="DO">Dominican Republic</option>
										<option value="EC">Ecuador</option>
										<option value="EG">Egypt</option>
										<option value="SV">El Salvador</option>
										<option value="GQ">Equatorial Guinea</option>
										<option value="ER">Eritrea</option>
										<option value="EE">Estonia</option>
										<option value="ET">Ethiopia</option>
										<option value="FK">Falkland Islands (Malvinas)</option>
										<option value="FO">Faroe Islands</option>
										<option value="FJ">Fiji</option>
										<option value="FI">Finland</option>
										<option value="FR">France</option>
										<option value="GF">French Guiana</option>
										<option value="PF">French Polynesia</option>
										<option value="TF">French Southern Territories</option>
										<option value="GA">Gabon</option>
										<option value="GM">Gambia</option>
										<option value="GE">Georgia</option>
										<option value="DE">Germany</option>
										<option value="GH">Ghana</option>
										<option value="GI">Gibraltar</option>
										<option value="GR">Greece</option>
										<option value="GL">Greenland</option>
										<option value="GD">Grenada</option>
										<option value="GP">Guadeloupe</option>
										<option value="GU">Guam</option>
										<option value="GT">Guatemala</option>
										<option value="GG">Guernsey</option>
										<option value="GN">Guinea</option>
										<option value="GW">Guinea-Bissau</option>
										<option value="GY">Guyana</option>
										<option value="HT">Haiti</option>
										<option value="HM">Heard Island and McDonald Islands</option>
										<option value="VA">Holy See (Vatican City State)</option>
										<option value="HN">Honduras</option>
										<option value="HK">Hong Kong</option>
										<option value="HU">Hungary</option>
										<option value="IS">Iceland</option>
										<option value="IN">India</option>
										<option value="ID">Indonesia</option>
										<option value="IR">Iran, Islamic Republic of</option>
										<option value="IQ">Iraq</option>
										<option value="IE">Ireland</option>
										<option value="IM">Isle of Man</option>
										<option value="IL">Israel</option>
										<option value="IT">Italy</option>
										<option value="JM">Jamaica</option>
										<option value="JP">Japan</option>
										<option value="JE">Jersey</option>
										<option value="JO">Jordan</option>
										<option value="KZ">Kazakhstan</option>
										<option value="KE">Kenya</option>
										<option value="KI">Kiribati</option>
										<option value="KP">Korea, Democratic People's Republic of</option>
										<option value="KR">Korea, Republic of</option>
										<option value="KW">Kuwait</option>
										<option value="KG">Kyrgyzstan</option>
										<option value="LA">Lao People's Democratic Republic</option>
										<option value="LV">Latvia</option>
										<option value="LB">Lebanon</option>
										<option value="LS">Lesotho</option>
										<option value="LR">Liberia</option>
										<option value="LY">Libya</option>
										<option value="LI">Liechtenstein</option>
										<option value="LT">Lithuania</option>
										<option value="LU">Luxembourg</option>
										<option value="MO">Macao</option>
										<option value="MK">Macedonia, the former Yugoslav Republic of</option>
										<option value="MG">Madagascar</option>
										<option value="MW">Malawi</option>
										<option value="MY">Malaysia</option>
										<option value="MV">Maldives</option>
										<option value="ML">Mali</option>
										<option value="MT">Malta</option>
										<option value="MH">Marshall Islands</option>
										<option value="MQ">Martinique</option>
										<option value="MR">Mauritania</option>
										<option value="MU">Mauritius</option>
										<option value="YT">Mayotte</option>
										<option value="MX">Mexico</option>
										<option value="FM">Micronesia, Federated States of</option>
										<option value="MD">Moldova, Republic of</option>
										<option value="MC">Monaco</option>
										<option value="MN">Mongolia</option>
										<option value="ME">Montenegro</option>
										<option value="MS">Montserrat</option>
										<option value="MA">Morocco</option>
										<option value="MZ">Mozambique</option>
										<option value="MM">Myanmar</option>
										<option value="NA">Namibia</option>
										<option value="NR">Nauru</option>
										<option value="NP">Nepal</option>
										<option value="NL">Netherlands</option>
										<option value="NC">New Caledonia</option>
										<option value="NZ">New Zealand</option>
										<option value="NI">Nicaragua</option>
										<option value="NE">Niger</option>
										<option value="NG">Nigeria</option>
										<option value="NU">Niue</option>
										<option value="NF">Norfolk Island</option>
										<option value="MP">Northern Mariana Islands</option>
										<option value="NO">Norway</option>
										<option value="OM">Oman</option>
										<option value="PK">Pakistan</option>
										<option value="PW">Palau</option>
										<option value="PS">Palestinian Territory, Occupied</option>
										<option value="PA">Panama</option>
										<option value="PG">Papua New Guinea</option>
										<option value="PY">Paraguay</option>
										<option value="PE">Peru</option>
										<option value="PH">Philippines</option>
										<option value="PN">Pitcairn</option>
										<option value="PL">Poland</option>
										<option value="PT">Portugal</option>
										<option value="PR">Puerto Rico</option>
										<option value="QA">Qatar</option>
										<option value="RE">Réunion</option>
										<option value="RO">Romania</option>
										<option value="RU">Russian Federation</option>
										<option value="RW">Rwanda</option>
										<option value="BL">Saint Barthélemy</option>
										<option value="SH">Saint Helena, Ascension and Tristan da Cunha</option>
										<option value="KN">Saint Kitts and Nevis</option>
										<option value="LC">Saint Lucia</option>
										<option value="MF">Saint Martin (French part)</option>
										<option value="PM">Saint Pierre and Miquelon</option>
										<option value="VC">Saint Vincent and the Grenadines</option>
										<option value="WS">Samoa</option>
										<option value="SM">San Marino</option>
										<option value="ST">Sao Tome and Principe</option>
										<option value="SA">Saudi Arabia</option>
										<option value="SN">Senegal</option>
										<option value="RS">Serbia</option>
										<option value="SC">Seychelles</option>
										<option value="SL">Sierra Leone</option>
										<option value="SG">Singapore</option>
										<option value="SX">Sint Maarten (Dutch part)</option>
										<option value="SK">Slovakia</option>
										<option value="SI">Slovenia</option>
										<option value="SB">Solomon Islands</option>
										<option value="SO">Somalia</option>
										<option value="ZA">South Africa</option>
										<option value="GS">South Georgia and the South Sandwich Islands</option>
										<option value="SS">South Sudan</option>
										<option value="ES">Spain</option>
										<option value="LK">Sri Lanka</option>
										<option value="SD">Sudan</option>
										<option value="SR">Suriname</option>
										<option value="SJ">Svalbard and Jan Mayen</option>
										<option value="SZ">Swaziland</option>
										<option value="SE">Sweden</option>
										<option value="CH">Switzerland</option>
										<option value="SY">Syrian Arab Republic</option>
										<option value="TW">Taiwan, Province of China</option>
										<option value="TJ">Tajikistan</option>
										<option value="TZ">Tanzania, United Republic of</option>
										<option value="TH">Thailand</option>
										<option value="TL">Timor-Leste</option>
										<option value="TG">Togo</option>
										<option value="TK">Tokelau</option>
										<option value="TO">Tonga</option>
										<option value="TT">Trinidad and Tobago</option>
										<option value="TN">Tunisia</option>
										<option value="TR">Turkey</option>
										<option value="TM">Turkmenistan</option>
										<option value="TC">Turks and Caicos Islands</option>
										<option value="TV">Tuvalu</option>
										<option value="UG">Uganda</option>
										<option value="UA">Ukraine</option>
										<option value="AE">United Arab Emirates</option>
										<option value="GB">United Kingdom</option>
										<option value="US" selected="selected">United States</option>
										<option value="UM">United States Minor Outlying Islands</option>
										<option value="UY">Uruguay</option>
										<option value="UZ">Uzbekistan</option>
										<option value="VU">Vanuatu</option>
										<option value="VE">Venezuela, Bolivarian Republic of</option>
										<option value="VN">Viet Nam</option>
										<option value="VG">Virgin Islands, British</option>
										<option value="VI">Virgin Islands, U.S.</option>
										<option value="WF">Wallis and Futuna</option>
										<option value="EH">Western Sahara</option>
										<option value="YE">Yemen</option>
										<option value="ZM">Zambia</option>
										<option value="ZW">Zimbabwe</option>
									</select>
								</div>
							</label>
						</fieldset>
						<!-- <p class="tip">Select another country to see different payment options.</p> -->
					</section>
					<section>
						<h2>Payment Information</h2>
						<nav id="payment-methods">
							<ul>
								<li>
									<input type="radio" name="payment" id="payment-card" value="card" checked />
									<label for="payment-card">Card</label>
								</li>
								<li>
									<input type="radio" name="payment" id="payment-ach_credit_transfer" value="ach_credit_transfer" checked />
									<label for="payment-ach_credit_transfer">Bank Transfer</label>
								</li>
								<li>
									<input type="radio" name="payment" id="payment-alipay" value="alipay" />
									<label for="payment-alipay">Alipay</label>
								</li>
								<li>
									<input type="radio" name="payment" id="payment-bancontact" value="bancontact" />
									<label for="payment-bancontact">Bancontact</label>
								</li>
								<li>
									<input type="radio" name="payment" id="payment-eps" value="eps" />
									<label for="payment-eps">EPS</label>
								</li>
								<li>
									<input type="radio" name="payment" id="payment-ideal" value="ideal" />
									<label for="payment-ideal">iDEAL</label>
								</li>
								<li>
									<input type="radio" name="payment" id="payment-giropay" value="giropay" />
									<label for="payment-giropay">Giropay</label>
								</li>
								<li>
									<input type="radio" name="payment" id="payment-multibanco" value="multibanco" />
									<label for="payment-multibanco">Multibanco</label>
								</li>
								<li>
									<input type="radio" name="payment" id="payment-sepa_debit" value="sepa_debit" />
									<label for="payment-sepa_debit">SEPA Direct Debit</label>
								</li>
								<li>
									<input type="radio" name="payment" id="payment-sofort" value="sofort" />
									<label for="payment-sofort">SOFORT</label>
								</li>
								<li>
									<input type="radio" name="payment" id="payment-wechat" value="wechat" />
									<label for="payment-wechat">WeChat Pay</label>
								</li>
							</ul>
						</nav>
						<div class="payment-info card visible">
							<fieldset>
								<label>
									<span>Card</span>
									<div id="card-element" class="field"></div>
								</label>
							</fieldset>
						</div>
						<div class="payment-info sepa_debit">
							<fieldset>
								<label>
									<span>IBAN</span>
									<div id="iban-element" class="field"></div>
								</label>
							</fieldset>
							<p class="notice">
								By providing your IBAN and confirming this payment, you’re authorizing Payments Demo and Stripe, our payment provider, to send instructions to your bank to debit your account. You’re entitled to a refund
								under the terms and conditions of your agreement with your bank.
							</p>
						</div>
						<div class="payment-info ideal">
							<fieldset>
								<label>
									<span>iDEAL Bank</span>
									<div id="ideal-bank-element" class="field"></div>
								</label>
							</fieldset>
						</div>
						<div class="payment-info redirect">
							<p class="notice">You’ll be redirected to the banking site to complete your payment.</p>
						</div>
						<div class="payment-info receiver">
							<p class="notice">Payment information will be provided after you place the order.</p>
						</div>
						<div class="payment-info wechat">
							<div id="wechat-qrcode"></div>
							<p class="notice">Click the button below to generate a QR code for WeChat.</p>
						</div>
					</section>
					<button class="payment-button" type="submit">Start Trial</button>
				</form>
				<div id="card-errors" class="element-errors"></div>
				<div id="iban-errors" class="element-errors"></div>
			</div>
			<div id="confirmation">
				<div class="status processing">
					<h1>Completing your order…</h1>
					<p>We’re just waiting for the confirmation from your bank… This might take a moment but feel free to close this page.</p>
					<p>We’ll send your receipt via email shortly.</p>
				</div>
				<div class="status success">
					<h1>Thanks for your order!</h1>
					<p>Woot! You successfully made a payment with Stripe.</p>
					<p class="note">We just sent your receipt to your email address, and your items will be on their way shortly.</p>
				</div>
				<div class="status receiver">
					<h1>Thanks! One last step!</h1>
					<p>Please make a payment using the details below to complete your order.</p>
					<div class="info"></div>
				</div>
				<div class="status error">
					<h1>Oops, payment failed.</h1>
					<p>It looks like your payment was failed at this time. <strong>Please refresh </strong> and try again or select a different payment option.</p>
					<p class="error-message"></p>
				</div>
			</div>
		</main>
		<aside id="summary">
			<div class="header">
				<h1>Subscribing to <span style="font-weight: 700;font-size: 20px;">Starter</span> plan</h1>
			</div>
			<div id="order-total">
				<div class="line-item">
					<p class="label">Amount</p>
					<p class="price" data-subtotal="">$12.00 /per month</p>
				</div>
				<div class="line-item shipping">
					<p class="label">Billed Monthly</p>
					<p class="price"></p>
				</div>
				<div class="line-item demo">
					<div id="demo">
						<p class="label"><strong style="color:red">Amex Cards are not supported</strong></p>
						<p class="note">See the <a style="color:#666ee8" href="<?=env('APP_URL')?>/buy/professional">Professional Plan</a></p>
					</div>
				</div>
				<div class="line-item total">
					<p class="label">Total</p>
					<p class="price" data-total="">$59.00</p>
				</div>
			</div>
		</aside>

		<!-- Stripe.js v3 for Elements -->
		<script src="https://js.stripe.com/v3/"></script>
		<!-- Library to render QR codes -->
		<script src="https://cdn.rawgit.com/davidshimjs/qrcodejs/gh-pages/qrcode.min.js" integrity="sha384-3zSEDfvllQohrq0PHL1fOXJuC/jSOO34H46t6UQfobFOmxE5BpjjaIJY5F2/bMnU" crossorigin="anonymous"></script>
		<!-- Library for generating fake user information -->
		<script src="https://cdnjs.cloudflare.com/ajax/libs/Faker/3.1.0/faker.min.js" integrity="sha384-PlFzuf6GOlJNxLuosezJ/jwndIVZ2hWI/AmvYQtBzstOdLtcUe6DPSI4LsqwiN1y" crossorigin="anonymous"></script>
		<!-- App -->
		<script src="<?=env('APP_URL')?>/stripe/javascripts/store.js"></script>
		<script src="<?=env('APP_URL')?>/stripe/javascripts/payments.js"></script>
	</body>
</html>
