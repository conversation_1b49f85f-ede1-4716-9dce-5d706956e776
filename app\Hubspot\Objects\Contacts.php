<?php

namespace App\Hubspot\Objects;

use Log;
use Exception;
use HubSpot\Client\Crm\Contacts\Model\SimplePublicObjectInput;
use HubSpot\Client\Crm\Contacts\Model\SimplePublicObjectBatchInput;
use HubSpot\Client\Crm\Contacts\Model\BatchInputSimplePublicObjectBatchInput;
use HubSpot\Client\CommunicationPreferences\Model\PublicUpdateSubscriptionStatusRequest;

class Contacts extends Objects
{
    public function __construct(
        protected $client,
        protected string $requestId = '',
    ) {
        parent::__construct($client, 'contacts');
    }

    public function getById(
        int $objectId,
        array $properties = [],
        ?string $properties_with_history = null,
        ?string $associations = null
    ) {
        try {
            $response = $this->client->crm()
                ->contacts()
                ->basicApi()
                ->getById($objectId, $properties, $properties_with_history, $associations);

            return json_decode(json_encode($response));
        } catch (Exception $e) {
            Log::error("[Contacts:getById] $this->requestId, Exception: ".$e->getMessage());

            return null;
        }
    }

    public function create(array $properties)
    {
        try {
            $newProperties = new SimplePublicObjectInput;
            $newProperties->setProperties($properties);

            $response = $this->client->crm()->contacts()->basicApi()->create($newProperties);

            return json_decode(json_encode($response));
        } catch (Exception $e) {
            Log::error("[Contacts:create] $this->requestId, Exception: ".$e->getMessage());

            return null;
        }
    }

    public function update(int $objectId, array $properties = [])
    {
        try {
            $newProperties = new SimplePublicObjectInput;
            $newProperties->setProperties($properties);

            $response = $this->client->crm()->contacts()->basicApi()->update($objectId, $newProperties);

            return json_decode(json_encode($response));
        } catch (Exception $e) {
            Log::error("[Contacts:update] $this->requestId, Exception: ".$e->getMessage());

            return null;
        }
    }

    public function updateBatch(array $contacts, array $properties)
    {
        try {
            $inputArray = [];
            $batchInput = new BatchInputSimplePublicObjectBatchInput;

            foreach ($contacts as $contact) {
                $objectId = $contact->id;

                $simplePublicObjectInput = new SimplePublicObjectBatchInput;
                $simplePublicObjectInput->setId($objectId);
                $simplePublicObjectInput->setProperties($properties);

                $inputArray[] = $simplePublicObjectInput;
            }

            $batchInput->setInputs($inputArray);

            $response = $this->client->crm()->contacts()->batchApi()->update($batchInput);

            return json_decode(json_encode($response));
        } catch (Exception $e) {
            Log::error("[Contacts:updateBatch] $this->requestId, Exception: ".$e->getMessage());

            return null;
        }
    }

    public function fetchBatchContactList(
        array $contactIds,
        array $additionalProperties = []
    ) {
        try {
            $properties = ['firstname', 'lastname', 'email', 'phone', 'mobilephone'];
            if (! empty($additionalProperties)) {
                $properties = array_merge($properties, $additionalProperties);
            }

            $batchReadInput = new \HubSpot\Client\Crm\Contacts\Model\BatchReadInputSimplePublicObjectId;
            $batchReadInput->setProperties($properties);
            $batchReadInput->setInputs(array_map(function ($id) {
                return ['id' => $id];
            }, $contactIds));

            $response = $this->client->crm()->contacts()->batchApi()->read($batchReadInput);

            return json_decode(json_encode($response->getResults()));
        } catch (Exception $e) {
            Log::error("[Contacts:fetchBatchContactList] $this->requestId, Exception: ".$e->getMessage());

            return false;
        }
    }

    public function search(array $data)
    {
        return $this->doSearch($data);
    }

    public function getProperties()
    {
        return $this->getAllProperties();
    }

    public function getSubscriptions()
    {
        try {

            $response = $this->client->communicationPreferences()->definitionApi()->getPage();

            return json_decode(json_encode($response));

        } catch (Exception $e) {
            if ($e instanceof \HubSpot\Client\CommunicationPreferences\ApiException) {
                Log::error("[Contacts:subscribeToWhatsApp] $this->requestId, ApiException: ".$e->getResponseBody());
            } else {
                Log::error("[Contacts:subscribeToWhatsApp] $this->requestId, Exception: ".$e->getMessage());
            }

            return null;
        }
    }

    public function subscribeToWhatsApp(int $objectId)
    {
        try {
            $contact = $this->getById($objectId);
            $email = $contact->properties->email;

            $request = new PublicUpdateSubscriptionStatusRequest([
                'email_address' => $email,
                'subscription_id' => 'whatsapp',
                'legal_basis' => 'LEGITIMATE_INTEREST_CLIENT',
                'legal_basis_explanation' => 'whatsapp consent',
            ]);
            $response = $this->client->communicationPreferences()->statusApi()->subscribe($request);

            return json_decode(json_encode($response));

        } catch (Exception $e) {
            if ($e instanceof \HubSpot\Client\CommunicationPreferences\ApiException) {
                Log::error("[Contacts:subscribeToWhatsApp] $this->requestId, ApiException: ".$e->getResponseBody());
            } else {
                Log::error("[Contacts:subscribeToWhatsApp] $this->requestId, Exception: ".$e->getMessage());
            }

            return null;
        }
    }
}
