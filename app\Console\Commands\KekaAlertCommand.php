<?php

namespace App\Console\Commands;

use App\Helpers\Slack;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class KekaAlertCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keka:alert';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Keka leave alert on Niswey';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $slack = new Slack(env('SLACK_TOKEN'));

        $kekaApiUrl = 'https://niswey.keka.com/k/dashboard/api/dashboard/teamleaves';
        $response = Http::withToken(env('KEKA_TOKEN'))->get($kekaApiUrl)->object();
        if (! isset($response->succeeded) || ! $response->succeeded) {
            return true;
        }

        if (! $response->data) {
            return true;
        }

        $message = $this->buildMessage($response->data);
        $slack->postMessage([
            'channel' => 'testing',
            'text' => $message,
        ]);
    }

    public function buildMessage($data)
    {
        $message = ':away: *On Leave Today* :away:';
        foreach ($data as $item) {
            $message .= "\n>".$item->displayName;
        }

        return $message;
    }
}
