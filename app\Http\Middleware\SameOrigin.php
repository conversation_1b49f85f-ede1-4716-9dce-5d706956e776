<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SameOrigin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $postman = null;
        $secFetchSite = $request->headers->get('sec-fetch-site');
        if (env('ALLOW_POSTMAN') == 'yes') {
            $postman = $request->headers->get('Postman-Token');
        }

        if (app()->isProduction() && ! $postman && $secFetchSite != 'same-origin') {
            return response('Forbidden', Response::HTTP_FORBIDDEN);
        }

        return $next($request);
    }
}
