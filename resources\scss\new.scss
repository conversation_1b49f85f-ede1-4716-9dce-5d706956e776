html, body {
	margin: 0;
	padding: 0;
	font-family: 'Mont<PERSON><PERSON>', sans-serif;
}
* {
	font: inherit;
}
header {
    padding: 1.15rem 0;
}
.container {
    max-width: 75rem;
    margin: 0 auto;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}
.rightcontent img {
    width: 100%;
}

@media screen and (min-width: 992px) {
    .aside {
        display: inline-flex;
        width: 100%;
    }
    .bcontainer {
        width: 48%;
        margin-right: 20%;
    }
    .box2 {
        width: 32%;
    }
    .acontainer {
        width: 50%;
    }
    .custom-progress-connector {
        top: 8px;
    }
}

@media screen and (max-width: 991px) {
    .box,
    .bcontainer {
        width: 100%;
    }
    .bcontainer {
        width: 100%;
    }
    .box2 {
        width: 60%;
    }
    .acontainer {
        width: 100%;
    }
    .custom-progress-connector {
        top: 14px;
    }
}
@media screen and (min-width: 322px) and (max-width: 599px) {
    .accordion {
        font-size: 20px;
        line-height: 30px;
    }
    button.accordion.active::after {
        background-position: 0 0;
    }
    button.accordion::after {
        background-position: 0 0;
    }
    .btn1 {
        width: 100% !important;
    }
    .custom-progress-step::after {
        font-size: 9px !important;
        line-height: 19px !important;
    }
}

@media screen and (max-width: 321px) {
    .accordion {
        font-size: 20px;
        line-height: 30px;
    }
    button.accordion.active::after {
        background-position: 0 0;
    }
    button.accordion::after {
        background-position: 0 0;
    }
    .btn1 {
        width: 100% !important;
    }
    .custom-progress-step::after {
        font-size: 7px !important;
        line-height: 17px !important;
    }
}

@media screen and (min-width: 600px) {
    .accordion {
        font-size: 26px;
        line-height: 36px;
    }
    button.accordion.active::after {
        background-position: 0 4px !important;
    }
    button.accordion::after {
        background-position: 0 4px !important;
    }
}

@media screen and (min-width: 768px) {
    .container2 {
        line-height: 35px;
        font-size: 25px;
    }
    .container1 {
        font-size: 40px;
        line-height: 50px;
    }
    .flex-container {
        display: flex;
        /* justify-content:center; */
        align-items: center;
    }
    .leftcontent,
    .rightcontent {
        width: 50%;
    }
}
p {
    font-size: 22px;
}
.container2 {
    padding: 13px 20px 20px !important;
}

@media screen and (max-width: 767px) {
    .flex-container {
        display: block !important;
    }
    .leftcontent,
    .rightcontent {
        width: 100%;
    }
    .leftcontent {
        margin-bottom: 20px;
    }
    .container1 {
        font-size: 33px;
        line-height: 0px;
    }
    .container2 {
        line-height: 30px;
        font-size: 20px;
    }
}
@media screen and (max-width: 1199px) {
    .leftcontent h1 {
    	font-weight: 700;
        font-size: 2.5em;
        margin-bottom: 2rem;
    }
    .leftcontent p {
        font-size: 1.6rem;
    }
}
@media screen and (min-width: 1200px) {
    .leftcontent h1 {
    	font-weight: 700;
        font-size: 3rem;
    	margin-bottom: 2rem;
    }
    .leftcontent p {
        font-size: 1.6rem;
    }
}

h1 {
    font-weight: 700;
    color: #33475b;
}

p {
    font-weight: 500;
    color: #516f90;
}

.margin_top {
    margin-top: 4rem;
}
.margin_bottom {
    margin-bottom: 4rem;
}
.custom-progress-bar {
    position: relative;
    display: flex;
    justify-content: space-between;
    counter-reset: step;
    margin: 0 3rem;
}
.custom-progress-step {
    width: 30px;
    height: 28px;
    border-radius: 50%;
    background: #fff;
    border: 3px solid #a29999;
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    text-align: center;
}
.custom-progress-step::before {
    counter-increment: step;
}

.custom-progress-step::after {
    content: attr(data-title);
    position: absolute;
    top: calc(100% + 0.5rem);
    font-size: 1rem;
    color: #516f90;
    width: auto;
}

.custom-progress-connector {
    position: relative;
    transform: translateY(-50%);
    height: 4px;
    width: 50%;
    background-color: #dcdcdc;
    z-index: -1;
    margin-top: 1.1%;
}

.custom-progress-connector.active {
    background-color: #ff725e !important;
}

.disabled {
    pointer-events: none;
    opacity: 0.2;
}

.custom-progress-step-active {
    border: 0.15rem solid #ff725e;
}
.btn1 {
    border: 2px;
    padding: 0.6rem 1.5rem;
    background: #ff725e;
    border-radius: 0.25rem;
    font-size: 1.3rem;
    color: #ffffff;
    cursor: pointer;
    font-family: "Montserrat";
}

.custom-progress-step-complete::before {
    content: "\2713\0020";
    color: #fff;
}

.custom-progress-step-complete {
    background-color: #ff725e;
    border: 0.15rem solid #ff725e;
}

.acontent {
    width: 100%;
    font-family: "Montserrat";
    font-style: normal;
    font-size: 20px;
    line-height: 30px;
    color: #33475b;
}
.acontent a {
    padding-top: 10px;
    display: inline-block;
}
.panel h2 {
    font-family: "Montserrat";
    font-size: 22px;
    line-height: 32px;
    color: #33475b;
    margin: 0;
}
.panel h3 {
    font-family: "Montserrat";
    font-size: 0.65rem;
    color: #33475b;
}
#a {
    position: absolute;
    font-family: "Montserrat";
    font-style: normal;
    color: #0aa0bf;
}
.acontainer {
    background-color: #fff;
    border-radius: 0.3rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}
.form {
    padding-top: 25px;
}
.form-control-new {
    margin-bottom: 10px;
    position: relative;
    padding: 0px 20px 20px 20px;
}
.form-control-new label {
    color: #516f90;
    display: block;
    margin: 0;
    padding-bottom: 10px;
}
.form-control-new input {
    color: #000000;
    display: block;
    width: 100%;
    padding: 0.75rem;
    font-size: 0.96rem;
    background: #f5f8fa;
    border: 1px solid #cbd6e2;
    box-sizing: border-box;
    border-radius: 0.2rem;
    font-family: "Montserrat";
}

.form-control-new input:focus {
    outline: 0;
    border-color: #777;
}

.form-control-new.success input {
    border-color: var(--success-color);
}

.form-control-new.error input {
    border-color: var(--error-color);
}

.form-control-new small {
    color: var(--error-color);
    position: absolute;
    bottom: 0;
    left: 0;
    visibility: hidden;
}

.form-control-new.error small {
    visibility: visible;
}

.btnmain {
    cursor: pointer;
    background-color: #f5f8fa;
    color: #fff;
    display: block;
    font-size: 1.75rem;
    padding: 20px;
}

.small {
    width: 9rem;
    height: 3rem;
    background: #ff725e;
    color: #fff;
    border-radius: 4px;
}

.paratext {
    font-weight: 400;
    font-size: 1.5rem;
    line-height: 2.5rem;
    color: #33475b;
    margin: 5px 0 15px;
}

.overlay {
    overflow: hidden;
    width: 1334px;
}

.container1 {
    font-weight: 500;
    color: #ffffff;
    display: flex;
    justify-content: center;
    letter-spacing: 0.04em;
    background: linear-gradient(263.43deg, rgba(10, 160, 191, 0.2) 0.37%, rgba(0, 0, 0, 0) 48.78%), linear-gradient(96.51deg, #0aa0bf 2.85%, rgba(6, 179, 75, 0.2) 223.16%);
    padding: 10px;
}
.container2 {
    padding: 20px 20px 30px;
    font-family: "Montserrat";
    font-style: normal;

    justify-content: center;
    text-align: center;
    letter-spacing: 0.04em;
    background: #ffffff;
}
.btnn {
    width: 330px;
    height: 64px;
    font-size: 20px;
    background: #ff725e;
    color: #ffffff;
    border-radius: 4px;
}

.accordion {
    margin-top: 1rem;
    background: #ffffff;
    border: 2px solid #7fd1de;
    box-sizing: border-box;
    box-shadow: 0px 3px 10px 5px rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    cursor: pointer;
    color: #666;
    padding: 15px 18px;
    width: 100%;
    text-align: left;
    outline: none;
    transition: 0.4s;
    font-family: "Montserrat";
}

.accordion.active {
    background: linear-gradient(263.43deg, rgba(10, 160, 191, 0.2) 0.37%, rgba(0, 0, 0, 0) 48.78%), linear-gradient(96.51deg, #0aa0bf 2.85%, rgba(6, 179, 75, 0.2) 223.16%);
    border: 2px solid #7fd1de;
    color: #ffffff;
    box-sizing: border-box;
    box-shadow: 0px 3px 10px 5px rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    opacity: 1;
    pointer-events: all;
}
.panelbox {
    padding-bottom: 30px;
    padding-top: 20px;
}

.panel {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    background-color: white;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.2s ease-out;
    box-shadow: 0px 3px 10px 5px rgba(0, 0, 0, 5%);
}
.panel.active {
	max-height: 100%;
}
.accwrap {
    margin-top: 6rem;
    display: inine-block;
}

.bcontent {
    width: 100%;
    left: 2rem;
    top: 0.25rem;
    font-family: "Montserrat";
    font-style: normal;
    font-size: 1.25rem;
    line-height: 3rem;
    color: #33475b;
}
.panel h4 {
    font-family: "Montserrat";
    font-size: 22px;
    line-height: 32px;
    padding-bottom: 20px;
    color: #33475b;
    margin: 0;
}
.panel h5 {
    font-family: "Montserrat";
    font-size: 20px;
    line-height: 26px;
    padding-bottom: 0;
    margin: 0;
    color: #33475b;
}
.panel h6 {
    font-family: "Montserrat";
    font-size: 18px;
    line-height: 28px;
    margin: 0;
    padding-top: 25px;
    padding-bottom: 10px;
    color: #33475b;
}
.bcontainer {
    background-color: #fff;
    border-radius: 3px;
}
.ccontainer {
    background-color: #fff;
    border-radius: 3px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    width: 100%;
    margin-top: 0.5rem;
}
.box {
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    width: 80%;
    margin: 20px 0 0;
    padding: 8% 6%;
}
.para1 {
    font-family: "Montserrat";
    font-size: 24px;
    line-height: 34px;
    color: #516f90;
    padding-bottom: 15px;
    margin: 0;
}
.para2 {
    font-family: "Montserrat";
    font-size: 30px;
    line-height: 40px;
    color: #ff725e;
    font-weight: bold;
    padding-bottom: 0;
    margin: 0;
}
.para3 {
    font-family: "Montserrat";
    font-size: 16px;
    line-height: 26px;
    color: #516f90;
    margin: 0;
}
.box2 {
    margin-top: 30px;
}

button.accordion.active::after {
    content: "\25bc";
    font-size: 0;
    background: url("/img/active-arrow.png");
    width: 24px;
    height: 24px;
    margin-top: 3px;
    background-repeat: no-repeat;
}
button.accordion::after {
    content: "\25bc";
    font-size: 0;
    background: url("/img/not-active-arrow.png");
    color: #777;
    font-weight: bold;
    float: right;
    margin-left: 5px;
    width: 24px;
    height: 24px;
    margin-top: 3px;
    background-repeat: no-repeat;
}
button.accordion.active::before {
    content: "\25bc";
    font-size: 0;
    background: url("/img/active-down-arrow.png") 0 0;
    width: 32px;
    height: 32px;
    margin-top: 3px;
    background-repeat: no-repeat;
}
button.accordion::before {
    content: "\25bc";
    font-size: 0;
    background: url("/img/not-active-down-arrow.png") 0 0;
    color: #777;
    font-weight: bold;
    float: left;
    margin-right: 10px;
    width: 32px;
    height: 32px;
    margin-top: 3px;
    background-repeat: no-repeat;
}
button.accordion.completed::before {
    content: "\25bc";
    font-size: 0;
    background: url("/img/not-active-check.png") 0 0;
    color: #777;
    font-weight: bold;
    float: left;
    margin-right: 10px;
    width: 32px;
    height: 32px;
    margin-top: 3px;
    background-repeat: no-repeat;
}
.imgballoon {
    text-align: center;
    padding: 40px 20px;
}
.imgballoon img {
    margin: 0 auto;
}
.imgballoon h2 {
    margin: 30px 0 0 0;
}


.close {
    color: #fff;
    position: absolute;
    float: right;
    right: 10px;
    font-size: 40px;
    font-weight: 500;
}

.close:hover,
.close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}
.f-btn {
    padding: 40px 20px;
    font-size: 1rem;
    border-top: 1px solid #cedded;
}

.paratext a {
    color: #0aa0bf;
}
button:disabled {
	opacity: .5;
}

.list-group-item-info {
    background-color: #F3E5B3 !important;
}
.rightcontent img {
	max-width: 510px
}
