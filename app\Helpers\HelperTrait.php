<?php

namespace App\Helpers;

use Log;
use Exception;
use App\Models\Dialog;
use App\Models\Message;

trait HelperTrait
{
    public function updateDialog($where, $data)
    {
        try {
            Dialog::where($where)->update($data);

            return true;
        } catch (Exception $e) {
            Log::error('[HelperTrait:updateDialog] Exception: '.$e->getMessage()."\n".$e->getTraceAsString());

            return false;
        }
    }

    public function saveMessage($message)
    {
        try {
            return Message::create($message);
        } catch (Exception $e) {
            Log::error('[HelperTrait:saveMessage] Exception: '.$e->getMessage()."\n".$e->getTraceAsString());

            return false;
        }
    }

    public function notPaidResponse($hsApp)
    {
        return [
            'results' => [
                [
                    'objectId' => $hsApp['config']['objectId'],
                    'title' => 'Portal not Activated',
                    'properties' => [
                        [
                            'label' => 'Contact support to activate your Account',
                            'dataType' => 'STRING',
                            'value' => '<EMAIL>',
                        ],
                    ],
                ],
            ],
        ];
    }

    public function buildReceiveEvent($users, $options)
    {
        $hsApp = $this->hubspotApp->hsApp;
        $eventTypeId = $hsApp['config']['message_recieved'];

        if (count($users) == 1) {
            $user = $users[0];
            $data = [
                'id' => $options['messageId'],
                'eventTypeId' => $eventTypeId,
                'user' => $user['name'],
                'message' => $options['message'],
                'objectId' => $user['objectId'],
                'phone' => $options['phone'],
            ];
        } else {
            $count = 0;
            $data['eventWrappers'] = [];
            foreach ($users as $user) {
                $event = [
                    'id' => $options['messageId'].$count,
                    'eventTypeId' => $eventTypeId,
                    'user' => $user['name'],
                    'message' => $options['message'],
                    'objectId' => $user['objectId'],
                    'phone' => $options['phone'],
                ];
                $data['eventWrappers'][] = $event;
                $count++;
            }
        }

        return $data;
    }

    public function buildSendEvent($users, $options)
    {
        $hsApp = $this->hubspotApp->hsApp;
        $eventTypeId = $hsApp['config']['message_sent'];

        if (count($users) == 1) {
            $data = [
                'id' => $options['messageId'],
                'eventTypeId' => $eventTypeId,
                'status' => $options['status'] ?? 'sent',
                'message' => $options['message'],
                'objectId' => $users[0]['objectId'],
                'phone' => $options['account'] ?? null,
            ];
        } else {
            $count = 0;
            $data['eventWrappers'] = [];
            foreach ($users as $user) {
                $event = [
                    'id' => $options['messageId'].$count,
                    'eventTypeId' => $eventTypeId,
                    'status' => $options['status'] ?? 'sent',
                    'message' => $options['message'],
                    'objectId' => $user['objectId'],
                    'phone' => $options['account'] ?? null,
                ];
                $data['eventWrappers'][] = $event;
                $count++;
            }
        }

        return $data;
    }

    public function buildStatusEvent($users, $options, $extraEvents = [])
    {
        $hsApp = $this->hubspotApp->hsApp;
        $type = $options['type'] ?? '';
        $eventTypeId = $hsApp['config']['message_sent'];
        $type && ($type == 'sms') && $eventTypeId = $hsApp['config']['sms_sent'];

        if (count($users) == 1) {
            $data = array_merge([
                'id' => $options['messageId'],
                'eventTypeId' => $eventTypeId,
                'phone' => $options['accountPhone'],
                'status' => $options['status'],
                'message' => $options['message'],
                'objectId' => $users[0]['objectId'],
            ], $extraEvents);
        } else {
            $count = 0;
            $data['eventWrappers'] = [];
            foreach ($users as $user) {
                $event = array_merge([
                    'id' => $options['messageId'].$count,
                    'eventTypeId' => $eventTypeId,
                    'phone' => $options['accountPhone'],
                    'status' => $options['status'],
                    'message' => $options['message'],
                    'objectId' => $user['objectId'],
                ], $extraEvents);
                $data['eventWrappers'][] = $event;
                $count++;
            }
        }

        return $data;
    }

    public function filterUsers($contacts)
    {
        $users = [];
        if (! $contacts) {
            return $users;
        }

        foreach ($contacts as $contact) {
            $firstname = $contact->properties->firstname->value ?? '';
            $lastname = $contact->properties->lastname->value ?? '';
            $name = $firstname;
            if ($lastname) {
                $name .= ' '.$lastname;
            }
            $phone = $contact->properties->phone->value ?? '';
            $users[] = [
                'phone' => $phone,
                'name' => $name ? $name : null,
                'email' => $contact->properties->email->value ?? '',
                'owner_id' => $contact->properties->hubspot_owner_id->value ?? '',
                'objectId' => $contact->vid,
            ];
        }

        return $users;
    }

    public function prepareContact($phone, $name)
    {
        if (strpos($phone, '+') === false) {
            $phone = '+'.$phone;
        }
        $nameArray = explode(' ', $name);
        $firstname = $nameArray[0] ?? '';
        $lastname = $nameArray[1] ?? '';

        $propArray = ['phone' => $phone];
        $firstname && $propArray['firstname'] = $firstname;
        $lastname && $propArray['lastname'] = $lastname;

        return $propArray;
    }

    public function getWorkflowTemplates($accountId, $type = 'text')
    {
        $filteredTemplates = [];
        $idParts = explode('.', $accountId);

        try {
            $templates = $this->templateModel::where([
                'portal_id' => $idParts[0],
                'ib_sender' => $idParts[1],
                'type' => $type,
            ])->get();
            if (! $templates) {
                return $filteredTemplates;
            }

            foreach ($templates as $template) {
                $filteredTemplates[] = ['label' => $template->name, 'value' => $template->name];
            }

            return $filteredTemplates;
        } catch (Exception $e) {
            Log::error('[HelperTrait:getWorkflowTemplates] Exception: '.$e->getMessage());

            return $filteredTemplates;
        }
    }

    public function getMediaTypes($accountId, $template)
    {
        $types = [];
        $idParts = explode('.', $accountId);
        $template = $this->templateModel::where(['portal_id' => $idParts[0], 'ib_sender' => $idParts[1], 'name' => $template])->first();
        if (! $template) {
            return [];
        }

        $structure = json_decode($template->structure);
        if (isset($structure->header)) {
            $headerType = $structure->header->format;
            $types[] = ['label' => $headerType, 'value' => strtolower($headerType)];
        }
        if (! $types) {
            $types[] = ['label' => 'NA', 'value' => 'NA'];
        }

        // handle dynamic templates later
        // if(isset($template->buttons)) {
        //  $button = $template->buttons[0] ?? [];
        //  $buttonType = $button['type'] ?? '';
        //  if($buttonType && $buttonType != 'QUICK_REPLY') {
        //      $types[] = ['label' => 'Dynamic Button', 'value' => 'dynamic_button'];
        //  }
        // }
        return $types;
    }

    public function getLabelValidation()
    {
        return ['name' => 'required', 'color' => 'required', 'sid' => 'required'];
    }

    public function getPhoneFromProps($properties)
    {
        $phone = '';
        $phoneKeys = ['phone', 'mobilephone'];
        foreach ($properties as $key => $value) {
            if (! in_array($key, $phoneKeys) || ! $value) {
                continue;
            } // only check for phone
            $phone = $value;

            break;
        }

        return $phone;
    }

    public function getOwnerFromProps($properties)
    {
        return $properties['hubspot_owner_id'] ?? null;
    }

    public function getNameFromProps($properties)
    {
        $name = [];
        $nameKeys = ['firstname', 'lastname'];
        foreach ($properties as $key => $value) {
            if (! in_array($key, $nameKeys)) {
                continue;
            } // only check for name
            $name[] = $value;
        }

        return $name ? implode(' ', $name) : null;
    }

    public function getProfile($properties)
    {
        $profile = [];
        $nameKeys = ['firstname', 'lastname', 'email'];
        foreach ($properties as $key => $value) {
            if (! in_array($key, $nameKeys)) {
                continue;
            }
            $profile[$key] = $value;
        }

        return $profile;
    }

    public function workflowAbleTemplates($templates, $type)
    {
        $filtered = [];
        foreach ($templates as $template) {
            if ($template->status != 'APPROVED') {
                continue;
            }

            $template = $this->buildTemplateParams($template, $type);

            if ($type == 'text' && $template->type != 'text') {
                continue;
            }

            if ($type != 'text' && $template->type == 'text') {
                continue;
            }

            $filtered[] = ['label' => $template->name.' - '.$template->body, 'value' => $template->id];
        }

        return $filtered;
    }

    public function buildTemplateParams($template, $defaultType = 'media')
    {
        if (! $template || ! isset($template->components)) {
            return $template;
        }

        $subType = null;
        $type = $defaultType;
        $bodyComponent = null;
        $headerComponent = null;
        $buttonComponent = null;

        foreach ($template->components as $component) {
            if ($component->type == 'HEADER') {
                $headerComponent = $component;
            }

            if ($component->type == 'BODY') {
                $bodyComponent = $component;
            }

            if ($component->type == 'BUTTONS') {
                $buttonComponent = $component->buttons;
                foreach ($component->buttons as $button) {
                    if ($button->type == 'FLOW') {
                        $subType = 'flow';
                        $buttonComponent = $component->buttons;

                        continue;
                    }

                    if ($button->type != 'URL') {
                        continue;
                    }

                    $buttonParamCount = preg_match_all('/\{\{\d+\}\}/', $button->url);
                    if (! $buttonParamCount) {
                        continue;
                    }

                    $type = 'buttons';
                    if ($headerComponent && isset($headerComponent->format)) {
                        $buttonComponent = $component->buttons;
                    }
                }
            }
        }

        $template->subType = $subType;
        $template->header = $headerComponent;
        $template->buttons = $buttonComponent;
        $template->params = preg_match_all('/\{\{\d+\}\}/', $bodyComponent->text);
        $template->body = $bodyComponent->text;
        $template->type = $headerComponent
                        ? strtolower($headerComponent->format)
                        : $type;
        $template->mediaType = $type;

        return $template;
    }

    public function analyzeTemplate($template)
    {
        if (! $template || ! isset($template->components)) {
            return $template;
        }

        $subType = null;
        $type = null;
        $bodyComponent = null;
        $headerComponent = null;
        $buttonComponent = null;
        $hasDynamicButton = false;
        $hasCopyButton = false;
        $dynamicButtonCount = 0;
        $mediaType = 'text';

        foreach ($template->components as $component) {
            if ($component->type == 'HEADER') {
                $headerComponent = $component;
                if (isset($component->format)) {
                    $type = $mediaType = strtolower($component->format);
                }
            }

            if ($component->type == 'BODY') {
                $bodyComponent = $component;
            }

            if ($component->type == 'BUTTONS') {
                $mediaType = 'buttons';
                $buttonComponent = $component->buttons;
                foreach ($component->buttons as $button) {
                    if ($button->type == 'FLOW') {
                        $subType = 'flow';
                    }

                    if ($button->type == 'COPY_CODE') {
                        $subType = 'copy_code';
                        $hasCopyButton = true;
                    }

                    if ($button->type == 'URL') {
                        $buttonUrl = $button->url ?? '';
                        $buttonParamCount = preg_match_all('/\{\{\d+\}\}/', $buttonUrl);
                        if ($buttonParamCount) {
                            $hasDynamicButton = true;
                            $dynamicButtonCount++;
                        }
                    }
                }
            }
        }

        // Determine type based on conditions
        if ($type) {
            if ($hasDynamicButton && ! $hasCopyButton) {
                $type = $type.'_buttons_dynamic_'.$dynamicButtonCount;
            } elseif ($hasDynamicButton && $hasCopyButton) {
                $type = $type.'_buttons_dynamic_'.$dynamicButtonCount.'_copy';
            } elseif (! $hasDynamicButton && $hasCopyButton) {
                $type = $type.'_buttons_copy';
            } elseif (! $hasDynamicButton && ! $hasCopyButton) {
                $type = $type;
            }
        } else {
            if ($hasDynamicButton && ! $hasCopyButton) {
                $type = 'buttons_dynamic_'.$dynamicButtonCount;
            } elseif ($hasDynamicButton && $hasCopyButton) {
                $type = 'buttons_dynamic_'.$dynamicButtonCount.'_copy';
            } elseif (! $hasDynamicButton && $hasCopyButton) {
                $type = 'button_copy';
            } elseif (! $hasDynamicButton && ! $hasCopyButton) {
                $type = 'NA';
            }
        }

        // Set template parameters
        $template->subType = $subType;
        $template->header = $headerComponent;
        $template->buttons = $buttonComponent;
        $template->params = preg_match_all('/\{\{\d+\}\}/', $bodyComponent->text);
        $template->body = $bodyComponent->text;
        $template->type = $type;
        $template->mediaType = $mediaType;

        return $template;
    }

    public function buildSentMessage($accountId, $data)
    {
        $message = [
            'id' => $data['messageId'],
            'account_id' => $accountId,
            'sender' => $data['sender'] ?? null,
            'to' => $data['phone'],
            'from' => $data['from'],
            'fromMe' => 1,
            'chatId' => Func::makeChatId($data['phone']),
            'body' => $data['message'] ?? '',
            'status' => 'sent',
            'type' => 'text',
            'time' => time(),
        ];
        // change type if it's a file
        isset($data['file_id']) && $message['file_id'] = $data['file_id'];
        isset($data['file_type']) && $message['type'] = $data['file_type'];

        isset($data['file_type']) && $message['file_type'] = $data['file_type'];
        isset($data['file_url']) && $message['file_url'] = $data['file_url'];

        isset($data['quotedMsgId']) && $message['quotedMsgId'] = $data['quotedMsgId'];
        isset($data['quotedMsgBody']) && $message['quotedMsgBody'] = $data['quotedMsgBody'];

        return $message;
    }

    public function getFileType($filename)
    {
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        if (! $extension) {
            return 'unknown';
        }

        $extension = strtolower($extension);

        if (in_array($extension, ['png', 'jpeg', 'gif', 'jpg', 'bmp', 'tiff'])) {
            return 'image';
        }
        if (in_array($extension, ['mp4', 'ogg', 'webm', 'mkv', 'avi', 'mov'])) {
            return 'video';
        }
        if (in_array($extension, ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'])) {
            return 'document';
        }
        if (in_array($extension, ['txt', 'csv', 'rtf'])) {
            return 'text';
        }
        if (in_array($extension, ['aac', 'm4a', 'wav', 'mp3', 'flac'])) {
            return 'audio';
        }

        return 'other';
    }

    /**
     * Get the appropriate status conditions based on the requested status
     */
    public function getStatusConditions(string $requestedStatus): array
    {
        switch ($requestedStatus) {
            case 'sent':
                return ['sent', 'delivered', 'read'];
            case 'delivered':
                return ['delivered', 'read'];
            default:
                return [$requestedStatus];
        }
    }
}
