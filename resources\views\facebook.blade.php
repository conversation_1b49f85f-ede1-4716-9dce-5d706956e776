@extends('layouts.app')
@section('title')
	Authorize WhatsApp
@endsection
@section('content')
	<div class="container">
		<button onclick="launchWhatsAppSignup()"
			style="background-color: #1877f2; border: 0; border-radius: 4px; color: #fff; cursor: pointer; font-family: Helvetica, Arial, sans-serif; font-size: 16px; font-weight: bold; height: 40px; padding: 0 24px;">Login
			with Facebook</button>
	</div>
	<script>
		window.fbAsyncInit = function () {
			// JavaScript SDK configuration and setup
			FB.init({
				appId: "{{$fbAppId}}", // Facebook App ID
				autoLogAppEvents: true,
				xfbml: true, // parse social plugins on this page
				version: "{{$fbVersion}}", // Graph API version
			});
		};

		// Load the JavaScript SDK asynchronously
		(function (d, s, id) {
			var js, fjs = d.getElementsByTagName(s)[0];
			if (d.getElementById(id)) return;
			js = d.createElement(s); js.id = id;
			js.src = "https://connect.facebook.net/en_US/sdk.js";
			fjs.parentNode.insertBefore(js, fjs);
		}(document, 'script', 'facebook-jssdk'));

		// Response callback function
		const fbLoginCallback = (response) => {
			if (response.authResponse) {
				const code = response.authResponse.code;
				console.log('Response code:', code); // For debugging, remove in production

				// Send this code to your server to exchange for a business token
				// You have 30 seconds to exchange this code before it expires

				// Redirect to specific URL with the code parameter
				const redirectUrl = "http://vira.niswey.net/facebook/auth?code=" + encodeURIComponent(code);
				window.location.href = redirectUrl;
			} else {
				console.log("User cancelled login or did not fully authorize.");
			}
		};

		// Facebook Login with JavaScript SDK
		function launchWhatsAppSignup() {
			// Only track using fbq if it's defined
			if (typeof fbq !== 'undefined') {
				fbq("trackCustom", "WhatsAppOnboardingStart", {
					appId: "{{$fbAppId}}",
					feature: "whatsapp_embedded_signup"
				});
			}

			// Launch Facebook login with proper configuration
			FB.login(fbLoginCallback, {
				config_id: "{{$fbConfigId}}",
				response_type: 'code',
				override_default_response_type: true,
				extras: {
					setup: {},
					featureType: "whatsapp_business_app_onboarding", // Leave blank for default flow or specify a feature type
					sessionInfoVersion: "3"
				}
			});
		}

		// Session logging message event listener
		const sessionInfoListener = (event) => {
			if (!event.origin.endsWith('facebook.com')) return;
			try {
				const data = JSON.parse(event.data);
				if (data.type === "WA_EMBEDDED_SIGNUP") {
					console.log('WhatsApp signup event:', data); // For debugging, remove in production

					// If user finishes the embedded signup flow
					if (data.event === "FINISH") {
						const { phone_number_id, waba_id, business_id } = data.data;

						// Log the successful completion (for debugging)
						console.log("WhatsApp signup completed:", {
							phoneNumberId: phone_number_id,
							wabaId: waba_id,
							businessId: business_id
						});

						// Redirect to your success URL with the relevant data
						const redirectUrl = "http://vira.niswey.net/facebook/auth?code=" +
							encodeURIComponent(JSON.stringify({
								phone_number_id,
								waba_id,
								business_id
							}));
						window.location.href = redirectUrl;
					}
					// If user cancels the embedded signup flow
					else if (data.event === "CANCEL") {
						if (data.data.current_step) {
							console.log("User cancelled at step:", data.data.current_step);
						} else if (data.data.error_message) {
							console.log("Error during signup:", data.data.error_message);
						}

					}
				}
			} catch (error) {
				// Non-JSON response
				console.log("Non-JSON Response:", event.data);
			}
		};

		window.addEventListener("message", sessionInfoListener);
	</script>
@endsection
