<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use App\Helpers\Waba;
use App\Models\Account;
use App\Models\Setting;
use Illuminate\Http\Request;
use App\Jobs\FacebookWebhookJob;
use App\Services\WebhookService;
use App\Services\Whatsapp\AuthService;
use App\Notifications\SlackNotification;
use Illuminate\Notifications\Notification;
use Netflie\WhatsAppCloudApi\WebHook as WhatsappWebhook;

class FacebookController extends Controller
{
    protected $webhookService;

    protected $skipZeroPortals;

    public function __construct()
    {
        parent::__construct('waba');
        $this->skipZeroPortals = [7222284, ********, ********];
        $this->webhookService = new WebhookService;
    }

    public function auth(Request $request)
    {
        $request->validate([
            'code' => 'required',
            'state' => 'required',
        ]);

        $input = $request->input();
        Log::info("[FacebookController:auth] $this->requestId, payload: ".json_encode($input));

        try {
            $waba = $this->waba;
            $state = json_decode($input['state']);

            $auth = new AuthService($this->requestId);
            $fbAuth = $auth->authenticate($input['code']);
            $tokens = $fbAuth->getTokens();
            $wabaId = $fbAuth->getWabaId();
            $number = $fbAuth->getNumber();

            $account = Account::updateOrCreate([
                'portal_id' => $state->portalId,
                'waba_id' => $wabaId, 'waba_phone_id' => $number->id,
            ], [
                'waba_phone' => preg_replace("/\D/", '', $number->display_phone_number),
                'waba_name' => $number->verified_name,
                'token' => $tokens->access_token,
                'expires_in' => $tokens->expires_in ?? 0,
                'issued_at' => time(),
                'paid' => 1, // remove later when payment is connected
            ]);

            // save consent property
            $setting = Setting::where(['account_id' => $account->id])->first();
            $consentPropertySaved = $setting ? $setting->consent_property : false;

            // update property if provided but does not equal to saved property
            if ($consentPropertySaved && $consentPropertySaved != $state->consentProperty) {
                $setting->consent_property = $state->consentProperty;
                $setting->save();
            }

            // if not saved in DB and also not provided then create it in customer's portal
            if (! $consentPropertySaved && ! $state->consentProperty) {
                $created = $this->hubspotApp
                    ->legacy()
                    ->createContactProperty(
                        $state->portalId,
                        $waba->consentPropertySchema()
                    );
                if (! $created) {
                    throw new Exception('Unable to create consent Property', 1); // NOSONAR
                }

                Setting::updateOrCreate(['account_id' => $account->id], [
                    'portal_id' => $state->portalId,
                    'consent_property' => $waba->consentPropertySchema()['name'],
                ]);
            }

            // account was created, now subscribe to webhooks
            $subscribed = $waba->subscribeToWebhook($wabaId);
            if (! $subscribed) {
                throw new Exception("Unable to set webhook for: $wabaId", 1); // NOSONAR
            }

            Log::info("[FacebookController:auth] $this->requestId, Success");

            return view('connected', ['portalId' => $state->portalId, 'phone_id' => $number->id]);
        } catch (Exception $e) {
            Log::error("[FacebookController:auth] $this->requestId, Exception: ".$e->getTraceAsString());

            return view('error', ['title' => 'Something went wrong',  'message' => 'Oops, something went wrong.']);
        }
    }

    public function deAuth(Request $request)
    {
        $input = $request->input();
        Log::info('[FacebookController:deAuth] input: ', $input);
        echo 'User De-Authorized';
    }

    public function delete(Request $request)
    {
        $input = $request->input();
        Log::info('[FacebookController:delete] input: ', $input);
        echo 'User Deleted account';
    }

    public function webhookVerifyCode(Request $request)
    {
        $input = $request->input();
        Log::info('[FacebookController:verifyWebhook], payload: '.json_encode($input));

        if ($request->input('hub_verify_token') == $this->webhookService->getWebhookVerifyCode()) {
            return $input['hub_challenge'];
        }

        return $this->jsonError(['message' => 'webhook validation failed'], 403);
    }

    public function webhook(Request $request)
    {
        Log::info("[FacebookController:webhook] $this->requestId, payload: ".$request->getContent());

        $webhook = new WhatsappWebhook;

        try {
            $events = $webhook->readAll($request->input());
            foreach ($events as $event) {
                FacebookWebhookJob::dispatch($event, $this->requestId)->onQueue('wabaf');
            }
        } catch (Exception $e) {
        }

        return $this->jsonOk(['message' => 'queued']);
    }

    public function register(Request $request)
    {
        $input = $request->validate([
            'pin' => 'required',
            'portalId' => 'required',
            'wabaPhoneId' => 'required',
        ]);
        Log::info("[FacebookController:register] $this->requestId, payload: ", $input);

        $account = Account::where(
            [
                'waba_phone_id' => $input['wabaPhoneId'],
                'portal_id' => $input['portalId']]
        )->first();
        if (! $account) {
            return $this->jsonError(['message' => 'Invalid account']);
        }

        $waba = new Waba($account->id, $this->requestId);

        // fetch waba setting to look for any region
        $settings = $waba->fetchSettings($input['wabaPhoneId']);
        $region = $settings->storage_configuration->data_localization_region ?? null;

        // register waba phone
        $register = $waba->register($input['wabaPhoneId'], $input['pin'], $region);
        if (! $register) {
            Notification::route('slack', '#hw-activity')->notify(new SlackNotification([
                'type' => 'error',
                'message' => $waba->error,
                $input,
            ]));

            return $this->jsonError(['message' => 'unable to register']);
        }

        return $this->jsonOk();
    }

    public function exchange(Request $request)
    {
        $input = $request->input();
        Log::info("[FacebookController:exchange] $this->requestId, payload: ".json_encode($input));

        return $this->jsonOk();
    }

    public function test()
    {
        echo 'ok';
    }
}
