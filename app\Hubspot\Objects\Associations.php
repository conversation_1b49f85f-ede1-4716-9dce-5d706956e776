<?php

namespace App\Hubspot\Objects;

use Log;
use Exception;
use HubSpot\Client\Crm\Associations\V4\Model\AssociationSpec;
use HubSpot\Client\Crm\Associations\V4\Model\PublicAssociationMultiPost;
use HubSpot\Client\Crm\Associations\V4\Model\BatchInputPublicAssociationMultiPost;

class Associations
{
    public function __construct(
        protected $client,
        protected string $requestId = '',
    ) {
        $this->requestId = $this->client->requestId;
    }

    public function get($object_type, $object_id, $to_object_type)
    {
        try {
            $response = $this->client->crm()
                ->associations()
                ->v4()
                ->basicApi()
                ->getPage($object_type, $object_id, $to_object_type);

            return json_decode(json_encode($response));
        } catch (Exception $e) {
            Log::error("[Associations:get] $this->requestId, Exception: ".$e->getMessage());

            return null;
        }
    }

    public function create($from_object_type, $from_object_id, $to_object_type, $to_object_id, $association_type = null)
    {
        try {
            // Create a basic association input
            $associationSpec = new AssociationSpec([
                'association_category' => 'HUBSPOT_DEFINED',
                'association_type_id' => $association_type ?: 16,
            ]);

            $publicAssociationMultiPost = new PublicAssociationMultiPost([
                'types' => [$associationSpec],
                'from' => ['id' => $from_object_id],
                'to' => ['id' => $to_object_id],
            ]);

            $batchInputAssociationMultiPost = new BatchInputPublicAssociationMultiPost([
                'inputs' => [$publicAssociationMultiPost],
            ]);

            // Create the association
            $response = $this->client->crm()
                ->associations()
                ->v4()
                ->batchApi()
                ->create($from_object_type, $to_object_type, $batchInputAssociationMultiPost);

            return json_decode(json_encode($response));
        } catch (Exception $e) {
            Log::error("[Associations:create] $this->requestId, Exception: ".$e->getMessage());

            return null;
        }
    }
}
