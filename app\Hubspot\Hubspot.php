<?php

namespace App\Hubspot;

use Log;
use Exception;
use Carbon\Carbon;
use HubSpot\Utils\OAuth2;
use App\Helpers\HubspotApp;
use App\Models\PortalToken;
use App\Hubspot\Objects\Deals;
use App\Hubspot\Objects\Lists;
use App\Hubspot\Objects\Oauth;
use App\Hubspot\Objects\Owners;
use App\Hubspot\Objects\Contacts;
use App\Hubspot\Objects\Pipelines;
use Illuminate\Support\Facades\Http;
use App\Hubspot\Objects\Associations;
use App\Hubspot\Objects\Conversations;

class Hubspot
{
    public $app;

    public $hsApp;

    public $debug;

    public $requestId;

    protected $portalId;

    protected $portalTokens;

    public function __construct($portalId, $requestId)
    {
        $this->app = 'waba';

        if (env('APP_ENV') == 'development') {
            $this->app = 'wabadev';
        }
        if (env('APP_ENV') == 'staging') {
            $this->app = 'wabaqa';
        }

        $this->portalId = $portalId;
        $this->requestId = $requestId;
        $this->hsApp = config('hsapp.'.$this->app);
        $this->debug = ($this->portalId == 49726162);
    }

    // set portalId
    public function forPortal(int $portalId)
    {
        $this->portalId = $portalId;

        return $this;
    }

    public function client()
    {
        $accessToken = $this->getAccessToken();
        $client = HubspotClient::createFactory($accessToken);
        $client->appConfig = $this->hsApp;
        $client->accessToken = $accessToken;
        $client->requestId = $this->requestId;

        return $client;
    }

    public function getAuthUrl($scope = 'scope')
    {
        $scopes = $this->hsApp['scope'];
        if ($scope == 'conversations') {
            $scopes = $scopes.' '.$this->hsApp['conversation_scope'];
        }

        return OAuth2::getAuthUrl(
            $this->hsApp['auth']['client_id'],
            $this->hsApp['auth']['redirect_uri'],
            explode(',', $scopes),
        );
    }

    public function createTokens($code)
    {
        $hsApp = $this->hsApp['auth'];
        $hsApp['code'] = $code;

        return Http::asForm()->post('https://api.hubapi.com/oauth/v1/token', $hsApp)->object();
    }

    public function refreshToken($tokens)
    {
        $this->debug && Log::info("[Hubspot:refreshToken] $this->requestId, refreshToken called");

        $hsApp = array_merge($this->hsApp['auth'], [
            'grant_type' => 'refresh_token',
            'refresh_token' => $tokens['refresh_token'],
        ]);
        unset($hsApp['code'], $hsApp['redirect_uri']);

        $newTokens = Http::asForm()->post('https://api.hubapi.com/oauth/v1/token', $hsApp)->object();

        if (! isset($newTokens->access_token)) {
            Log::error("[Hubspot:refreshToken] for $this->requestId, response: ".json_encode($newTokens));
            $newTokens->refresh_token = $tokens['refresh_token'];

            return $tokens;
        }

        if (isset($newTokens->access_token)) {
            PortalToken::where('portal_id', $tokens['portal_id'])
                ->update([
                    'access_token' => $newTokens->access_token,
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
            $tokens['access_token'] = $newTokens->access_token;
        }

        return $tokens;
    }

    public function getAccessToken()
    {
        if ($this->portalTokens) {
            // Check if the cached token is expired
            $shouldRefresh = now()->gte(
                Carbon::parse($this->portalTokens->updated_at)->addSeconds($this->portalTokens->expires_in - 10)
            );

            if ($shouldRefresh) {
                $this->debug && Log::info("[Hubspot:getAccessToken] {$this->requestId}, refreshing cached token for: ".$this->portalTokens->portal_id);
                $this->portalTokens = $this->refreshToken($this->portalTokens);
            } else {
                $this->debug && Log::info("[Hubspot:getAccessToken] {$this->requestId}, returning valid cached token for: ".$this->portalTokens->portal_id);
            }

            return $this->portalTokens->access_token;
        }

        $tokens = PortalToken::where('portal_id', $this->portalId)->first();
        if (! $tokens) {
            $this->debug && Log::info("[Hubspot:getAccessToken] {$this->requestId}, no portal found");

            return false;
        }

        $shouldRefresh = now()->gte(
            Carbon::parse($tokens->updated_at)->addSeconds($tokens->expires_in - 10)
        );

        $this->portalTokens = $shouldRefresh ? $this->refreshToken($tokens) : $tokens;

        $this->debug && Log::info("[Hubspot:getAccessToken] {$this->requestId}, shouldRefresh: {$shouldRefresh} for: ".$this->portalTokens->portal_id);

        return $this->portalTokens->access_token;
    }

    public function getPortalId($access_token)
    {
        return Http::withToken($access_token)->get('https://api.hubapi.com/integrations/v1/me')->object();
    }

    public function uploadFile($file, $type = 'laravel')
    {
        $filename = '';
        $mimeType = '';
        $hubspot = $this->client();

        try {
            if ($type == 'laravel') {
                $mimeType = $file->getClientMimeType();
                $filename = $file->getClientOriginalName();
            } else {
                $mimeType = $file->mimeType;
                $filename = $file->filename;
                $file = new \SplFileObject($file->path);
            }

            $appNameLowerCase = strtolower(env('APP_NAME'));
            $response = $hubspot->files()->filesApi()->upload(
                $file,
                null,
                "/$appNameLowerCase/",
                $filename,
                null,
                json_encode([
                    'folderPath' => $appNameLowerCase,
                    'access' => 'PUBLIC_NOT_INDEXABLE',
                    'duplicateValidationScope' => 'EXACT_FOLDER',
                    'duplicateValidationStrategy' => 'NONE',
                ])
            );

            if (isset($response->error)) {
                Log::error("[Hubspot:uploadFile] $this->requestId, Response: ".json_encode($response));

                return false;
            }

            $fileResponse = new \StdClass;
            $fileResponse->id = $response->getId();
            $fileResponse->url = $response->getUrl();
            $fileResponse->name = $response->getName();
            $fileResponse->type = $response->getType();
            $fileResponse->extension = $response->getExtension();
            $fileResponse->filename = $fileResponse->name.'.'.$fileResponse->extension;
            $mimeType && $fileResponse->type = explode('/', $mimeType)[0] ?? $fileResponse->type;

            Log::info("[Hubspot:uploadFile] $this->requestId, file: ".json_encode($response));

            return $fileResponse;
        } catch (Exception $e) {
            $trace = $e->getTraceAsString();
            Log::error("[Hubspot:uploadFile] $this->requestId, Error: ".$e->getMessage().'\n'.$trace);

            return false;
        }
    }

    public function legacy()
    {
        return new HubspotApp($this->app, 'App\Models\PortalToken', $this->requestId);
    }

    public function associations()
    {
        return new Associations($this->client());
    }

    public function pipelines()
    {
        return new Pipelines($this->client());
    }

    public function contacts()
    {
        return new Contacts($this->client());
    }

    public function deals()
    {
        return new Deals($this->client());
    }

    public function timeline()
    {
        return new HubspotTimeline($this->client());
    }

    public function lists()
    {
        return new Lists($this->client());
    }

    public function owners()
    {
        return new Owners($this->client());
    }

    public function oauth()
    {
        $client = $this->client();
        $client->access_token = $this->portalTokens['access_token'];

        return new Oauth($client);
    }

    public function conversations()
    {
        return new Conversations($this->client());
    }
}
