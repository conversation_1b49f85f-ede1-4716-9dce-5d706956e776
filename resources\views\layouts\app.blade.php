<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta http-equiv="x-ua-compatible" content="ie=edge">
	<title>@yield('title', 'Vira - WhatApp Automation')</title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="csrf-token" content="{{ csrf_token() }}">
	<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
	<link rel="manifest" href="/site.webmanifest">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&display=swap" rel="stylesheet">

	@vite(['resources/js/style.js'])
</head>
	<body class="d-flex flex-column h-100">
		<nav class="navbar navbar-default">
			<div class="container-fluid">
				<a class="navbar-brand" href="{{env('APP_URL')}}">
					<img src="/img/niswey-logo.png" alt="Logo" width="30" class="d-inline-block align-top" />
					{{env('APP_NAME')}}
				</a>
			</div>
		</nav>


		<main class="pb-5">
			<div class="errors">
				<div class="container-sm mt-2 bt-2">
					@if (isset($errors) && $errors->any())
						<div class="alert alert-danger">
							<ul style="margin-bottom: 0">
								@foreach ($errors->all() as $error)
									<li>{{ $error }}</li>
								@endforeach
							</ul>
						</div>
					@endif
				</div>
			</div>
			@yield('content')
		</main>

		<footer class="footer mt-auto py-3 border-top">
			<div class="container">
				<span>Powered by <a target="_blank" href="http://niswey.com/">Niswey</a></span>
			</div>
		</footer>

		@vite(['resources/js/app.js'])
	</body>
</html>
