@extends('layouts.new')

@section('title', 'Deploy Staging')

@section('content')
<section class="container text-center margin_top" x-data="data()">
	<h2>Deploy Staging</h2>

	<div class="container-sm py-2">
		<template x-if="failed">
			<div class="text-center">
				<div class="alert alert-danger" role="alert" x-text="failed"></div>
			</div>
		</template>
		<template x-if="success">
			<div class="text-center">
				<div class="alert alert-success" role="alert" x-text="success"></div>
			</div>
		</template>
		<template x-if="loading">
			<div class="text-center">
				<div class="spinner-border text-primary" role="status">
					<span class="visually-hidden">Loading...</span>
				</div>
			</div>
		</template>
	</div>

	<div class="d-flex justify-content-center mt-4">
		<button class="btn btn-primary px-5 py-3" @click="deploy">
			<strong>Deploy</strong>
		</button>
	</div>
</section>

<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script src="//unpkg.com/alpinejs" defer></script>
<script type="text/javascript">
	function data() {
		return {
			loading: false,
			failed: "",
			success: "",
			deploy() {
				console.log('sss')
				this.loading = true;
				axios.post('/api/deploy')
					.then(response => {
						this.loading = false;
						let res = response.data;
						if (!res.ok) {
							this.failed = res.message;
						} else {
							this.success = "Deployment triggered successfully!";
						}
					})
					.catch(error => {
						this.loading = false;
						this.failed = "An error occurred. Please try again.";
						console.error(error);
					});
			}
		}
	}
</script>

@endsection
