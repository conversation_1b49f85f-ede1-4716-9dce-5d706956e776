<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class VerifyOriginMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $allowedDomains = explode(',', env('ALLOWED_DOMAINS', ''));

        if (! in_array($request->headers->get('origin'), $allowedDomains)) {
            return response('Unauthorized.', 401);
        }

        return $next($request);
    }
}
