<?php

namespace App\Console\Commands;

use App\Models\HubList;
use Illuminate\Console\Command;
use App\Jobs\ProcessCampaignJob;

class RecoverCampaignsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recover:campaigns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'recover stuck campaigns';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Find lists that are partially processed but haven't been updated recently
        $stuckLists = HubList::where('more', true)
            ->where('enabled', true)
            ->where('last_processed', '<', now()->subMinutes(15)->timestamp)
            ->get();

        foreach ($stuckLists as $campaign) {
            // Dispatch a new job to continue processing from where it left off
            dispatch(new ProcessCampaignJob($campaign->id))->onQueue('lists');
            $this->info("Restarted processing for campaign ID: {$campaign->id}");
        }
    }
}
