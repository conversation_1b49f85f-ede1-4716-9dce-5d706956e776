<?php

namespace App\Helpers;

use Log;

class Slack
{
    protected $token;

    public function __construct($token)
    {
        $this->token = $token;
    }

    public function postMessage($data)
    {
        $res = Func::request('POST', 'https://slack.com/api/chat.postMessage', [
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => "Bearer $this->token",
            ],
            'json' => $data,
        ]);

        $ok = $res->ok ?? '';
        if (! $ok) {
            $error = $res->error ?? 'Unable to send message: '.json_encode($res);
            Log::error('[Slack:postMessage] Error: '.$error);

            return false;
        }

        return true;
    }

    public function interactionReply($url, $data)
    {
        $res = Func::request('POST', $url, [
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => "Bearer $this->token",
            ],
            'json' => $data,
        ]);

        $ok = $res->ok ?? '';
        if (! $ok) {
            $error = $res->error ?? 'Unable to send interactionReply: '.json_encode($res);
            Log::error('[Slack:interactionReply] Error: '.$error);

            return false;
        }

        return true;
    }

    public function fetchUsers()
    {
        $res = Func::request('GET', 'https://slack.com/api/users.list', [
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => "Bearer $this->token",
            ],
        ]);

        $ok = $res->ok ?? '';
        if (! $ok) {
            $error = $res->error ?? 'Unable to get Users: '.json_encode($res);
            Log::error('[Slack:fetchUsers] Error: '.$error);

            return false;
        }

        return $res->members;
    }

    public function sendMoodQuestion($user_id, $block_id)
    {
        $elements = $this->getElements();
        $data = [
            'channel' => $user_id,
            'blocks' => [
                [
                    'type' => 'section',
                    'text' => [
                        'type' => 'plain_text',
                        'text' => 'How was your day today?',
                    ],
                ],
                [
                    'type' => 'actions',
                    'block_id' => $block_id,
                    'elements' => $elements,
                ],
            ],
        ];

        $res = Func::request('POST', 'https://slack.com/api/chat.postMessage', [
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => "Bearer $this->token",
            ],
            'json' => $data,
        ]);

        $ok = $res->ok ?? '';
        if (! $ok) {
            $error = $res->error ?? 'Unable to get Users: '.json_encode($res);
            Log::error('[Slack:sendMoodQuestion] Error: '.$error);

            return false;
        }

        return true;
    }

    public function getMoodScale()
    {
        return ['Very Bad', 'Bad', 'Neutral', 'Nice', 'Awesome'];
    }

    public function getElements()
    {
        $elements = [];
        $moodScale = $this->getMoodScale();
        foreach ($moodScale as $key => $mood) {
            $item = [
                'type' => 'button',
                'text' => [
                    'type' => 'plain_text',
                    'text' => $mood,
                ],
                'value' => "$key",
            ];
            if ($key == 0) {
                $item['style'] = 'danger';
            }
            if ($key == 4) {
                $item['style'] = 'primary';
            }
            $elements[] = $item;
        }

        return $elements;
    }
}
