@extends('layouts.new')

@section('title')
    {{ $title ?? env('APP_NAME') }}
@endsection

@section('content')
<div class="container mt-4" x-data="formHandler()">
    <div class="container-sm py-2">
        <template x-if="failed">
            <div class="text-center">
                <div class="alert alert-danger" role="alert" x-text="failed"></div>
            </div>
        </template>
        <template x-if="success">
            <div class="text-center">
                <div class="alert alert-success" role="alert" x-text="success"></div>
            </div>
        </template>
        <template x-if="loading">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </template>
    </div>

    <div class="card mt-5">
        <div class="card-header">
            <h4>Portal Form</h4>
        </div>
        <div class="card-body">
            <form action="{{ route('dummy_sub') }}" @submit.prevent="submitForm">
                @csrf

                <div class="mb-3">
                    <label for="portal_id" class="form-label">Portal ID</label>
                    <input type="number" class="form-control" id="portal_id" name="portal_id" required x-model="formData.portal_id">
                </div>

                <div class="mb-3">
                    <label for="sub_id" class="form-label">Sub ID</label>
                    <input type="text" class="form-control" id="sub_id" name="sub_id" maxlength="100" required x-model="formData.sub_id" placeholder="Put any unique number">
                </div>

                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="email" name="email" x-model="formData.email">
                </div>

                <div class="mb-3">
                    <label for="phone" class="form-label">Phone</label>
                    <input type="text" class="form-control" id="phone" name="phone" maxlength="20" x-model="formData.phone" placeholder="Put any number">
                </div>

                <div class="mb-3">
                    <label for="hubspot_email" class="form-label">HubSpot Email</label>
                    <input type="email" class="form-control" id="hubspot_email" name="hubspot_email" x-model="formData.hubspot_email">
                </div>

                <div class="mb-3">
                    <label for="plan" class="form-label">Plan</label>
                    <select class="form-select" id="plan" name="plan" x-model="formData.plan">
                        <option value="starter">Starter</option>
                        <option value="professional">Professional</option>
                        <option value="enterprise">Enterprise</option>
                    </select>
                </div>

                <button type="submit" class="btn btn-primary" x-bind:disabled="loading">
                    <span x-show="!loading">Submit</span>
                    <span x-show="loading">Submitting...</span>
                </button>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script src="//unpkg.com/alpinejs" defer></script>

<script type="text/javascript">
    function formHandler() {
        return {
            loading: false,
            failed: "",
            success: "",
            formData: {
                portal_id: '',
                sub_id: '',
                email: '',
                phone: '',
                hubspot_email: '',
                plan: 'starter'
            },
            submitForm(event) {

			    let form = event.target; // Directly access the form from the event
			    let url = form.action;   // Get the form action URL

			    if (!url) {
			        this.failed = "Form action URL is missing!";
			        this.loading = false;
			        return;
			    }

			    axios.post(url, this.formData)
			        .then(response => {
			            console.log(this.formData);
			            this.loading = false;
			            let res = response.data;
			            if (res.ok) {
			                this.success = "Subscription created successfully!";
			                this.formData = { // Reset form fields after success
			                    portal_id: '',
			                    sub_id: '',
			                    email: '',
			                    phone: '',
			                    hubspot_email: '',
			                    plan: 'starter'
			                };
			            } else {
			                this.failed = res.message;
			            }
			        })
			        .catch(error => {
			            this.loading = false;
			            this.failed = "An error occurred. Please try again.";
			            console.error(error);
			        });
			}

        };
    }
</script>
@endsection
