<?php

namespace App\Jobs\Waba;

use App\Helpers\HelperTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class WhatsAppJob implements ShouldQueue
{
    use Dispatchable;
    use HelperTrait;
    use InteractsWithQueue;
    use Queueable;

    protected $input;

    protected $requestId;

    protected $workflow;

    public $tries = 1;

    public $timeout = 90;

    public $retryAfter = 30;

    public function __construct($input, $requestId)
    {
        $this->input = $input;
        $this->requestId = $requestId;
    }

    /**
     * Prepare workflow data for storing in the database
     *
     * @param  array  $params  Direct keys needed for workflow data
     * @return array Prepared workflow data
     */
    protected function prepareWorkflowData($params)
    {
        // Prepare workflow data using direct keys from params
        $workflowData = [
            'requestId' => $this->requestId,
            'callbackId' => $this->input['callbackId'] ?? null,
            'workflowId' => $this->input['context']['workflowId'] ?? null,
            'account_id' => $params['accountId'] ?? $this->input['fields']['account'] ?? null,
            'messageId' => $params['messageId'] ?? null,
            'portalId' => $this->input['origin']['portalId'] ?? null,
            'workflow' => $this->input['context']['source'] ?? null,
            'object_id' => $params['object_id'] ?? null,
            'template_id' => $params['template_id'] ?? null,
            'template_name' => $params['template_name'] ?? null,
            'sent_from' => $params['from'] ?? null,
            'status' => $params['status'] ?? 'sent',
            'name' => $params['name'] ?? null,
            'phone' => $params['phone'] ?? null,
            'sent_to' => $params['to'] ?? $params['phone'] ?? null,
            'type' => $params['messageType'] ?? 'template',
            'provider_request' => $param['provider_request'] ?? null,
            'provider_response' => $param['provider_response'] ?? null,
        ];

        return $workflowData;
    }

    public function getWabaId($res)
    {
        if (empty($res->customData['waba_id'])) {
            return null;
        }

        return $res->customData['waba_id'];
    }
}
