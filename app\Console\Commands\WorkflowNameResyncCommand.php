<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Models\Account;
use App\Hubspot\Hubspot;
use App\Models\PortalToken;
use App\Models\WorkflowReport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class WorkflowNameResyncCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:workflow-names';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Re-sync workflow names for all workflow reports with generic names for portals reauthorized since May 12, 2025';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Started workflow name re-sync');

        try {
            // Fixed batch size of 100 for HubSpot API limits
            $chunkSize = 100;

            // Fixed launch date: May 12, 2025
            $launchDate = \Carbon\Carbon::parse('2025-05-12');

            $this->info("Using fixed launch date: {$launchDate->toDateString()}");

            // Get all reauthorized portals
            $portals = $this->getReauthorizedPortals($launchDate);

            $totalPortals = count($portals);
            $this->info("Found {$totalPortals} portals reauthorized since {$launchDate->toDateString()}");

            $processedPortals = 0;
            foreach ($portals as $portal) {
                $processedPortals++;
                $this->info("Processing portal {$portal} [{$processedPortals}/{$totalPortals}]");

                // Get all active accounts for this portal
                $accounts = Account::select('id', 'portal_id')
                    ->where('portal_id', $portal)
                    ->where('paid', '!=', 0)
                    ->get();

                $totalAccounts = $accounts->count();
                $this->info("Found {$totalAccounts} active accounts for portal {$portal}");

                $processedAccounts = 0;
                foreach ($accounts as $account) {
                    $processedAccounts++;
                    $this->info("Processing account {$account->id} [{$processedAccounts}/{$totalAccounts}]");

                    $this->resyncWorkflowNames($account->id, $account->portal_id, $chunkSize);
                }
            }

            $this->info('Workflow name re-sync completed');

            return 0;
        } catch (Exception $e) {
            $this->error('Error: '.$e->getMessage());
            Log::error('[WorkflowNameResyncCommand] '.$e->getMessage());

            return 1;
        }
    }

    /**
     * Get portals that have been reauthorized after the launch date
     */
    private function getReauthorizedPortals($launchDate)
    {
        return PortalToken::whereNotNull('auth_at')
            ->where('auth_at', '>=', $launchDate)
            ->pluck('portal_id')
            ->toArray();
    }

    /**
     * Re-sync workflow names for an account
     */
    private function resyncWorkflowNames($accountId, $portalId, $chunkSize)
    {
        // Get all workflows with generic names (like "Workflow #123")
        $workflowsToUpdate = $this->getWorkflowsWithGenericNames($accountId);

        $totalWorkflows = count($workflowsToUpdate);
        $this->info("Found {$totalWorkflows} workflows with generic names for account {$accountId}");

        if ($totalWorkflows === 0) {
            return;
        }

        // Process in optimized batches
        $bar = $this->output->createProgressBar($totalWorkflows);
        $bar->start();

        foreach (array_chunk($workflowsToUpdate, $chunkSize) as $batch) {
            $this->processWorkflowNameBatch($batch, $accountId, $portalId);
            $bar->advance(count($batch));
        }

        $bar->finish();
        $this->line(''); // Add a new line after progress bar
    }

    /**
     * Get workflows with generic names
     */
    private function getWorkflowsWithGenericNames($accountId)
    {
        return WorkflowReport::where('account_id', $accountId)
            ->where(function ($query) {
                $query->whereRaw("name LIKE 'Workflow #%'")
                    ->orWhereNull('name');
            })
            ->select('id')
            ->pluck('id')
            ->toArray();
    }

    /**
     * Process a batch of workflows efficiently
     */
    private function processWorkflowNameBatch(array $workflowIds, $accountId, $portalId)
    {
        if (empty($workflowIds)) {
            return;
        }

        try {
            // Fetch actual names from HubSpot API using batch endpoint
            $workflowNames = $this->fetchWorkFlowDataBatch($workflowIds, $portalId);

            // Update names in batches for database efficiency
            $updates = [];
            foreach ($workflowIds as $workflowId) {
                $name = $workflowNames[$workflowId] ?? null;

                // Skip if no valid name was returned
                if ($name && $name !== "Workflow #{$workflowId}") {
                    $updates[] = [
                        'id' => $workflowId,
                        'account_id' => $accountId,
                        'name' => $name,
                    ];
                }
            }

            // Use bulk update for efficiency
            if (! empty($updates)) {
                $this->bulkUpdateWorkflowNames($updates);
                $this->info('Updated '.count($updates)." workflow names for account {$accountId}");
            } else {
                $this->info("No workflow names to update for account {$accountId}");
            }
        } catch (Exception $e) {
            Log::error('[WorkflowNameResyncCommand] Error processing batch: '.$e->getMessage());
            $this->error('Error processing batch: '.$e->getMessage());
        }
    }

    /**
     * Bulk update workflow names efficiently
     */
    private function bulkUpdateWorkflowNames(array $updates)
    {
        // Use chunking to avoid too many queries at once
        foreach (array_chunk($updates, 100) as $chunk) {
            foreach ($chunk as $update) {
                WorkflowReport::where('id', $update['id'])
                    ->where('account_id', $update['account_id'])
                    ->update(['name' => $update['name']]);
            }
        }
    }

    /**
     * Fetch multiple workflow data from HubSpot API using the batch endpoint
     */
    private function fetchWorkFlowDataBatch(array $workflowIds, $portalId)
    {
        $results = [];

        // Set default fallback names first
        foreach ($workflowIds as $workflowId) {
            $results[$workflowId] = "Workflow #{$workflowId}";
        }

        if (empty($workflowIds)) {
            return $results;
        }

        try {
            // Verify portal has valid token
            $hubspot = new Hubspot($portalId, uniqid());
            $token = $hubspot->getAccessToken();

            if (empty($token)) {
                $this->warn("No valid token for portal {$portalId}, skipping batch");
                Log::warning("[WorkflowNameResyncCommand] No valid token for portal {$portalId}");

                return $results;
            }

            // Prepare the batch request body
            $requestBody = [
                'inputs' => array_map(function ($id) {
                    return [
                        'type' => 'FLOW_ID',
                        'flowId' => (string) $id,
                    ];
                }, $workflowIds),
            ];

            Log::info('fetchWorkFlowDataBatch: '.json_encode($requestBody));
            $response = Http::withToken($token)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post('https://api.hubapi.com/automation/v4/flows/batch/read', $requestBody);

            if ($response->successful()) {
                $data = $response->object();
                // Process the response and extract names
                if (isset($data->results) && is_array($data->results)) {
                    foreach ($data->results as $result) {
                        // Extract the id from the result
                        $flowId = $result->id ?? null;
                        $name = $result->name ?? null;

                        if ($flowId && $name) {
                            $results[$flowId] = $name;
                        }
                    }
                }

                $this->info('Successfully fetched '.count($data->results ?? [])." workflow names for portal {$portalId}");
            } else {
                Log::error('[WorkflowNameResyncCommand] Batch API error: '.$response->status().' - '.$response->body());
                $this->error("Batch API error for portal  {$portalId}: ".$response->status());
            }
        } catch (Exception $e) {
            Log::error('[WorkflowNameResyncCommand] Error fetching workflows batch: '.$e->getMessage());
            $this->error("Error fetching workflows batch for portal {$portalId}: ".$e->getMessage());
        }

        return $results;
    }
}
