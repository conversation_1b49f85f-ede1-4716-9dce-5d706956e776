<?php

namespace App\Http\Controllers;

use App\Helpers\Waba;
use Ramsey\Uuid\Uuid;
use App\Hubspot\Hubspot;
use App\Helpers\HelperTrait;
use App\Services\LoggerService;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class Controller extends BaseController
{
    use AuthorizesRequests;
    use HelperTrait;
    use ValidatesRequests;

    protected $app;

    protected $waba;

    protected $requestId;

    protected $hubspotApp;

    protected LoggerService $logger;

    protected $messageSendError = 'Unable to send message';

    protected $messageSendSuccess = 'Message sent successfully';

    public function __construct($app)
    {
        $this->app = $app;
        $uuid = Uuid::uuid4();
        $this->requestId = round(microtime(true) * 1000).'-req-'.$uuid->toString();
        $this->waba = new Waba(null, $this->requestId);
        $this->hubspotApp = new Hubspot(null, $this->requestId);
        $this->logger = new LoggerService(static::class, $this->requestId); // Set once
    }

    public function jsonOk($data = [], $code = 200)
    {
        $data = array_merge(['ok' => true], $data);

        return response()->json($data, $code);
    }

    public function jsonError($data = [], $code = 200)
    {
        $data = array_merge(['ok' => false], $data);

        return response()->json($data, $code);
    }
}
