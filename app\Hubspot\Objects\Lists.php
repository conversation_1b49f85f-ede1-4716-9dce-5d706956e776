<?php

namespace App\Hubspot\Objects;

use Log;
use Exception;
use HubSpot\Client\Crm\Lists\Model\ListCreateRequest;
use HubSpot\Client\Crm\Lists\Model\ListSearchRequest;

class Lists extends Objects
{
    public function __construct(
        protected $client,
        protected string $requestId = '',
    ) {
        $this->requestId = $this->client->requestId;
    }

    public function fetchListMembership(
        string $portalId,
        string $listId,
        ?string $after = null
    ): array {
        try {
            $response = $this->client->crm()->lists()->membershipsApi()->getPage(
                $listId,
                $after,
                null,
                100
            );

            return json_decode(json_encode($response), true);
        } catch (Exception $e) {
            Log::error("[Lists:fetchListMembership] $this->requestId:$portalId, Exception: ".$e->getMessage());

            return [];
        }
    }

    public function fetchContactLists(
        string $portalId,
        string $query = ''
    ): array {
        try {
            $request = new ListSearchRequest;
            $additionalProperties = [
                'hs_object_type_id',
                'hs_list_size',
            ];
            $request->setAdditionalProperties($additionalProperties);

            $setSort = [
                'propertyName' => 'HS_CREATED_AT',
                'direction' => 'DESCENDING',
            ];
            $request->setSort($setSort);
            $request->setOffset(0);
            $request->setCount(50);

            if (! empty($query)) {
                $request->setQuery($query);
            }

            $response = $this->client->crm()->lists()->listsApi()->doSearch($request);

            if (isset($response['lists'])) {
                $response->lists = array_values(array_filter($response['lists'], function ($list) {
                    return isset($list['object_type_id']) && $list['object_type_id'] === '0-1';
                }));
            }

            if (isset($response->status)) {
                Log::error("[Lists:fetchContactLists] Error for $portalId, response: ".json_encode($response));

                return [];
            }

            return $response->lists;
        } catch (Exception $e) {
            Log::error("[Lists:fetchContactLists] $this->requestId:$portalId, Exception: ".$e->getMessage());

            return [];
        }
    }

    public function createContactList(
        string $portalId,
        string $query,
        string $selectedListType = 'MANUAL'
    ): array {
        try {
            $listCreateRequest = new ListCreateRequest([
                'object_type_id' => 'contact',
                'processing_type' => $selectedListType,
                'name' => $query,
            ]);

            $response = $this->client->crm()->lists()->listsApi()->create($listCreateRequest);
            if (isset($response['list'])) {
                return json_decode(json_encode($response), true);
            }

            if (isset($response->status)) {
                Log::error("[Lists:createContactList] Error for $this->requestId:$portalId, response: ".json_encode($response));

                throw new Exception('Something went wrong');
            }

            return $response;
        } catch (\HubSpot\Client\Crm\Lists\ApiException $e) {
            $responseBody = $e->getResponseBody();
            $decodedBody = json_decode($responseBody, true);

            $errorMessage = $decodedBody['message'] ?? $e->getMessage();

            Log::error("[Lists:createContactList] $this->requestId:$portalId APIException: ".json_encode($decodedBody));
            Log::error("[Lists:createContactList] $this->requestId:$portalId APIException: ".$errorMessage);

            return ['message' => $errorMessage];
        } catch (Exception $e) {
            Log::error("[Lists:createContactList] $this->requestId:$portalId Exception: ".$e->getMessage());

            return ['message' => 'Something went wrong'];
        }
    }

    public function addMembersInList(
        string $portalId,
        string $listId,
        array $contactIds
    ): array {
        try {
            $response = $this->client->crm()->lists()->membershipsApi()->add(
                $listId,
                $contactIds
            );

            if (isset($response->status)) {
                Log::error("[Lists:addMembersInList] Error for $this->requestId:$portalId, response: ".json_encode($response));

                return [];
            }

            return json_decode(json_encode($response), true);
        } catch (Exception $e) {
            Log::error("[Lists:addMembersInList] $this->requestId:$portalId, Exception: ".$e->getMessage());

            return [];
        }
    }
}
