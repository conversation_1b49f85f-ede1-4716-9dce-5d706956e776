<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use App\Models\Role;
use App\Models\User;
use App\Hubspot\Hubspot;
use App\Models\PortalToken;
use Illuminate\Http\Request;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;
use PHPOpenSourceSaver\JWTAuth\Exceptions\JWTException;



class UserController extends Controller
{
    public function __construct()
    {
        parent::__construct('waba');
    }

    // get all users withing an account
    public function index(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|string',
        ]);

        try {
            // Fetch the account ID using the user_id
            $accountId = readUserId($validated['user_id']);

            // Fetch all users within the account
            $users = User::where('account_id', $accountId)
                ->with('permissions', 'role')
                ->where('declined', 0)
                ->get();

            // Format the response
            $formattedUsers = $users->map(function ($user) {
                $isAdmin = PortalToken::where(['portal_id' => $user->portal_id, 'user' => $user->email])->exists();

                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'admin' => $isAdmin,
                    'active' => $user->active,
                    'approved' => $user->approved,
                    'role' => $user->role ? $user->role->name : null,
                    'permissions' => $user->permissions->pluck('name'),
                ];
            });

            return $this->jsonOk([
                'users' => $formattedUsers,
            ]);
        } catch (Exception $e) {
            return $this->jsonError(['message' => $e->getMessage()], 404);
        }
    }

    /**
     * Get a user along with their role and permissions.
     */
    public function show(Request $request, $email)
    {
        $validated = $request->validate([
            'user_id' => 'required|string',
        ]);

        try {

            $accountId = readUserId($validated['user_id']);
            $portalId = readUserId($validated['user_id'], 'portalId');

            $portal = PortalToken::where(['portal_id' => $portalId])->first();
            if (! $portal->user) {
                return $this->jsonOk();
            }

            // Fetch the user
            $user = User::where(['account_id' => $accountId, 'email' => $email])->with('permissions', 'role')->first();
            // create admin user if doesn't exists
            if (! $user && $portal->user == $email) {
                $user = User::create([
                    'account_id' => $accountId,
                    'portal_id' => $portalId,
                    'name' => $email,
                    'email' => $email,
                    'approved' => 1,
                    'active' => 1,
                ]);
            }

            if (! $user) {
                throw new Exception('User not found', 1);
            }

            $isAdmin = ($portal->user === $email);
            $token = JWTAuth::fromUser($user);

            return $this->jsonOk([
                'user' => [
                    'token'=>$token,
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'admin' => $isAdmin,
                    'active' => $user->active,
                    'approved' => $user->approved,
                    'declined' => $user->declined,
                    'permissions' => $user->permissions->pluck('name'),
                ],
            ]);
        } catch (Exception $e) {
            return $this->jsonError(['message' => $e->getMessage()], 404);
        }
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|string',
            'email' => 'required|email',
            'name' => 'required|string|max:255',
            'role_id' => 'nullable|integer',
            'permission_ids' => 'nullable|array',
        ]);

        try {
            $accountId = readUserId($validated['user_id']);
            $portalId = readUserId($validated['user_id'], 'portalId');

            $user = User::where(['account_id' => $accountId, 'email' => $validated['email']])->with('permissions', 'role')->first();
            if ($user) {

                if ($user->declined == 0) {
                    return $this->jsonError(['message' => 'User already exists please try with another email'], 400);
                }
                $user->update(['declined' => 0, 'approved' => 1]);

            } else {

                $user = User::create([
                    'account_id' => $accountId,
                    'email' => $validated['email'],
                    'portal_id' => $portalId,
                    'name' => $validated['name'],
                    'role_id' => $validated['role_id'] ?? null,
                    'approved' => 1,
                ]);

            }

            if (isset($validated['permission_ids'])) {
                $user->permissions()->sync(
                    collect($validated['permission_ids'])->mapWithKeys(function ($id) use ($accountId) {
                        return [$id => ['account_id' => $accountId]];
                    })->toArray()
                );
            }

            return $this->jsonOk([
                'message' => 'User created successfully.',
                'user' => $user,
            ]);
        } catch (Exception $e) {
            return $this->jsonError(['message' => $e->getMessage()], 400);
        }
    }

    public function update(Request $request, $email)
    {
        $validated = $request->validate([
            'user_id' => 'required|string',
            'name' => 'nullable|string|max:255',
            'role_id' => 'nullable|integer', // This allows null or integer values
            'permission_ids' => 'nullable|array',
            'active' => 'nullable|boolean',
            'approved' => 'nullable|boolean',
            'declined' => 'nullable|boolean',
        ]);

        try {
            $accountId = readUserId($validated['user_id']);
            $user = User::where(['account_id' => $accountId, 'email' => $email])->firstOrFail();

            $user->fill([
                'name' => $validated['name'] ?? $user->name,
                'role_id' => array_key_exists('role_id', $validated)
                    ? ($validated['role_id']
                        ? optional(Role::where('id', $validated['role_id'])->where('account_id', $accountId)->first())->id
                        : null)
                    : $user->role_id,
                'active' => array_key_exists('active', $validated)
                    ? $validated['active']
                    : $user->active,
                'approved' => ($validated['declined'] ?? false) ? false : ($validated['approved'] ?? $user->approved),
                'declined' => ($validated['approved'] ?? false) ? false : ($validated['declined'] ?? $user->declined),
            ])->save();

            if (isset($validated['permission_ids'])) {
                $user->permissions()->sync(
                    collect($validated['permission_ids'])->mapWithKeys(function ($id) use ($accountId) {
                        return [$id => ['account_id' => $accountId]];
                    })->toArray()
                );
            }

            return $this->jsonOk(['message' => 'User updated successfully.', 'user' => $user]);
        } catch (Exception $e) {
            return $this->jsonError(['message' => $e->getMessage()], 400);
        }
    }

    /**
     * Assign one or multiple permissions to a user.
     */
    public function assignPermissions(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email',
            'user_id' => 'required|string',
            'permission_ids' => 'nullable|array',
            'approved' => 'nullable|boolean',
            'declined' => 'nullable|boolean',
        ]);

        try {
            $accountId = readUserId($validated['user_id']);
            // Fetch the user
            $user = User::where(['account_id' => $accountId, 'email' => $validated['email']])->firstOrFail();

            // Sync permissions, allowing for empty `permission_ids`
            $user->permissions()->sync($validated['permission_ids'] ?? []);
            // Update approved and declined status in a single save operation
            $user->fill([
                'approved' => $validated['declined'] ?? false ? false : ($validated['approved'] ?? $user->approved),
                'declined' => ($validated['approved'] ?? false) ? false : ($validated['declined'] ?? false),
            ])->save();

            return $this->jsonOk(['message' => 'Permissions updated successfully.']);
        } catch (Exception $e) {
            return $this->jsonError(['message' => $e->getMessage()], 400);
        }
    }

    public function refreshToken(Request $request)
    {
        try {
        // Refresh the token
        $newToken = JWTAuth::parseToken()->refresh();

        // Optionally get the user
        $user = JWTAuth::setToken($newToken)->toUser();

        return response()->json([
            'ok' => true,
            'user' => [
                'token' => $newToken,
                'id' => $user->id,
                'email' => $user->email,
                'name' => $user->name,
                'role' => $user->role,
                'active' => $user->active,
                'approved' => $user->approved,
                'permissions' => $user->permissions->pluck('name'),
            ],
        ]);
    } catch (JWTException $e) {
        return response()->json([
            'ok' => false,
            'message' => $e->getMessage()
        ], 401);
    }
    }

    public function requestAccess(Request $request, $email)
    {
        $input = $request->validate(['user_id' => 'required']);
        Log::info("[UserController:requestAccess] $this->requestId, email:$email, payload: ".json_encode($input));

        $accountId = readUserId($input['user_id']);
        $portalId = readUserId($input['user_id'], 'portalId');

        try {
            User::updateOrCreate(['account_id' => $accountId, 'email' => $email], [
                'name' => $email,
                'portal_id' => $portalId,
                'approved' => 0,
                'declined' => 0,
                'active' => 1,
            ]);

            // fetch admin email
            $hsApp = new Hubspot($portalId, $this->requestId);
            $userInfo = $hsApp->oauth()->get();
            if (! $userInfo) {
                return view('error', ['message' => 'Something went wrong please try again']);
            }

            $adminEmail = $userInfo->user;
            $searchData = [
                'filters' => [
                    [
                        'propertyName' => 'email',
                        'operator' => 'EQ',
                        'value' => $email,
                    ],
                ],
                'properties' => ['id', 'email'],
            ];

            $contacts = $hsApp->contacts()->search($searchData);

            if (! empty($contacts->results)) {
                // Contact exists, update it
                $contactId = $contacts->results[0]->id;

                $updateProperties = [
                    'vira_admin_email' => $adminEmail,
                    'vira_portal_id' => $portalId,
                ];

                $updatedContact = $hsApp->contacts()->update($contactId, $updateProperties);
                Log::info('[UserController:requestAccess] Updated contact: '.json_encode($updatedContact));
            } else {
                // Contact does not exist, create it
                $createProperties = [
                    'email' => $email,
                    'vira_admin_email' => $adminEmail,
                    'vira_portal_id' => $portalId,
                ];

                $newContact = $hsApp->contacts()->create($createProperties);
                Log::info('[UserController:requestAccess] Created new contact: '.json_encode($newContact));
            }

            return view('reqaccess');
        } catch (Exception $e) {
            Log::error("[UserController:requestAccess] $this->requestId, message: ".$e->getMessage().'-'.$e->getTraceAsString());

            return view('error', ['message' => 'Something went wrong']);
        }
    }
}
