<?php

namespace App\Helpers\Log;

class LogViewer
{
    /**
     * @var string file
     */
    private $file;

    /**
     * @var string folder
     */
    private $folder;

    /**
     * @var string storage_path
     */
    private $storagePath;

    /**
     * Why? Uh... Sorry
     */
    public const MAX_FILE_SIZE = 52428800;

    /**
     * @var Level level
     */
    private $level;

    /**
     * @var Pattern pattern
     */
    private $pattern;

    /**
     * LaravelLogViewer constructor.
     */
    public function __construct()
    {
        $this->level = new Level;
        $this->pattern = new Pattern;
        $this->storagePath = function_exists('config')
                            ? config('logviewer.storage_path', storage_path('logs'))
                            : storage_path('logs');

    }

    /**
     * @param  string  $folder
     */
    public function setFolder($folder)
    {
        if (app('files')->exists($folder)) {
            $this->folder = $folder;
        }
        if (is_array($this->storagePath)) {
            foreach ($this->storagePath as $value) {
                $logsPath = $value.'/'.$folder;
                if (app('files')->exists($logsPath)) {
                    $this->folder = $folder;

                    break;
                }
            }
        } else {
            if ($this->storagePath) {
                $logsPath = $this->storagePath.'/'.$folder;
                if (app('files')->exists($logsPath)) {
                    $this->folder = $folder;
                }
            }
        }
    }

    /**
     * @param  string  $file
     *
     * @throws \Exception
     */
    public function setFile($file)
    {
        $file = $this->pathToLogFile($file);

        if (app('files')->exists($file)) {
            $this->file = $file;
        }
    }

    /**
     * @param  string  $file
     * @return string
     *
     * @throws \Exception
     */
    public function pathToLogFile($file)
    {

        if (app('files')->exists($file)) { // try the absolute path
            return $file;
        }
        if (is_array($this->storagePath)) {
            foreach ($this->storagePath as $folder) {
                if (app('files')->exists($folder.'/'.$file)) { // try the absolute path
                    $file = $folder.'/'.$file;

                    break;
                }
            }

            return $file;
        }

        $logsPath = $this->storagePath;
        $logsPath .= ($this->folder) ? '/'.$this->folder : '';
        $file = $logsPath.'/'.$file;
        // check if requested file is really in the logs directory
        if (dirname($file) !== $logsPath) {
            throw new \Exception('No such log file'); // NOSONAR
        }

        return $file;
    }

    /**
     * @return string
     */
    public function getFolderName()
    {
        return $this->folder;
    }

    /**
     * @return string
     */
    public function getFileName()
    {
        return basename($this->file);
    }

    /**
     * @return array
     */
    public function all()
    {
        $log = [];

        if (! $this->file) {
            $logFiles = $this->folder ? $this->getFolderFiles() : $this->getFiles();
            $this->file = $logFiles[1] ?? $logFiles[0] ?? null;
        }

        $fileContent = $this->readFile($this->file);
        $headingsPattern = $this->pattern->getPattern('logs');
        $currentLogPatternPart1 = $this->pattern->getPattern('current_log', 0);
        $currentLogPatternPart2 = $this->pattern->getPattern('current_log', 1);

        preg_match_all($headingsPattern, $fileContent, $headings);
        $logData = preg_split($headingsPattern, $fileContent);

        if ($logData[0] < 1) {
            array_shift($logData);
        }

        $levels = $this->level->all();

        foreach ($headings[0] as $i => $heading) {
            $logEntries = $this->processHeading(
                $heading,
                $levels,
                $currentLogPatternPart1,
                $currentLogPatternPart2,
                $logData,
                $i
            );
            $log = array_merge($log, $logEntries);
        }

        if (empty($log)) {
            $log = $this->processEmptyLog($fileContent);
        }

        return array_reverse($log);
    }

    private function processHeading($heading, $levels, $patternPart1, $patternPart2, $logData, $index)
    {
        $logEntries = [];
        foreach ($levels as $level) {
            if ($this->isMatchingLevel($heading, $level)) {
                preg_match($patternPart1.$level.$patternPart2, $heading, $current);

                if (isset($current[4])) {
                    $logEntries[] = [
                        'context' => $current[3],
                        'level' => $level,
                        'folder' => $this->folder,
                        'level_class' => $this->level->cssClass($level),
                        'level_img' => $this->level->img($level),
                        'date' => $current[1],
                        'text' => $current[4],
                        'in_file' => $current[5] ?? null,
                        'stack' => preg_replace("/^\n*/", '', $logData[$index]),
                    ];
                }
            }
        }

        return $logEntries;
    }

    private function isMatchingLevel($heading, $level)
    {
        $headingLower = strtolower($heading);

        return strpos($headingLower, '.'.$level) !== false || strpos($headingLower, $level.':') !== false;
    }

    private function processEmptyLog($fileContent)
    {
        $lines = explode(PHP_EOL, $fileContent);
        $log = [];

        foreach ($lines as $key => $line) {
            $log[] = [
                'context' => '',
                'level' => '',
                'folder' => '',
                'level_class' => '',
                'level_img' => '',
                'date' => $key + 1,
                'text' => $line,
                'in_file' => null,
                'stack' => '',
            ];
        }

        return $log;
    }

    public function tailCustom($filepath, $lines = 1, $adaptive = true)
    {

        // Open file
        $f = @fopen($filepath, 'rb');
        if ($f === false) {
            return false;
        }

        // Sets buffer size, according to the number of lines to retrieve.
        // This gives a performance boost when reading a few lines from the file.
        if (! $adaptive) {
            $buffer = 4096;
        } else {
            if ($lines < 2) {
                $buffer = 64;
            } elseif ($lines < 10) {
                $buffer = 512;
            } else {
                $buffer = 4096;
            }
        }

        // Jump to last character
        fseek($f, -1, SEEK_END);

        // Read it and adjust line number if necessary
        // (Otherwise the result would be wrong if file doesn't end with a blank line)
        if (fread($f, 1) != "\n") {
            $lines -= 1;
        }

        // Start reading
        $output = '';
        $chunk = '';

        // While we would like more
        while (ftell($f) > 0 && $lines >= 0) {

            // Figure out how far back we should jump
            $seek = min(ftell($f), $buffer);

            // Do the jump (backwards, relative to where we are)
            fseek($f, -$seek, SEEK_CUR);

            // Read a chunk and prepend it to our output
            $output = ($chunk = fread($f, $seek)).$output;

            // Jump back to where we started reading
            fseek($f, -mb_strlen($chunk, '8bit'), SEEK_CUR);

            // Decrease our line counter
            $lines -= substr_count($chunk, "\n");

        }

        // While we have too many lines
        // (Because of buffer size we might have read too many)
        while ($lines++ < 0) {

            // Find first newline and remove all text before that
            $output = substr($output, strpos($output, "\n") + 1);

        }

        // Close file and return
        fclose($f);

        return trim($output);

    }

    public function readFile($file)
    {
        return $this->tailCustom($file, 5000);
    }

    /**
     * @return array
     */
    public function getFolders()
    {
        $folders = glob($this->storagePath.'/*', GLOB_ONLYDIR);
        if (is_array($this->storagePath)) {
            foreach ($this->storagePath as $value) {
                $folders = array_merge(
                    $folders,
                    glob($value.'/*', GLOB_ONLYDIR)
                );
            }
        }

        if (is_array($folders)) {
            foreach ($folders as $k => $folder) {
                $folders[$k] = basename($folder);
            }
        }

        return array_values($folders);
    }

    /**
     * @param  bool  $basename
     * @return array
     */
    public function getFolderFiles($basename = false)
    {
        return $this->getFiles($basename, $this->folder);
    }

    /**
     * @param  bool  $basename
     * @param  string  $folder
     * @return array
     */
    public function getFiles($basename = false, $folder = '')
    {
        $pattern = function_exists('config') ? config('logviewer.pattern', '*.log') : '*.log';
        $files = glob(
            $this->storagePath.'/'.$folder.'/'.$pattern,
            preg_match($this->pattern->getPattern('files'), $pattern) ? GLOB_BRACE : 0
        );
        if (is_array($this->storagePath)) {
            foreach ($this->storagePath as $value) {
                $files = array_merge(
                    $files,
                    glob(
                        $value.'/'.$folder.'/'.$pattern,
                        preg_match($this->pattern->getPattern('files'), $pattern) ? GLOB_BRACE : 0
                    )
                );
            }
        }

        $files = array_reverse($files);
        $files = array_filter($files, 'is_file');
        if ($basename && is_array($files)) {
            foreach ($files as $k => $file) {
                $files[$k] = basename($file);
            }
        }

        return array_values($files);
    }
}
