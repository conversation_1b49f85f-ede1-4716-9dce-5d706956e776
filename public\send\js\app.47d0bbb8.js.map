{"version": 3, "file": "js/app.47d0bbb8.js", "mappings": "mEAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,EAAGF,EAAII,UAAYJ,EAAIK,UAAWH,EAAG,cAAcA,EAAG,eAAe,EACjJ,EACII,EAAkB,G,oCCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAQD,EAAIG,MAAMD,GAAG,OAAOF,EAAIO,GAAG,EAC1E,EACID,EAAkB,CAAC,WAAY,IAAIN,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,KAAK,CAACF,EAAIS,GAAG,6BAC7H,GCIA,GACAC,KAAA,UCRqP,I,UCQjPC,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIF,EAAeA,EAAiB,QCnB5BZ,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,MAAM,CAACM,YAAY,aAAaI,MAAM,CAAEC,IAAKb,EAAIc,SAAU,CAACZ,EAAG,MAAM,CAACM,YAAY,kBAAkBI,MAAM,CAAEG,UAAWf,EAAIgB,SAAU,CAACd,EAAG,gBAAgB,MACzO,EACII,EAAkB,GCYtB,GACAI,KAAA,SACAO,WAAA,GACAC,SAAA,CACAF,MAAAA,GACA,YAAAG,OAAAC,MAAAC,WACA,EACAP,MAAAA,GACA,MAAAQ,EAAA,KAAAC,OAAAb,KACA,qBAAAY,CAIA,IC3BqP,ICOjP,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIF,EAAe,EAAiB,Q,syBCHhC,GACAZ,KAAA,MACAO,WAAA,CAAAO,UAAA,EAAAC,UAAAA,GAEAC,IAAAA,GACA,OACAC,SAAA,EACAC,SAAA,KAEA,EAEAV,SAAA,KACAW,EAAAA,EAAAA,IAAA,2BAGAC,MAAA,CACAzB,SAAAA,GACA,KAAAA,WACA0B,cAAA,KAAAH,SAEA,GAGAI,QAAA,KACAC,EAAAA,EAAAA,IAAA,CACA,cACA,cACA,iBACA,YACA,cACA,qBAGAC,OAAAA,GACA,IAAAC,EAAA,GACA,IAAAC,EAAAC,OAAAC,SAAAC,KACAC,EAAA,IAAAC,IAAAL,GACAD,EAAAO,QAAAF,EAAAG,aAAAC,IAAA,WACAT,EAAAU,UAAAL,EAAAG,aAAAC,IAAA,aACAT,EAAAW,YAAAN,EAAAG,aAAAC,IAAA,eACAT,EAAAY,aAAAP,EAAAG,aAAAC,IAAA,gBAEA,KAAAjB,QACA,KAAAqB,YAAAC,GAIA,KAAAD,YAAAb,EACA,EAEAe,kBAAAA,CAAAC,GACA,WAAAC,GAAAD,EACA,WAAAC,EACAf,OAAAC,SAAAe,UAEA,KAAAC,aAAA,GACA,KAAAC,eAAA,oBAEA,GAGAC,OAAAA,GAEA,KAAAtB,SACA,GC/EyO,ICOrO,GAAY,OACd,EACAnC,EACAO,GACA,EACA,KACA,KACA,MAIF,EAAe,EAAiB,Q,SClB5BP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,uBAAuB,CAAGR,EAAIyD,WAA2BzD,EAAI0D,oBAAuB1D,EAAI2D,SAAW3D,EAAI4D,aAAe1D,EAAG,cAAc,CAAC2D,MAAM,CAAC,YAAY7D,EAAII,SAAS,kBAAkBJ,EAAI8D,cAAc,MAAQ9D,EAAI+D,MAAM,WAAa/D,EAAIgE,WAAW,aAAehE,EAAIiE,aAAa,WAAajE,EAAIkE,WAAW,iBAAmBlE,EAAImE,iBAAiB,iBAAmBnE,EAAIoE,iBAAiB,kBAAoBpE,EAAIqE,kBAAkB,eAAerE,EAAIsE,YAAY,SAAWtE,EAAIuE,SAAS,kBAAkBvE,EAAIwE,eAAe,gBAAgBxE,EAAIyE,aAAa,SAAWzE,EAAI0E,SAAS,QAAU1E,EAAI2E,QAAQ,OAAS3E,EAAI4E,OAAO,cAAc5E,EAAI6E,WAAW,cAAc7E,EAAI8E,WAAW,kBAAkB9E,EAAI+E,eAAe,UAAY/E,EAAIgF,UAAU,gBAAgBhF,EAAIiF,aAAa,kBAAkBjF,EAAIkF,eAAe,WAAWlF,EAAImF,QAAQ,mBAAmBnF,EAAIoF,eAAe,oBAAoBpF,EAAIqF,gBAAgB,qBAAqBrF,EAAIsF,iBAAiB,qBAAqBtF,EAAIuF,iBAAiB,uBAAuBvF,EAAIwF,mBAAmB,gBAAgBxF,EAAIyF,aAAa,gBAAgBzF,EAAI0F,aAAa,iBAAmB1F,EAAI2F,iBAAiB,kBAAkB3F,EAAIqB,YAAY,aAAerB,EAAI4F,cAAcC,GAAG,CAAC,sBAAsB7F,EAAI8F,kBAAkB,eAAe9F,EAAI+F,YAAY,iBAAiB/F,EAAIgG,cAAc,YAAYhG,EAAIiG,SAAS,sBAAsBjG,EAAIkG,kBAAkB,gBAAgBlG,EAAImG,aAAa,sBAAsBnG,EAAIoG,kBAAkB,mBAAmBpG,EAAIqG,kBAAkBnG,EAAG,OAAO,CAAC2D,MAAM,CAAC,QAAU,8DAAj+C3D,EAAG,YAA6hD,EACnqD,EACII,EAAkB,GCFlBP,G,QAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,UAAU,CAACN,EAAG,MAAM,CAACM,YAAY,kBAAkB8F,MAAO,CAACtG,EAAIuG,UAAW,CAACrG,EAAG,MAAM,CAACM,YAAY,qBAAqBI,MAAM,CAAE,kBAAmBZ,EAAIwG,cAAe,CAACtG,EAAG,OAAO,CAAC2D,MAAM,CAAC,kBAAkB7D,EAAI8D,cAAc,MAAQ9D,EAAI+D,MAAM,UAAU/D,EAAIyG,KAAKC,QAAU,GAAG,kBAAkB1G,EAAI2G,cAAc,SAAW3G,EAAIuE,SAAS,eAAevE,EAAI4G,YAAY,kBAAkB5G,EAAIwE,eAAe,eAAexE,EAAI6G,YAAY,kBAAkB7G,EAAI8G,eAAe,iBAAiB9G,EAAI+G,aAAa,aAAa/G,EAAIgH,UAAU,aAAahH,EAAIiH,UAAU,iBAAiBjH,EAAIkH,aAAa,oBAAoBlH,EAAImH,gBAAgB,cAAcnH,EAAIoH,WAAW,uBAAuBpH,EAAIqH,mBAAmB,4BAA4BrH,EAAIsH,uBAAuB,cAActH,EAAIuH,WAAW,gBAAgBvH,EAAIwH,EAAE,cAAcxH,EAAIyH,WAAW,kBAAkBzH,EAAI0H,cAAc,kBAAkB1H,EAAI2H,eAAe,eAAe3H,EAAI4H,YAAY,aAAY,EAAM,gBAAgB5H,EAAIyF,aAAa,0BAA0BzF,EAAI6H,sBAAsB,iBAAiB7H,EAAI8H,cAAc,iBAAiB9H,EAAI+H,cAAc,UAAY/H,EAAIgF,UAAU,aAAehF,EAAI4F,aAAa,gBAAgB5F,EAAI0F,aAAa,iBAAmB1F,EAAI2F,kBAAkBE,GAAG,CAAC,qBAAqB7F,EAAIuF,iBAAiB,iBAAiBvF,EAAIgG,cAAc,eAAehG,EAAI+F,YAAY,eAAe/F,EAAIgI,YAAY,iBAAiBhI,EAAIiI,cAAc,YAAYjI,EAAIiG,SAAS,wBAAwBjG,EAAIkI,oBAAoB,iBAAiBlI,EAAImI,cAAc,0BAA0BnI,EAAIoI,sBAAsB,mBAAmBpI,EAAIqI,gBAAgB,kBAAkB,SAASC,GAAQ,OAAOtI,EAAIuI,MAAM,kBAAkB,EAAE,mBAAmBvI,EAAIwI,mBAAmB,sBAAsB,SAASF,GAAQ,OAAOtI,EAAIuI,MAAM,sBAAuBvI,EAAIyG,KAAK,GAAGgC,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,MAAS,KAAKxB,EAAG,eAAe,CAAC2D,MAAM,CAAC,KAAO7D,EAAIiJ,aAAa,MAAQjJ,EAAIkJ,cAAc,OAASlJ,EAAImJ,gBAAgBjJ,EAAG,cAAc,CAAC2D,MAAM,CAAC,KAAO7D,EAAIoF,eAAe,OAASpF,EAAIuF,iBAAiB,gBAAgBtF,KAAKgF,gBAAgB/E,EAAG,gBAAgB,CAAC2D,MAAM,CAAC,KAAO7D,EAAIsF,iBAAiB,OAAStF,EAAIwF,mBAAmB,kBAAkBxF,EAAIkF,kBAAkBhF,EAAG,eAAe,CAAC2D,MAAM,CAAC,KAAO7D,EAAIoJ,aAAa,IAAMpJ,EAAIqJ,eAAe,MAAQrJ,EAAIsJ,qBAAqB,EACt+E,GACIhJ,EAAkB,GCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAQF,EAAIuJ,KAAMrJ,EAAG,MAAM,CAACM,YAAY,eAAeqD,MAAM,CAAC,GAAK,aAAa,CAAC3D,EAAG,MAAM,CAACM,YAAY,uBAAuB,CAACN,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,OAAO,CAACM,YAAY,cAAc,CAACR,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAIyJ,eAAevJ,EAAG,OAAO,CAACM,YAAY,cAAc,CAACR,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAI0J,gBAAgBxJ,EAAG,MAAM,CAACM,YAAY,6BAA6B,CAACN,EAAG,MAAM,CAAC2F,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,qBAAsB,CAAEoB,IAAK3J,EAAI4J,OAAO5J,EAAI6J,aAAaC,QAAS,IAAI,CAAC5J,EAAG,MAAM,CAACM,YAAY,eAAeqD,MAAM,CAAC,QAAU,YAAY,MAAQ,KAAK,OAAS,OAAO,CAAC3D,EAAG,OAAO,CAAC2D,MAAM,CAAC,KAAO,eAAe,EAAI,2OAA2O3D,EAAG,MAAM,CAAC2F,GAAG,CAAC,MAAQ7F,EAAIiG,WAAW,CAAC/F,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,eAAe,GAAG3D,EAAG,SAAS,CAACM,YAAY,4BAA4BqF,GAAG,CAAC,MAAQ7F,EAAI+J,QAAQ,CAAC/J,EAAIS,GAAG,WAAWP,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACN,EAAG,MAAM,CAACM,YAAY,WAAW,CAACN,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAIgK,aAAa,IAAM,qBAAqB9J,EAAG,MAAM,CAACM,YAAY,2BAA2BR,EAAI2I,GAAI3I,EAAI4J,QAAQ,SAASK,EAAMC,GAAO,OAAOhK,EAAG,MAAM,CAAC4I,IAAImB,EAAME,GAAGvJ,MAAM,CAAC,kBAAmBZ,EAAI6J,aAAeK,EAAQ,SAAW,IAAIrE,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAIoK,cAAcF,EAAM,IAAI,CAAChK,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAMoG,EAAMI,UAAU,IAAG,GAAGnK,EAAG,MAAM,CAACM,YAAY,wBAAwB,CAACN,EAAG,OAAO,CAACM,YAAY,OAAOqF,GAAG,CAAC,MAAQ7F,EAAIsK,YAAY,CAACpK,EAAG,MAAM,CAAC2D,MAAM,CAAC,QAAU,YAAY,MAAQ,KAAK,OAAS,OAAO,CAAC3D,EAAG,OAAO,CAAC2D,MAAM,CAAC,KAAO,eAAe,EAAI,qFAAqF3D,EAAG,OAAO,CAACM,YAAY,OAAOqF,GAAG,CAAC,MAAQ7F,EAAIuK,YAAY,CAACrK,EAAG,MAAM,CAAC2D,MAAM,CAAC,QAAU,YAAY,MAAQ,KAAK,OAAS,OAAO,CAAC3D,EAAG,OAAO,CAAC2D,MAAM,CAAC,KAAO,eAAe,EAAI,uFAAuF7D,EAAIwK,IAC3mE,EACIlK,EAAkB,GCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAC2D,MAAM,CAAC,MAAQ7D,EAAIyK,OAAO,MAAQ,6BAA6B,cAAc,+BAA+B,QAAU,MAAM,MAAQ,KAAK,OAAS,KAAK,QAAU,OAAOzK,EAAI0K,QAAQ1K,EAAI0K,SAAS,CAACxK,EAAG,OAAO,CAAC2D,MAAM,CAAC,GAAK7D,EAAI2K,MAAM,EAAI3K,EAAI4K,QAAQ5K,EAAIU,MAAMmK,QAAS7K,EAAI4K,QAAQ5K,EAAIU,MAAMoK,MAAO5K,EAAG,OAAO,CAAC2D,MAAM,CAAC,GAAK7D,EAAI2K,MAAM,EAAI3K,EAAI4K,QAAQ5K,EAAIU,MAAMoK,SAAS9K,EAAIwK,MACxb,EACIlK,EAAkB,GCctB,GACAI,KAAA,UAEAqK,MAAA,CACArK,KAAA,CAAA0C,KAAA4H,OAAAC,QAAA,MACAC,MAAA,CAAA9H,KAAA4H,OAAAC,QAAA,MACAR,OAAA,CAAArH,KAAA4H,OAAAC,QAAA,OAGAvJ,IAAAA,GACA,OACAkJ,QAAA,CACAO,OAAA,CACAN,KAAA,qQAEAO,IAAA,CACAP,KAAA,qHAEAQ,OAAA,CACAR,KAAA,4FAGAS,KAAA,CACAT,KAAA,kNAEAd,MAAA,CACAc,KAAA,4MAEAU,OAAA,CACAV,KAAA,ieAEAW,KAAA,CACAX,KAAA,iIAEAY,UAAA,CACAZ,KAAA,8wBAEA,iBACAA,KAAA,iHAEAa,KAAA,CACAb,KAAA,qCAEAc,MAAA,CACAd,KAAA,qWAEAe,SAAA,CACAf,KAAA,6CAEAgB,OAAA,CACAhB,KAAA,uJAEAiB,UAAA,CACAjB,KAAA,2DAEA,oBACAA,KAAA,sJAEAkB,KAAA,CACAlB,KAAA,0pBAEAmB,IAAA,CACAnB,KAAA,qPAEAoB,MAAA,CACApB,KAAA,gGAGAqB,SAAA,CACArB,KAAA,8DAEAsB,QAAA,CACAtB,KAAA,+PAEAuB,WAAA,CACA1B,KAAA,QACAG,KAAA,mzBAEA,cACAH,KAAA,SACAG,KAAA,6RAEA,eACAH,KAAA,SACAG,KAAA,oKACAC,MACA,yKAIA,EAEA5J,SAAA,CACAyJ,KAAAA,GACA,MAAAO,EAAA,KAAAA,MAAA,SAAAA,MAAA,GACA,uBAAAxK,OAAAwK,GACA,EACAR,IAAAA,GACA,MAAA2B,EAAA,KAAAzB,QAAA,KAAAlK,MAEA,gBAAA2L,EAAA3B,KAAA,IACA,WAAA2B,EAAA3B,KAAA,GACA,EACA,ICvHwQ,ICOpQ,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIF,EAAe,EAAiB,QC0ChC,GACAhK,KAAA,WACAO,WAAA,CAAAqL,QAAAA,GACAvB,MAAA,CACAxB,KAAA,CAAAnG,KAAAmJ,SACAxC,MAAA,CAAA3G,KAAAoJ,SAAAvB,QAAAA,KAAA,KACArB,OAAA,CAAAxG,KAAAqJ,MAAAxB,QAAAA,IAAA,KAEAyB,MAAA,uBACAhL,IAAAA,GACA,OACAmI,YAAA,EAEA,EACA3I,SAAA,CACA8I,YAAAA,GACA,YAAAJ,OAAA,KAAAC,cAAA8C,GACA,EACAlD,UAAAA,GACA,YAAAG,OAAA,KAAAC,cAAA+C,QACA,EACAC,SAAAA,GACA,YAAAjD,OAAA,KAAAC,cAAAnJ,IACA,EACAgJ,SAAAA,GACA,YAAAE,OAAA,KAAAC,cAAAiD,KAAA,YAAAlD,OAAA,KAAAC,cAAAkD,SACA,GAEA/K,QAAA,CACAuI,SAAAA,GACA,IAAAvJ,EAAA,KAAA6I,YAAA,EACA7I,GAAA,KAAA4I,OAAAoD,SACAhM,EAAA,GAEA,KAAAoJ,cAAApJ,EACA,EACAsJ,SAAAA,GACA,IAAAtJ,EAAA,KAAA6I,YAAA,EACA7I,EAAA,IACAA,EAAA,KAAA4I,OAAAoD,OAAA,GAEA,KAAA5C,cAAApJ,EACA,EACAoJ,aAAAA,CAAA6C,GACA,KAAApD,YAAAoD,CACA,EACA,cAAAhH,GACA,MAAAgE,QAAAiD,MAAA,KAAAlD,cACAmD,QAAAlD,EAAAkD,OACAC,EAAAxB,SAAAyB,cAAA,KACAD,EAAA7K,KAAAE,IAAA6K,gBAAAH,GACAC,EAAAG,SAAA,KAAAV,UACAO,EAAAI,QACA/K,IAAAgL,gBAAAL,EAAA7K,KACA,IClHyQ,ICOrQ,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIF,EAAe,EAAiB,QClB5BxC,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACwN,WAAW,CAAC,CAAChN,KAAK,OAAOiN,QAAQ,SAASC,MAAO5N,EAAIuJ,KAAMsE,WAAW,SAASrN,YAAY,eAAe,CAACN,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAAO,eAAe,CAAE7D,EAAIuJ,KAAMrJ,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAI8N,UAAU,IAAM,gBAAgB5N,EAAG,IAAI,CAACF,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAIiF,mBAAmBjF,EAAIwK,OAAOtK,EAAG,MAAM,CAACM,YAAY,sBAAsBqF,GAAG,CAAC,MAAQ,SAASyC,GAAgC,OAAxBA,EAAOyF,iBAAwB/N,EAAIqL,OAAO2C,MAAM,KAAMC,UAAU,MAAM,EAC9hB,EACI3N,EAAkB,G,oCCctB,GACAI,KAAA,aACAqK,MAAA,CACAxB,KAAA,CAAAnG,KAAAmJ,SACAlB,OAAA,CAAAjI,KAAAoJ,SAAAvB,QAAAA,KAAA,KACAhG,aAAA,CAAA7B,KAAA4H,OAAAC,QAAA,0BAEAvJ,IAAAA,GACA,OACAoM,UAAAI,EAEA,GC3B2Q,ICOvQ,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIF,EAAe,EAAiB,QClB5BnO,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACwN,WAAW,CAAC,CAAChN,KAAK,OAAOiN,QAAQ,SAASC,MAAO5N,EAAIuJ,KAAMsE,WAAW,SAASrN,YAAY,iBAAiB,CAACN,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAAO,eAAe,CAAE7D,EAAIuJ,KAAMrJ,EAAG,MAAM,CAACM,YAAY,yBAAyB,CAACN,EAAG,MAAM,CAACM,YAAY,kBAAkB,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAIkF,eAAeiJ,SAAS,KAAKjO,EAAG,SAAS,CAACM,YAAY,eAAeqF,GAAG,CAAC,MAAQ,SAASyC,GAAgC,OAAxBA,EAAOyF,iBAAwB/N,EAAIqL,OAAO2C,MAAM,KAAMC,UAAU,IAAI,CAACjO,EAAIS,GAAG,SAASP,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,IAAI,CAACF,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAIkF,eAAekJ,gBAAgBpO,EAAIwK,OAAOtK,EAAG,MAAM,CAACM,YAAY,wBAAwBqF,GAAG,CAAC,MAAQ,SAASyC,GAAgC,OAAxBA,EAAOyF,iBAAwB/N,EAAIqL,OAAO2C,MAAM,KAAMC,UAAU,MAAM,EACzxB,EACI3N,EAAkB,GCiBtB,GACAI,KAAA,eACAqK,MAAA,CACAxB,KAAA,CAAAnG,KAAAmJ,SACAlB,OAAA,CAAAjI,KAAAoJ,SAAAvB,QAAAA,KAAA,KACA/F,eAAA,CACA9B,KAAAiL,OACAC,UAAA,KC1B6Q,ICOzQ,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIF,GAAe,EAAiB,QClB5BvO,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAQF,EAAIuJ,KAAMrJ,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,MAAM,CAACM,YAAY,wBAAwB,CAACN,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,OAAO,CAACM,YAAY,cAAc,CAACR,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAIuO,IAAI3B,aAAa1M,EAAG,OAAO,CAACM,YAAY,cAAc,CAACR,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAI0J,gBAAgBxJ,EAAG,MAAM,CAACM,YAAY,6BAA6B,CAACN,EAAG,MAAM,CAACM,YAAY,gBAAgBqF,GAAG,CAAC,MAAQ7F,EAAIiG,WAAW,CAAC/F,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,cAAc7D,EAAIO,GAAG,IAAI,GAAGL,EAAG,MAAM,CAACM,YAAY,6BAA6BqF,GAAG,CAAC,MAAQ7F,EAAI+J,QAAQ,CAAC7J,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,WAAW7D,EAAIO,GAAG,IAAI,OAAOL,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAACN,EAAG,MAAM,CAACM,YAAY,WAAW,CAACN,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAIuO,IAAI/L,IAAI,IAAMxC,EAAIuO,IAAI7N,gBAAgBV,EAAIwK,IAC90B,EACIlK,GAAkB,CAAC,WAAY,IAAIN,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,IAAI,CAACM,YAAY,oBAAoB,CAACR,EAAIS,GAAG,eAClK,EAAE,WAAY,IAAIT,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,IAAI,CAACM,YAAY,oBAAoB,CAACR,EAAIS,GAAG,YAC7I,GCgCA,IACAC,KAAA,cACAO,WAAA,CAAAqL,QAAAA,GACAvB,MAAA,CACAxB,KAAA,CAAAnG,KAAAmJ,SACAxC,MAAA,CAAA3G,KAAAoJ,SAAAvB,QAAAA,KAAA,KACAsD,IAAA,CAAAnL,KAAAiL,OAAAC,UAAA,IAEA5B,MAAA,uBACAxL,SAAA,CACAwI,SAAAA,GACA,YAAA6E,IAAAzB,KAAA,YAAAyB,IAAAxB,SACA,GAGA/K,QAAA,CACA,cAAAiE,GACA,MAAAuI,EAAA5C,SAAAyB,cAAA,KACAmB,EAAAjM,KAAA,KAAAgM,IAAA/L,IACAgM,EAAAC,OAAA,SACAD,EAAAjB,SAAA,KAAAgB,IAAA/L,IAAAkM,MAAA,KAAAC,MACAC,QAAAC,IAAAL,EAAAjB,UACA3B,SAAAkD,KAAAC,YAAAP,GACAA,EAAAhB,QACA5B,SAAAkD,KAAAE,YAAAR,EAsBA,IClF4Q,MCQxQ,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,Q,sBCnB5BzO,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,wCAAwC,CAAER,EAAIiP,cAAe/O,EAAG,QAAQF,EAAIwK,KAAKtK,EAAG,MAAM,CAACwN,WAAW,CAAC,CAAChN,KAAK,OAAOiN,QAAQ,SAASC,MAAQ5N,EAAIkP,WAAalP,EAAI0H,gBAAmB1H,EAAIkP,UAAYlP,EAAIyH,WAAYoG,WAAW,4DAA4DrN,YAAY,mBAAmBqF,GAAG,CAAC,WAAa7F,EAAImP,aAAa,CAAEnP,EAAIoP,WAAYpP,EAAIgJ,GAAG,oBAAmB,WAAW,MAAO,CAAC9I,EAAG,MAAM,CAACM,YAAY,uCAAuC,CAACN,EAAG,MAAM,CAACF,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAIqP,aAAaC,iBAAiB,IAAGpP,EAAG,cAAc,CAAC2D,MAAM,CAAC,kBAAkB7D,EAAI8D,cAAc,gBAAgB9D,EAAIqP,aAAa,cAAcrP,EAAIyH,WAAW,kBAAkBzH,EAAI0H,cAAc,YAAY1H,EAAIkP,SAAS,eAAelP,EAAI6G,YAAY,aAAe7G,EAAI4F,aAAa,KAAO5F,EAAIyG,KAAK,UAAYzG,EAAIgF,WAAWa,GAAG,CAAC,wBAAwB7F,EAAIuP,oBAAoB,eAAevP,EAAIwP,YAAY,mBAAmBxP,EAAIwI,mBAAmB,oBAAoB,SAASF,GAAQ,OAAOtI,EAAIuI,MAAM,oBAAoB,EAAE,kBAAkB,SAASD,GAAQ,OAAOtI,EAAIuI,MAAM,kBAAkB,EAAE,sBAAsB,SAASD,GAAQ,OAAOtI,EAAIuI,MAAM,sBAAuBvI,EAAIyG,KAAK,GAAGgC,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,KAAQxB,EAAG,MAAM,CAACuP,IAAI,kBAAkBjP,YAAY,uBAAuBqF,GAAG,CAAC,OAAS7F,EAAI0P,oBAAoB,CAACxP,EAAG,MAAM,CAACM,YAAY,0BAA0B,CAACN,EAAG,MAAM,CAACU,MAAM,CAAE,sBAAuBZ,EAAI2P,kBAAmB,CAACzP,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAAO,qBAAqB,CAAC3D,EAAG,MAAM,CAAEF,EAAI4P,eAAgB1P,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACR,EAAIgJ,GAAG,kBAAiB,WAAW,MAAO,CAAChJ,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAIqP,aAAaQ,gBAAgB,KAAK,KAAI,GAAG7P,EAAIwK,KAAMxK,EAAI8P,oBAAqB5P,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAIqP,aAAaU,sBAAsB,IAAI/P,EAAIwJ,GAAGxJ,EAAIuE,SAAS,GAAGuI,MAAM,OAAO9M,EAAIwK,SAAStK,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAAO,qBAAqB,CAAE7D,EAAIuE,SAASyI,OAAQ9M,EAAG,mBAAmB,CAACU,MAAM,CAAE,wBAAyBZ,EAAIwE,gBAAiBX,MAAM,CAAC,6BAA6B,wBAAwB,qBAAqB,oBAAoB,QAAU,SAAS,UAAY,MAAM,SAAW,IAAIgC,GAAG,CAAC,SAAW7F,EAAIgQ,kBAAkBvH,YAAYzI,EAAI0I,GAAG,CAAC,CAACI,IAAI,UAAUC,GAAG,WAAW,MAAO,CAAC7I,EAAG,SAAS,CAAC2D,MAAM,CAAC,MAAO,EAAK,UAAW,KAAQ,EAAEoM,OAAM,GAAM,CAACnH,IAAI,aAAaC,GAAG,WAAW,MAAO,CAAC7I,EAAG,OAAO,EAAE+P,OAAM,GAAM,CAACnH,IAAI,UAAUC,GAAG,WAAW,MAAO,CAAC7I,EAAG,OAAO,EAAE+P,OAAM,IAAO,MAAK,EAAM,cAAcjQ,EAAIwK,MAAM,GAAGtK,EAAG,mBAAmB,CAAC4I,IAAI9I,EAAI0G,OAAO7C,MAAM,CAAC,KAAO,mBAAmB,IAAM,SAAS7D,EAAI2I,GAAI3I,EAAIuE,UAAU,SAAS2L,EAAErH,GAAG,OAAO3I,EAAG,MAAM,CAAC4I,IAAIoH,EAAEC,SAAWD,EAAEvG,KAAK,CAACzJ,EAAG,UAAU,CAAC2D,MAAM,CAAC,kBAAkB7D,EAAI8D,cAAc,QAAUoM,EAAE,MAAQrH,EAAE,SAAW7I,EAAIuE,SAAS,iBAAiBvE,EAAIoQ,cAAc,kBAAkBpQ,EAAI8G,eAAe,aAAa9G,EAAIyG,KAAK4J,MAAM,gBAAgBrQ,EAAIqP,aAAa,kBAAkBrP,EAAIsQ,MAAMC,WAAW,eAAevQ,EAAIwQ,YAAY,uBAAuBxQ,EAAIqH,mBAAmB,4BAA4BrH,EAAIsH,uBAAuB,kBAAkBtH,EAAI2H,eAAe,eAAe3H,EAAI4H,YAAY,eAAe5H,EAAIyQ,YAAY,sBAAsBzQ,EAAIkG,kBAAkB,mBAAmBlG,EAAI0Q,eAAe,WAAW1Q,EAAIyG,KAAKkK,QAAQ,gBAAgB3Q,EAAI0F,aAAa,UAAU1F,EAAI0G,QAAQb,GAAG,CAAC,qBAAqB7F,EAAIuF,iBAAiB,gBAAgBvF,EAAI4Q,eAAe,yBAAyB5Q,EAAI6Q,qBAAqB,YAAY7Q,EAAIiG,SAAS,wBAAwBjG,EAAIkI,oBAAoB,eAAe,SAASI,GAAQtI,EAAIyQ,YAAcnI,CAAM,EAAE,mBAAmBtI,EAAIqI,gBAAgB,oBAAoBrI,EAAI8Q,iBAAiBrI,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASmI,EAAIrQ,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,MAAS,EAAE,IAAG,IAAI,OAAS1B,EAAI2P,gBAAoe3P,EAAIwK,KAAvdtK,EAAG,MAAM,CAACA,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAAO,eAAe,CAAE7D,EAAIgR,WAAY9Q,EAAG,MAAM,CAACM,YAAY,kBAAkBqF,GAAG,CAAC,MAAQ7F,EAAIiR,iBAAiB,CAAC/Q,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAAO,eAAe,CAAE7D,EAAIkR,oBAAqBhR,EAAG,MAAM,CAACM,YAAY,wCAAwC,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAIkR,qBAAqB,OAAOlR,EAAIwK,OAAOxK,EAAIgJ,GAAG,eAAc,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,WAAW,MAAQ,YAAY,KAAI,GAAG7D,EAAIwK,QAAQ,GAAYtK,EAAG,MAAM,CAACuP,IAAI,aAAajP,YAAY,kBAAkBI,MAAM,CAC5/I,qBAAsBZ,EAAImR,aAC1B,kBAAmBnR,EAAIiP,gBACtB,CAAC/O,EAAG,cAAc,CAAC2D,MAAM,CAAC,kBAAkB7D,EAAIoR,eAAe,cAAcpR,EAAIqR,gBAAgB,oBAAoBrR,EAAIsR,sBAAsBzL,GAAG,CAAC,eAAe,SAASyC,GAAQ,OAAOtI,EAAIuR,YAAYjJ,EAAO,EAAE,gBAAgB,SAASA,GAAQtI,EAAIsR,qBAAuB,CAAC,KAAKpR,EAAG,qBAAqB,CAAC2D,MAAM,CAAC,KAAO7D,EAAIyG,KAAK,gBAAgBzG,EAAIwR,aAAa,kBAAkBxR,EAAI2H,eAAe,eAAe3H,EAAI4H,aAAa/B,GAAG,CAAC,gBAAgB7F,EAAIyR,cAAchJ,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,KAAQxB,EAAG,MAAM,CAACM,YAAY,kBAAkB,CAAGR,EAAIiH,WAAcjH,EAAI0R,MAAM1E,OAAs2BhN,EAAIwK,KAAl2BtK,EAAG,MAAM,CAACM,YAAY,0BAA0B,CAAER,EAAI2R,YAAa,CAACzR,EAAG,MAAM,CAACM,YAAY,qCAAqCqF,GAAG,CAAC,MAAQ7F,EAAI4R,eAAe,CAAC5R,EAAIgJ,GAAG,mBAAkB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,mBAAmB,KAAI,GAAG3D,EAAG,MAAM,CAACM,YAAY,yBAAyBN,EAAG,MAAM,CAACM,YAAY,6BAA6B,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAI6R,cAAc,OAAO3R,EAAG,MAAM,CAACM,YAAY,wCAAwCqF,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAI8R,gBAAe,EAAM,IAAI,CAAC9R,EAAIgJ,GAAG,mBAAkB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,eAAe,KAAI,IAAI3D,EAAG,MAAM,CAACM,YAAY,iBAAiBqF,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAI8R,gBAAe,EAAK,IAAI,CAAC9R,EAAIgJ,GAAG,mBAAkB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAACM,YAAY,sBAAsBqD,MAAM,CAAC,KAAO,gBAAgB,KAAI,IAAI,GAAc7D,EAAI+R,gBAAwR/R,EAAIwK,KAA3QtK,EAAG,aAAa,CAAC2D,MAAM,CAAC,MAAQ7D,EAAI0R,OAAO7L,GAAG,CAAC,cAAc7F,EAAIgS,WAAW,gBAAgBhS,EAAIyR,cAAchJ,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,KAAiBxB,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAAER,EAAIoQ,cAAczG,IAAKzJ,EAAG,MAAM,CAACM,YAAY,iBAAiBqF,GAAG,CAAC,MAAQ7F,EAAIyR,eAAe,CAACzR,EAAIgJ,GAAG,mBAAkB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,mBAAmB,KAAI,GAAG7D,EAAIwK,KAAMxK,EAAIoH,WAAYlH,EAAG,yBAAyB,CAACwN,WAAW,CAAC,CAAChN,KAAK,gBAAgBiN,QAAQ,kBAAkBC,MAAOA,IAAO5N,EAAIiS,aAAc,EAAQpE,WAAW,gCAAgChK,MAAM,CAAC,eAAe7D,EAAIiS,YAAY,gBAAe,GAAMpM,GAAG,CAAC,YAAY7F,EAAIkS,SAAS,aAAa,SAAS5J,GAAQtI,EAAIiS,YAAc3J,CAAM,GAAGG,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,KAAQ1B,EAAIwK,KAAMxK,EAAIgH,UAAW9G,EAAG,MAAM,CAACM,YAAY,gCAAgCqF,GAAG,CAAC,MAAQ7F,EAAImS,mBAAmB,CAACnS,EAAIgJ,GAAG,kBAAiB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,YAAY,MAAQ,KAAK,OAAS,QAAQ,KAAI,GAAG7D,EAAIwK,KAAMxK,EAAI6H,sBAAuB3H,EAAG,MAAM,CAACM,YAAY,iBAAiBqF,GAAG,CAAC,MAAQ7F,EAAIoI,wBAAwB,CAACpI,EAAIgJ,GAAG,sBAAqB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,aAAa,KAAI,GAAG7D,EAAIwK,KAAMxK,EAAIgH,UAAW9G,EAAG,QAAQ,CAACuP,IAAI,OAAO2C,YAAY,CAAC,QAAU,QAAQvO,MAAM,CAAC,KAAO,OAAO,SAAW,GAAG,OAAS7D,EAAI8H,eAAejC,GAAG,CAAC,OAAS,SAASyC,GAAQ,OAAOtI,EAAIqS,aAAa/J,EAAOmG,OAAOiD,MAAM,KAAK1R,EAAIwK,MAAM,GAAGtK,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,WAAW,CAACuP,IAAI,eAAejP,YAAY,eAAeI,MAAM,CAC51G,uBAAwBZ,EAAIoQ,cAAczG,KAC1CrD,MAAO,CACPgM,SAAU,WACVC,UAAW,SACV1O,MAAM,CAAC,SAAW7D,EAAIiP,eAAiBjP,EAAI2F,iBAAiB,YAAc,qBAAqBE,GAAG,CAAC,MAAQ7F,EAAIwS,cAAc,QAAU,CAAC,SAASlK,GAAQ,OAAIA,EAAOlF,KAAKqP,QAAQ,QAAQzS,EAAI0S,GAAGpK,EAAOqK,QAAQ,MAAM,GAAGrK,EAAOQ,IAAI,CAAC,MAAM,WAAkB,KAAY9I,EAAI4S,eAAe5E,MAAM,KAAMC,UAAU,EAAE,SAAS3F,GAAQ,OAAIA,EAAOlF,KAAKqP,QAAQ,QAAQzS,EAAI0S,GAAGpK,EAAOqK,QAAQ,QAAQ,GAAGrK,EAAOQ,IAAI,UAAwBR,EAAOuK,SAASvK,EAAOwK,UAAUxK,EAAOyK,QAAQzK,EAAO0K,QAA/D,MAAmF1K,EAAOyF,iBAAwB/N,EAAIiT,WAAWjF,MAAM,KAAMC,WAAU,EAAE,SAAS3F,GAAQ,OAAIA,EAAOlF,KAAKqP,QAAQ,QAAQzS,EAAI0S,GAAGpK,EAAOqK,QAAQ,MAAM,EAAErK,EAAOQ,IAAI,QAAsBR,EAAOuK,SAASvK,EAAOwK,UAAUxK,EAAOyK,QAAQzK,EAAO0K,QAA/D,UAAmF1K,EAAOyF,gBAAiB,EAAE,SAASzF,GAAQ,OAAIA,EAAOlF,KAAKqP,QAAQ,QAAQzS,EAAI0S,GAAGpK,EAAOqK,QAAQ,MAAM,EAAErK,EAAOQ,IAAI,OAAc,KAAY9I,EAAIiT,WAAWjF,MAAM,KAAMC,UAAU,EAAE,SAAS3F,GAAQ,OAAIA,EAAOlF,KAAKqP,QAAQ,QAAQzS,EAAI0S,GAAGpK,EAAOqK,QAAQ,KAAK,GAAGrK,EAAOQ,IAAI,CAAC,KAAK,YAAmB,KAAY9I,EAAIkT,qBAAqB5K,GAAS,EAAE,EAAE,SAASA,GAAQ,OAAIA,EAAOlF,KAAKqP,QAAQ,QAAQzS,EAAI0S,GAAGpK,EAAOqK,QAAQ,OAAO,GAAGrK,EAAOQ,IAAI,CAAC,OAAO,cAAqB,KAAY9I,EAAIkT,qBAAqB5K,EAAQ,EAAE,GAAG,MAAQtI,EAAImT,gBAAiBnT,EAAI+G,aAAc7G,EAAG,MAAM,CAACM,YAAY,2BAA2BI,MAAM,CAAE,oBAAqBZ,EAAIoT,gBAAiBvN,GAAG,CAAC,MAAQ7F,EAAI+F,cAAc,CAAG/F,EAAI2F,iBAAoK3F,EAAIwK,KAAtJtK,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAIoT,eAAiBpT,EAAIqT,iBAAmBrT,EAAIsT,SAAS,IAAM,YAAY,MAAQtT,EAAIoT,eAAiB,WAAa,MAAgBpT,EAAI2F,iBAAkBzF,EAAG,MAAM,CAACM,YAAY,8BAA8BqD,MAAM,CAAC,KAAO,WAAW,CAAC3D,EAAG,OAAO,CAACM,YAAY,mBAAmB,CAACR,EAAIS,GAAG,kBAAkBT,EAAIwK,OAAOxK,EAAIwK,UAAU,IAAI,IAAI,GAAGtK,EAAG,eAAe,CAAC2D,MAAM,CAAC,KAAO7D,EAAI+R,gBAAgB,MAAQ/R,EAAI0R,MAAM,OAAS1R,EAAIuT,kBAAkB,gBAAgBvT,EAAIwT,iBAAiB,EACt8D,EACIlT,GAAkB,G,iHCVlBP,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,UACzF,EACIF,GAAkB,GCGtB,IACAI,KAAA,UCNuQ,MCOnQ,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,Q,iFClB5BX,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACN,EAAG,MAAM,CAACM,YAAY,4BAA4BI,MAAM,CAAE,qBAAsBZ,EAAIyT,eAAgB5N,GAAG,CAAC,MAAQ7F,EAAI0T,YAAY,CAAC1T,EAAIgJ,GAAG,qBAAoB,WAAW,MAAO,CAAC9I,EAAG,MAAM,CAAC2D,MAAM,CAAC,MAAQ,KAAK,OAAS,KAAK,QAAU,YAAY,KAAO,OAAO,MAAQ,6BAA6B,cAAc,+BAA+B,MAAQ7D,EAAIyT,cAAgB,WAAa,KAAK,CAACvT,EAAG,OAAO,CAAC2D,MAAM,CAAC,EAAI,WAAW,MAAQ,KAAK,OAAS,UAAU,KAAO,oBAAoB3D,EAAG,OAAO,CAACA,EAAG,UAAU,CAAC2D,MAAM,CAAC,GAAK,WAAW,oBAAsB,oBAAoB,MAAQ,IAAI,OAAS,MAAM,CAAC3D,EAAG,MAAM,CAAC2D,MAAM,CAAC,aAAa,UAAU,UAAY,iDAAiD3D,EAAG,QAAQ,CAAC2D,MAAM,CAAC,GAAK,SAAS,MAAQ,KAAK,OAAS,KAAK,aAAa,4nOAA4nO,KAAI,GAAI7D,EAAIiS,YAAa,CAAC/R,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAAO,eAAe,OAAS,KAAK,CAAC3D,EAAG,MAAM,CAACM,YAAY,mBAAmBI,MAAM,CAAE,sBAAuBZ,EAAIyT,eAAgBnN,MAAO,CAC3pQqN,OAAQ,GAAG3T,EAAI4T,sBACfC,IAAK7T,EAAI8T,YAAc9T,EAAI4T,kBAAoB,GAAG5T,EAAI+T,mBACtDC,MAAOhU,EAAIiU,iBACXC,QAASlU,EAAI+T,iBAAmB/T,EAAIyT,cAAgB,UAAY,SAC9D,CAAEzT,EAAIiS,YAAa/R,EAAG,eAAe,CAACuP,IAAI,gBAAgBzP,EAAIwK,MAAM,MAAMxK,EAAIwK,MAAM,EAChG,EACIlK,GAAkB,GC0CtB,IACAI,KAAA,uBAEAqK,MAAA,CACAkH,YAAA,CAAA7O,KAAAmJ,QAAAtB,SAAA,GACAwI,cAAA,CAAArQ,KAAAmJ,QAAAtB,SAAA,GACAkJ,cAAA,CAAA/Q,KAAAgR,eAAAnJ,QAAA,MACA6I,YAAA,CAAA1Q,KAAAmJ,QAAAtB,SAAA,GACAoJ,cAAA,CAAAjR,KAAAmJ,QAAAtB,SAAA,IAGAyB,MAAA,2BAEAhL,IAAAA,GACA,OACAkS,kBAAA,IACAG,eAAA,EACAE,iBAAA,GAEA,EAEAnS,MAAA,CACAmQ,WAAAA,CAAAqC,GACAA,GACAC,YAAA,KACA,KAAAC,mBAEA5I,SAAA6I,cAAA,gBAAAC,iBAAA,iBAAAC,aACA,KAAApM,MAAA,aACAqM,QAAAD,EAAAC,SACA,GACA,GACA,EAEA,GAGA5S,QAAA,CACAwS,gBAAAA,GACA,MAAAK,EAAA,4CAIAC,EAAA,6CAIAC,EAAA,sFAKA5J,EAAA,6RAWA7E,EAAAsF,SAAAyB,cAAA,SACA/G,EAAA0O,YAAAH,EAAAC,EAAAC,EAAA5J,EACA,KAAAmF,MAAA2E,YAAAC,WAAAnG,YAAAzI,EACA,EACAoN,SAAAA,CAAAyB,GACA,KAAA5M,MAAA,mBAAA0J,aACA,KAAAmD,uBAAAD,EAAAE,QAAAF,EAAAG,KAAAC,WAAAJ,EAAAG,KAAAE,YACA,EACAJ,sBAAAA,CAAAC,EAAAE,EAAAC,GACAjB,YAAA,KACA,MAAAkB,EAAAF,EAAA,KAAAC,EAAA,IAEA,QAAArB,cAKA,GAAAsB,EACA,KAAAxB,iBAAAsB,EAAA,WACA,KAAAxB,eAAA,IACA,KAAAH,kBAAA4B,EAAA,QACA,CACA,MAAAE,EAAA,KAAAvB,cAAAwB,wBAAA9B,IACA+B,EAAAF,EAAAL,EAAA,KAAAzB,kBAAA,GAEA,KAAAG,eAAA6B,EAAAP,EAAA,GACAA,EAAA,KAAAzB,kBAAA,GAEA,KAAAK,iBAAA,KAAAH,YAAA,aAAAO,cAAA,SACA,MAhBAoB,IAAA,KAAAxB,iBAAA,QAgBA,GAEA,IC/IqR,MCOjR,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QClB5BlU,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAQF,EAAIuJ,KAAMrJ,EAAG,MAAM,CAACA,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,SAAS,CAACM,YAAY,qBAAqBqF,GAAG,CAAC,MAAQ,SAASyC,GAAgC,OAAxBA,EAAOyF,iBAAwB/N,EAAIqL,OAAO2C,MAAM,KAAMC,UAAU,IAAI,CAACjO,EAAIS,GAAG,OAAOP,EAAG,MAAM,CAACM,YAAY,wBAAwB,CAACR,EAAIO,GAAG,GAAGL,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,MAAM,CAACM,YAAY,YAAYI,MAAM,CACzZ,eAAgBZ,EAAI6V,aACpBhQ,GAAG,CAAC,KAAO,SAASyC,GAAQA,EAAOyF,iBAAiBzF,EAAOwN,iBAAkB,EAAE,UAAY,SAASxN,GAAQA,EAAOyF,iBAAiBzF,EAAOwN,iBAAkB,EAAE,QAAU,SAASxN,GAAQA,EAAOyF,iBAAiBzF,EAAOwN,kBAAkB9V,EAAI6V,aAAc,CAAK,EAAE,SAAW,SAASvN,GAAQA,EAAOyF,iBAAiBzF,EAAOwN,kBAAkB9V,EAAI6V,aAAc,CAAI,EAAE,UAAY,SAASvN,GAAQA,EAAOyF,iBAAiBzF,EAAOwN,kBAAkB9V,EAAI6V,aAAc,CAAI,EAAE,UAAY,SAASvN,GAAQA,EAAOyF,iBAAiBzF,EAAOwN,kBAAkB9V,EAAI6V,aAAc,CAAK,EAAE,KAAO,SAASvN,GAAyD,OAAjDA,EAAOyF,iBAAiBzF,EAAOwN,kBAAyB9V,EAAI+V,eAAe/H,MAAM,KAAMC,UAAU,IAAI,CAAC/N,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAIgW,SAAS,IAAM,oBAAoB9V,EAAG,MAAM,CAACM,YAAY,aAAa,CAACR,EAAIS,GAAG,gCAAgCP,EAAG,MAAM,CAACM,YAAY,kBAAkB,CAACR,EAAIS,GAAG,QAAQP,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,SAAS,CAAC2F,GAAG,CAAC,MAAQ,SAASyC,GAAgC,OAAxBA,EAAOyF,iBAAwB/N,EAAIwT,aAAaxF,MAAM,KAAMC,UAAU,IAAI,CAACjO,EAAIS,GAAG,sBAAuBT,EAAI0R,MAAM1E,OAAQ9M,EAAG,MAAM,CAACM,YAAY,kBAAkB,CAACR,EAAIO,GAAG,GAAGL,EAAG,MAAM,CAACM,YAAY,uBAAuBR,EAAI2I,GAAI3I,EAAI0R,OAAO,SAASlG,GAAM,OAAOtL,EAAG,MAAM,CAAC4I,IAAI0C,EAAK9K,KAAKF,YAAY,oBAAoB,CAACN,EAAG,MAAM,CAACM,YAAY,iBAAiBqD,MAAM,CAAC,IAAyB,QAAnB2H,EAAKyK,UAAsBjW,EAAIkW,QAAUlW,EAAImW,WAAWjW,EAAG,MAAM,CAACM,YAAY,yBAAyB,CAACN,EAAG,MAAM,CAACM,YAAY,aAAa,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGgC,EAAK9K,MAAM,OAAOV,EAAIO,GAAG,GAAE,KAAQL,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAIoW,gBAAgB,IAAG,KAAKpW,EAAIwK,SAAStK,EAAG,MAAM,CAACM,YAAY,uBAAuBqF,GAAG,CAAC,MAAQ,SAASyC,GAAgC,OAAxBA,EAAOyF,iBAAwB/N,EAAIqL,OAAO2C,MAAM,KAAMC,UAAU,OAAOjO,EAAIwK,IAChwD,EACIlK,GAAkB,CAAC,WAAY,IAAIN,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,MAAM,CAACM,YAAY,wBAAwB,CAACR,EAAIS,GAAG,kBAAkBP,EAAG,MAAM,CAACM,YAAY,uBAAuB,CAACR,EAAIS,GAAG,yCAC9O,EAAE,WAAY,IAAIT,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,OAAO,CAACF,EAAIS,GAAG,qBAC3G,EAAE,WAAY,IAAIT,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,MAAM,CAACM,YAAY,YAAY,CAACN,EAAG,MAAM,CAACM,YAAY,2BAA2B4R,YAAY,CAAC,MAAQ,QAAQvO,MAAM,CAAC,KAAO,cAAc,gBAAgB,MAAM,gBAAgB,IAAI,gBAAgB,YACpS,G,okPCoEA,IACAnD,KAAA,cACAqK,MAAA,CACAxB,KAAA,CAAAnG,KAAAmJ,SAEAmF,MAAA,CAAAtO,KAAAqJ,MAAAxB,QAAA,IACAI,OAAA,CAAAjI,KAAAoJ,SAAAvB,QAAAA,KAAA,KACAuI,aAAA,CAAApQ,KAAAoJ,SAAAvB,QAAAA,KAAA,MAEAvJ,IAAAA,GACA,OACAsU,SAAAK,GACAH,QAAAI,GACAH,QAAAI,GACAH,UAAAI,GACAX,aAAA,EAEA,EAEA7T,QAAA,CACA+T,cAAAA,CAAAzN,GACA,KAAAuN,aAAA,EACA,KAAArC,aAAAlL,EACA,IClG4Q,MCOxQ,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QClB5BvI,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAAO,eAAe,CAAC3D,EAAG,MAAM,CAACA,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAIyW,SAAS,IAAM,MAAMvW,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAI0W,iBAAiB,EAC/P,EACIpW,GAAkB,G,oCCYtB,IACAoB,IAAAA,GACA,OAEA+U,SAAAA,GAEA,EACA1L,MAAA,CACA2L,QAAA,CACAtT,KAAA4H,OACAC,QAAA,+ECxBqQ,MCOjQ,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QClB5BlL,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACR,EAAIgJ,GAAG,eAAc,WAAW,MAAO,CAAC9I,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACR,EAAIgJ,GAAG,sBAAqB,WAAW,MAAO,CAAC9I,EAAG,MAAM,CAACM,YAAY,aAAa8F,MAAO,CACtT,mBAAoB,QAAQtG,EAAIyG,KAAKkQ,QAAU3W,EAAI4W,mBAChD,GAAE,KAAK,CAAEnQ,KAAMzG,EAAIyG,OAAQzG,EAAIgJ,GAAG,oBAAmB,WAAW,MAAO,CAAC9I,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACN,EAAG,MAAM,CAACM,YAAY,mCAAmC,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAI6W,UAAU,SAAS,GAAE,KAAK,CAAEpQ,KAAMzG,EAAIyG,KAAMqQ,YAAa9W,EAAI8W,eAAgB,GAAG5W,EAAG,MAAM,CAACM,YAAY,6BAA6B,CAACN,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,IAAI,CAAC2D,MAAM,CAAC,KAAO,GAAG7D,EAAI+W,OAAOC,6BAA6BhX,EAAIiX,sBAAsBjX,EAAI8C,yBAAyB9C,EAAIkX,yBAAyBlX,EAAI+C,eAAe,OAAS,WAAW,CAAC7C,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAImX,aAAa,IAAM,QAAQjX,EAAG,OAAO,CAACM,YAAY,gBAAgB,CAACR,EAAIS,GAAG,4BAA4BP,EAAG,MAAM,CAACM,YAAY,eAAeqF,GAAG,CAAC,MAAQ,SAASyC,GAAgC,OAAxBA,EAAOyF,iBAAwB/N,EAAIoX,gBAAgBpJ,MAAM,KAAMC,UAAU,IAAI,CAAC/N,EAAG,IAAI,CAACM,YAAY,qBAAqB,CAACR,EAAIS,GAAG,eAAeP,EAAG,MAAM,CAACU,MAAM,CAAEyW,UAAWrX,EAAIsX,gBAAiBzT,MAAM,CAAC,IAAM7D,EAAIuX,aAAa,IAAM,GAAG,OAAS,QAAQrX,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAAO,mBAAmB,CAAE7D,EAAIsX,eAAgBpX,EAAG,MAAM,CAACwN,WAAW,CAAC,CAAChN,KAAK,gBAAgBiN,QAAQ,kBAAkBC,MAAO5N,EAAIwX,eAAgB3J,WAAW,mBAAmBrN,YAAY,oBAAoB,CAAGR,EAAIyX,eAAezK,OAAiOhN,EAAIwK,KAA7NtK,EAAG,MAAM,CAACM,YAAY,oCAAoC,CAACN,EAAG,IAAI,CAACM,YAAY,WAAWqD,MAAM,CAAC,OAAS,SAAS,KAAO,8DAA8D,CAAC7D,EAAIS,GAAG,yBAAmCT,EAAIyX,eAAezK,OAAQ9M,EAAG,MAAM,CAACA,EAAG,MAAM,CAACM,YAAY,uBAAuB,CAACN,EAAG,QAAQ,CAACwN,WAAW,CAAC,CAAChN,KAAK,QAAQiN,QAAQ,UAAUC,MAAO5N,EAAI0X,eAAgB7J,WAAW,mBAAmBhK,MAAM,CAAC,KAAO,OAAO,YAAc,mBAAmB8T,SAAS,CAAC,MAAS3X,EAAI0X,gBAAiB7R,GAAG,CAAC,MAAQ,CAAC,SAASyC,GAAWA,EAAOmG,OAAOmJ,YAAiB5X,EAAI0X,eAAepP,EAAOmG,OAAOb,MAAK,EAAE,SAAStF,GAAQ,OAAOtI,EAAI6X,gCAAgC7X,EAAI0X,eAAe,MAAMxX,EAAG,MAAM,CAACM,YAAY,6BAA6BqD,MAAM,CAAC,IAAM7D,EAAI8X,WAAW,IAAM,mBAAmB5X,EAAG,KAAK,CAACM,YAAY,iBAAiB,CAAER,EAAI+X,QAAS7X,EAAG,MAAM,CAACM,YAAY,8BAA8B,CAACR,EAAIS,GAAG,4BAA4BT,EAAIwK,KAAKxK,EAAI2I,GAAI3I,EAAIgY,mBAAmB,SAASC,GAAU,OAAO/X,EAAG,KAAK,CAAC4I,IAAImP,EAAS9N,GAAG3J,YAAY,gBAAgBqD,MAAM,CAAC,MAAQoU,EAASvX,MAAMmF,GAAG,CAAC,MAAQ,SAASyC,GAAgC,OAAxBA,EAAOyF,iBAAwB/N,EAAIkY,UAAUD,EAAS,IAAI,CAACjY,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGyO,EAASvX,MAAM,MAAM,IAAIV,EAAImY,oBAAsBnY,EAAI+X,QAAS7X,EAAG,KAAK,CAACM,YAAY,iBAAiB,CAACR,EAAIS,GAAG,wBAAwBT,EAAIwK,MAAM,KAAKxK,EAAIwK,OAAOxK,EAAIwK,QAAQ,OAAO,GAAE,KAAK,CAAE/D,KAAMzG,EAAIyG,KAAMqQ,YAAa9W,EAAI8W,cAAgB9W,EAAIoY,YAAalY,EAAG,iBAAiB,CAAC2D,MAAM,CAAC,iBAAmB7D,EAAIqY,iBAAiB,UAAYrY,EAAIyG,KAAKC,OAAO,SAAW1G,EAAIyG,KAAK6R,UAAU,yBAA2BtY,EAAIuY,0BAA0B1S,GAAG,CAAC,cAAc7F,EAAIwY,WAAW,mBAAmBxY,EAAIwI,sBAAsBxI,EAAIwK,MAAM,EACx+F,EACIlK,GAAkB,G,s4MCJtB,IAAgBmG,EAAM3C,EAAeuL,KACnC,GAAI5I,EAAKqQ,aAAerQ,EAAKqQ,YAAY9J,OAAQ,CAC/C,MAAM8J,EAAcrQ,EAAK4J,MAAMoI,QAAOtW,IACpC,GAAIA,EAAKwH,MAAQ7F,IAC2B,IAAxC2C,EAAKqQ,YAAYrE,QAAQtQ,EAAKwH,QAC9BxH,EAAKuW,QAAgC,YAAtBvW,EAAKuW,OAAOtX,OAC/B,OAAO,CAAI,IAGb,IAAK0V,EAAY9J,OAAQ,OAEzB,OAA0B,IAAtBvG,EAAK4J,MAAMrD,OACNqC,EAAasJ,UAEb7B,EAAY8B,KAAIzW,GAAQA,EAAKyK,WAAUiM,KAAK,MAAQ,IAAMxJ,EAAasJ,SAElF,CACD,ECjBG5Y,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,kBAAkB,CAACN,EAAG,MAAM,CAACM,YAAY,gBAAgBqF,GAAG,CAAC,MAAQ7F,EAAIwY,cAActY,EAAG,MAAM,CAACM,YAAY,yBAAyB,CAACN,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,MAAM,CAACM,YAAY,wCAAwC,CAACN,EAAG,KAAK,CAACM,YAAY,eAAe,CAACR,EAAIS,GAAG,2BAA2BP,EAAG,SAAS,CAACM,YAAY,MAAMqD,MAAM,CAAC,KAAO,UAAUgC,GAAG,CAAC,MAAQ7F,EAAIwY,aAAa,CAACtY,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAI8Y,UAAU,IAAM,GAAG,OAAS,UAAU5Y,EAAG,MAAMA,EAAG,OAAO,CAAC2F,GAAG,CAAC,OAAS,SAASyC,GAAgC,OAAxBA,EAAOyF,iBAAwB/N,EAAI+Y,cAAc/K,MAAM,KAAMC,UAAU,IAAI,CAAC/N,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,IAAI,CAACA,EAAG,OAAO,CAACM,YAAY,aAAa,CAACR,EAAIS,GAAG,qBAAqBT,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAIqY,iBAAiB3X,SAASR,EAAG,IAAI,CAACA,EAAG,OAAO,CAACM,YAAY,aAAa,CAACR,EAAIS,GAAG,sBAAsBT,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAIqY,iBAAiBW,aAAa9Y,EAAG,IAAI,CAACkS,YAAY,CAAC,cAAc,aAAa,CAAClS,EAAG,OAAO,CAACM,YAAY,aAAa,CAACR,EAAIS,GAAG,wBAAwBT,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAIqY,iBAAiBvJ,MAAM,OAAQ9O,EAAIqY,iBAAkBnY,EAAG,MAAM,CAACM,YAAY,QAAQ,CAAER,EAAIqY,iBAAiBY,eAAgB,CAAC/Y,EAAG,MAAM,CAAC4I,IAAI9I,EAAIqY,iBAAiBlO,GAAG3J,YAAY,cAAc,CAACN,EAAG,MAAM,CAACM,YAAY,qDAAqD,CAACN,EAAG,QAAQ,CAACM,YAAY,cAAc,CAACR,EAAIS,GAAG,uBAAuBP,EAAG,MAAM,CAACM,YAAY,YAAY,CAACN,EAAG,SAAS,CAACM,YAAY,kCAAkCqD,MAAM,CAAC,KAAO,SAAS,iBAAiB,YAAYgC,GAAG,CAAC,MAAQ,SAASyC,GAAgC,OAAxBA,EAAOyF,iBAAwB/N,EAAIkZ,kBAAkB,gBAAgB,IAAI,CAAClZ,EAAIS,GAAG,sBAAgD,kBAAzBT,EAAImZ,iBAAsCjZ,EAAG,KAAK,CAACM,YAAY,kBAAkB,CAACN,EAAG,KAAK,CAACA,EAAG,QAAQ,CAACM,YAAY,4BAA4BqD,MAAM,CAAC,KAAO,OAAO,YAAc,wBAAwBgC,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAIoZ,iBAAiB9Q,EAAO,EAAE,QAAU,SAASA,GAAQ,IAAIA,EAAOlF,KAAKqP,QAAQ,QAAQzS,EAAI0S,GAAGpK,EAAOqK,QAAQ,QAAQ,GAAGrK,EAAOQ,IAAI,SAAS,OAAO,KAAKR,EAAOyF,gBAAiB,OAAO7N,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACR,EAAI2I,GAAI3I,EAAIqZ,YAAY,SAASC,GAAU,OAAOpZ,EAAG,KAAK,CAAC4I,IAAIwQ,EAAS5Y,KAAKF,YAAY,YAAYqD,MAAM,CAAC,YAAYyV,EAASC,QAAQ,CAACrZ,EAAG,SAAS,CAACM,YAAY,gBAAgBqD,MAAM,CAAC,MAAQyV,EAAS5Y,KAAK,KAAO,UAAUmF,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAIwZ,SAASF,EAAS5Y,KAAM,gBAAgB,IAAI,CAACV,EAAIS,GAAG,IAAIT,EAAIwJ,GAAG8P,EAASC,OAAO,QAAQ,IAAGrZ,EAAG,IAAI,CAACM,YAAY,iBAAiB,CAACR,EAAIS,GAAG,8BAA8B,KAAKT,EAAIwK,SAAStK,EAAG,QAAQ,CAACwN,WAAW,CAAC,CAAChN,KAAK,QAAQiN,QAAQ,UAAUC,MAAO5N,EAAIyZ,eAAe,iBAAkB5L,WAAW,oCAAoCrN,YAAY,eAAeqD,MAAM,CAAC,KAAO,gBAAgB,KAAO,OAAO,YAAc,cAAc,SAAW,IAAI8T,SAAS,CAAC,MAAS3X,EAAIyZ,eAAe,kBAAmB5T,GAAG,CAAC,QAAU,SAASyC,GAAQ,IAAIA,EAAOlF,KAAKqP,QAAQ,QAAQzS,EAAI0S,GAAGpK,EAAOqK,QAAQ,QAAQ,GAAGrK,EAAOQ,IAAI,SAAS,OAAO,KAAKR,EAAOyF,gBAAiB,EAAE,MAAQ,SAASzF,GAAWA,EAAOmG,OAAOmJ,WAAiB5X,EAAI0Z,KAAK1Z,EAAIyZ,eAAgB,gBAAiBnR,EAAOmG,OAAOb,MAAM,QAAQ5N,EAAIwK,KAAsC,IAAhCxK,EAAIqY,iBAAiBsB,OAAc3Z,EAAI2I,GAAI3I,EAAIqY,iBAAiBsB,QAAQ,SAASzP,GAAO,OAAOhK,EAAG,MAAM,CAAC4I,IAAI9I,EAAIqY,iBAAiBlO,GAAKD,EAAM1J,YAAY,cAAc,CAACN,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAACN,EAAG,MAAM,CAACM,YAAY,2CAA2C,CAACN,EAAG,QAAQ,CAAC2D,MAAM,CAAC,IAAM,kBAAoBqG,IAAQ,CAAClK,EAAIS,GAAG,eAAeT,EAAIwJ,GAAGU,MAAUhK,EAAG,SAAS,CAACM,YAAY,kCAAkCqD,MAAM,CAAC,KAAO,SAAS,gBAAgB,QAAQgC,GAAG,CAAC,MAAQ,SAASyC,GAAgC,OAAxBA,EAAOyF,iBAAwB/N,EAAIkZ,kBAAkB,cAAchP,IAAQ,IAAI,CAAClK,EAAIS,GAAG,sBAAuBT,EAAImZ,mBAAqB,cAAcjP,IAAShK,EAAG,KAAK,CAACM,YAAY,kBAAkB,CAACN,EAAG,KAAK,CAACA,EAAG,QAAQ,CAACM,YAAY,4BAA4BqD,MAAM,CAAC,KAAO,OAAO,YAAc,wBAAwBgC,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAIoZ,iBAAiB9Q,EAAO,EAAE,QAAU,SAASA,GAAQ,IAAIA,EAAOlF,KAAKqP,QAAQ,QAAQzS,EAAI0S,GAAGpK,EAAOqK,QAAQ,QAAQ,GAAGrK,EAAOQ,IAAI,SAAS,OAAO,KAAKR,EAAOyF,gBAAiB,OAAO7N,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACR,EAAI2I,GAAI3I,EAAIqZ,YAAY,SAASC,GAAU,OAAOpZ,EAAG,KAAK,CAAC4I,IAAIwQ,EAAS5Y,KAAKF,YAAY,YAAYqD,MAAM,CAAC,YAAYyV,EAASC,QAAQ,CAACrZ,EAAG,SAAS,CAACM,YAAY,gBAAgBqD,MAAM,CAAC,MAAQyV,EAAS5Y,KAAK,KAAO,UAAUmF,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAIwZ,SAASF,EAAS5Y,KAAM,eAAiBwJ,EAAM,IAAI,CAAClK,EAAIS,GAAG,IAAIT,EAAIwJ,GAAG8P,EAASC,OAAO,QAAQ,IAAGrZ,EAAG,IAAI,CAACM,YAAY,iBAAiB,CAACR,EAAIS,GAAG,8BAA8B,KAAKT,EAAIwK,OAAOtK,EAAG,QAAQ,CAACwN,WAAW,CAAC,CAAChN,KAAK,QAAQiN,QAAQ,UAAUC,MAAO5N,EAAIyZ,eAAe,eAAevP,KAAU2D,WAAW,2CAA2CrN,YAAY,eAAeqD,MAAM,CAAC,GAAK,kBAAoBqG,EAAM,KAAO,eAAiBA,EAAM,KAAO,OAAO,YAAc,2BAA2B,SAAW,IAAIyN,SAAS,CAAC,MAAS3X,EAAIyZ,eAAe,eAAevP,MAAWrE,GAAG,CAAC,QAAU,SAASyC,GAAQ,IAAIA,EAAOlF,KAAKqP,QAAQ,QAAQzS,EAAI0S,GAAGpK,EAAOqK,QAAQ,QAAQ,GAAGrK,EAAOQ,IAAI,SAAS,OAAO,KAAKR,EAAOyF,gBAAiB,EAAE,MAAQ,SAASzF,GAAWA,EAAOmG,OAAOmJ,WAAiB5X,EAAI0Z,KAAK1Z,EAAIyZ,eAAgB,eAAevP,IAAS5B,EAAOmG,OAAOb,MAAM,QAAQ,IAAG5N,EAAIwK,KAAMxK,EAAI4Z,kBAAmB,CAAC5Z,EAAI2I,GAAI3I,EAAI4Z,kBAAkBC,qBAAqB,SAASC,EAAM5P,GAAO,MAAO,CAAChK,EAAG,MAAM,CAAC4I,IAAIoB,EAAM1J,YAAY,cAAc,CAACN,EAAG,MAAM,CAACM,YAAY,qDAAqD,CAACN,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,QAAQ,CAACM,YAAY,cAAc,CAACR,EAAIS,GAAGT,EAAIwJ,GAAG6E,OAAO0L,OAAOD,GAAO,OAAQzL,OAAO2L,KAAKF,GAAO,GAAGG,SAAS,QAAS/Z,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAIyW,SAAS,IAAM,eAAezW,EAAIO,GAAG,GAAE,KAAQP,EAAIwK,OAAOtK,EAAG,MAAM,CAACM,YAAY,YAAY,CAACN,EAAG,SAAS,CAACM,YAAY,kCAAkCqD,MAAM,CAAC,KAAO,SAAS,iBAAiB,WAAW,gBAAgB,SAASgC,GAAG,CAAC,MAAQ,SAASyC,GAAQA,EAAOyF,iBAAiB/N,EAAIkZ,kBAAkB7K,OAAO0L,OAAOD,GAAO,GAAG,IAAI,CAAC9Z,EAAIS,GAAG,sBAAuBT,EAAImZ,mBAAqB9K,OAAO0L,OAAOD,GAAO,GAAI5Z,EAAG,KAAK,CAACM,YAAY,kBAAkB,CAACN,EAAG,KAAK,CAACA,EAAG,QAAQ,CAACM,YAAY,4BAA4BqD,MAAM,CAAC,KAAO,OAAO,YAAc,wBAAwBgC,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAIoZ,iBAAiB9Q,EAAO,EAAE,QAAU,SAASA,GAAQ,IAAIA,EAAOlF,KAAKqP,QAAQ,QAAQzS,EAAI0S,GAAGpK,EAAOqK,QAAQ,QAAQ,GAAGrK,EAAOQ,IAAI,SAAS,OAAO,KAAKR,EAAOyF,gBAAiB,OAAO7N,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACR,EAAI2I,GAAI3I,EAAIqZ,YAAY,SAASC,GAAU,OAAOpZ,EAAG,KAAK,CAAC4I,IAAIwQ,EAAS5Y,KAAKF,YAAY,YAAYqD,MAAM,CAAC,YAAYyV,EAASC,QAAQ,CAACrZ,EAAG,SAAS,CAACM,YAAY,gBAAgBqD,MAAM,CAAC,MAAQyV,EAASC,MAAM,KAAO,UAAU1T,GAAG,CAAC,MAAQ,SAASyC,GAAQtI,EAAIwZ,SAASF,EAAS5Y,KAAM2N,OAAO2L,KAAKF,GAAO,GAAG,IAAI,CAAC9Z,EAAIS,GAAG,IAAIT,EAAIwJ,GAAG8P,EAASC,OAAO,QAAQ,IAAGrZ,EAAG,IAAI,CAACM,YAAY,iBAAiB,CAACR,EAAIS,GAAG,8BAA8B,KAAKT,EAAIwK,SAAStK,EAAG,QAAQ,CAACM,YAAY,eAAeqD,MAAM,CAAC,KAAOwK,OAAO2L,KAAKF,GAAO,GAAG,KAAO,OAAO,YAAc,cAAc,SAAW,IAAInC,SAAS,CAAC,MAAQ3X,EAAIka,YAAcla,EAAIma,YAAc,IAAItU,GAAG,CAAC,MAAQ,SAASyC,GAAQtI,EAAIoa,YAAY9R,EAAQ+F,OAAO2L,KAAKF,GAAO,GAAG,EAAE,QAAU,SAASxR,GAAQ,IAAIA,EAAOlF,KAAKqP,QAAQ,QAAQzS,EAAI0S,GAAGpK,EAAOqK,QAAQ,QAAQ,GAAGrK,EAAOQ,IAAI,SAAS,OAAO,KAAKR,EAAOyF,gBAAiB,OAAO,KAAI/N,EAAIwK,MAAM,GAAGxK,EAAIwK,OAAOtK,EAAG,MAAM,CAACM,YAAY,uBAAuB,CAACN,EAAG,SAAS,CAACM,YAAY,kBAAkBqD,MAAM,CAAC,SAAW5D,KAAKoa,UAAU,KAAO,WAAW,CAACra,EAAIS,GAAG,UAAWR,KAAKoa,UAAWna,EAAG,UAAUF,EAAIwK,MAAM,SAAStK,EAAG,cAAc,CAAC2D,MAAM,CAAC,KAAO5D,KAAKqa,kBAAkB,OAASta,EAAIua,eAAe,gBAAgBta,KAAKgF,iBAAiB,EACnyP,EACI3E,GAAkB,CAAC,WAAY,IAAIN,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,IAAI,CAACF,EAAIS,GAAG,wCAAwCP,EAAG,IAAI,CAAC2D,MAAM,CAAC,KAAO,iGAAiG,OAAS,WAAW,CAAC7D,EAAIS,GAAG,UAAUT,EAAIS,GAAG,8DAClS,GCHA,MAAMsW,GAAS,CACbC,QAASwD,4BAIX,UCFA,GAAeC,KAAAA,OAAa,CAC1BzD,QAASD,GAAOC,U,8yRCqNlB,IACAtW,KAAA,gBACAgM,MAAA,mBACAzL,WAAA,CACAyZ,OAAA,GACAC,WAAAA,GAEA5P,MAAA,CACAsN,iBAAAhK,OACAuM,UAAA5P,OACA6P,OAAA7P,OACA8P,SAAAC,OACAxC,yBAAAvN,QAGAtJ,IAAAA,GACA,OACA2X,WAAA,GACAI,eAAA,GACAN,iBAAA,KACAkB,WAAA,EACAvB,UAAAA,GACArC,SAAAA,GACA6D,mBAAA,EACArV,aAAA,4CACAiV,WAAA,GACAC,WAAA,KAEA,EAEAjZ,SAAA,CACA0Y,iBAAAA,GACA,OAAAoB,GAAAC,MAAAC,GAAAA,EAAAC,wBAAA,KAAA9C,kBAAAjV,MACA,GAGAgY,OAAAA,GACA,MAAAhb,EAAA,KAAAe,OAAAC,MAAAhB,SACA,KAAAsC,QAAAtC,EAAAsC,QACA,KAAA2Y,sBACA,EAEAvZ,MAAA,CACAyW,yBAAA,CACA+C,OAAAA,CAAAC,GACA,GAAAA,EAAA,CACA,IAAAC,EAAAR,GAAAC,MAAAC,GAAAA,EAAAC,wBAAA,KAAA9C,kBAAAjV,OACA,IAAAoY,IAAAA,EAAA3B,qBAAA,IAAA2B,EAAA3B,oBAAA7M,OACA,OAEA,IAAAyO,EAAApN,OAAA2L,KAAAwB,GAAA3B,oBAAA,OACA,KAAAH,KAAA,KAAAD,eAAAgC,EAAAF,GACA,KAAApB,WAAAoB,CACA,CACA,EACAG,WAAA,EACAC,MAAA,IAIA3Z,QAAA,CACAuY,cAAAA,GACA,KAAAD,mBAAA,CACA,EAEAF,WAAAA,CAAAwB,EAAA9S,GACA,MAAA+S,EAAAC,mBAAAF,GAAAnN,QAAAb,OAAA,IACA,KAAA8L,KAAA,KAAAD,eAAA3Q,EAAA+S,GACA,KAAA3B,WAAA2B,EACA,KAAA1B,WAAA,IACA,EAGAjB,iBAAAA,CAAA6C,GACA,KAAA5C,mBAAA4C,EACA,KAAA5C,iBAAA,KAEA,KAAAA,iBAAA4C,CAEA,EAEAvD,UAAAA,GACA,KAAA0B,WAAA,KACA,KAAA3R,MAAA,eACA,KAAAkR,eAAA,GACA,KAAAP,kBAAA,KACA,EAEAE,gBAAAA,CAAAwC,GACA,IAAAI,EAAAJ,EAAAnN,OAAAb,MAAAqO,OAAAC,cACAC,EAAAP,EAAAnN,OAAA2N,cAAAA,cAAAA,cAAAC,iBAAA,cACAC,EAAA1Q,SAAA6I,cAAA,kBACA8H,GAAA,EAEA,GAAAJ,EACA,QAAAtT,EAAA,EAAAA,EAAAsT,EAAAnP,OAAAnE,IAAA,CACA,IAAA2T,EAAAL,EAAAtT,GACA4T,EAAAD,EAAAE,aAAA,aAAAT,OAAAC,cAEAO,EAAAE,SAAAX,IACAQ,EAAAlW,MAAA4N,QAAA,QACAqI,GAAA,GAEAC,EAAAlW,MAAA4N,QAAA,MAEA,CAOAoI,EAAAhW,MAAA4N,QAHAqI,EAGA,OAFA,MAIA,EAGA/C,QAAAA,CAAAgD,EAAA/N,GACA,KAAAgL,eAAAhL,IACA,KAAAiL,KAAA,KAAAD,eAAAhL,EAAA,IAEA,KAAAyL,WAAA,IAAAsC,KACA,KAAA/C,eAAAhL,GAAA,IAAA+N,KAEA,KAAArC,WAAA,KACA,KAAAhB,iBAAA,IACA,EAEAJ,aAAAA,CAAA6C,GACA,IAAAxZ,EAAAC,OAAAC,SAAAC,KACAC,EAAA,IAAAC,IAAAL,GACA,MAAA6U,EAAAzU,EAAAG,aAAAC,IAAA,WACAkY,EAAAtY,EAAAG,aAAAC,IAAA,YACAga,EAAApa,EAAAG,aAAAC,IAAA,SAEA,IAAAlB,EAAA,CACAkb,MAAAA,EACA9B,SAAAA,EACA+B,QAAA,KAAAxE,iBAAAvJ,KACAgO,WAAA,KAAAzE,iBAAAlO,MACA,KAAAsP,gBAAA,CAAAsD,OAAA,KAAAtD,iBAGA,MAAAuD,EAAA,CACAta,QAAAuU,KACAvV,GAEA,KAAA2Y,WAAA,EACA,IACAI,GACAwC,KAAA,uBAAAD,GACAE,MAAAC,IACA,KAAA9C,WAAA,EACA8C,EAAAzb,KAAA0b,IACA,KAAA5E,aACA,KAAAjQ,MAAA,mBAAA4U,EAAAzb,QAEA,KAAAuD,aAAAkY,EAAAzb,KAAAmb,QACA,KAAAvC,mBAAA,EACA,IAEA+C,OAAApR,IACA,KAAAoO,WAAA,EACA,KAAAC,mBAAA,EACA1L,QAAA3C,MAAAA,EAAA,GAEA,OAAAqR,GACA,KAAAjD,WAAA,EACAzL,QAAAC,IAAAyO,EACA,CACA,EAGA,0BAAAjC,GACA,IACA,MAAA8B,QAAA1C,GAAA7X,IAAA,uCAAAF,WACA,KAAA2W,WAAA8D,EAAAzb,KAAAA,KAAA6b,OACA,OAAAtR,GACA2C,QAAA3C,MAAAA,EACA,CACA,IC7Y8Q,MCO1Q,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QCkFhC,IACAvL,KAAA,aACAO,WAAA,CACAuc,cAAAA,IAGA9P,WAAA,CACA+P,aAAAC,KAAAA,WAGA3S,MAAA,CACAjH,cAAA,CAAAV,KAAA,CAAA4H,OAAA+P,QAAAzM,UAAA,GACAe,aAAA,CAAAjM,KAAAiL,OAAAC,UAAA,GACA7G,WAAA,CAAArE,KAAAmJ,QAAA+B,UAAA,GACA1I,aAAA,CAAAxC,KAAAmJ,QAAA+B,UAAA,GACAzH,YAAA,CAAAzD,KAAAqJ,MAAA6B,UAAA,GACA7H,KAAA,CAAArD,KAAAiL,OAAAC,UAAA,GACAtJ,UAAA,CAAA5B,KAAAqJ,MAAA6B,UAAA,IAGA5B,MAAA,iFAEAhL,IAAAA,GACA,OACAic,YAAAC,GACAhH,YAAAiH,GACAtG,aAAAA,GACAuG,YAAA,EACAhG,WAAAiG,GACAzG,gBAAA,EACA0G,YAAA,EACAtG,eAAA,GACAS,mBAAA,EACAC,aAAA,EACAC,iBAAA,KACAuC,UAAA5P,OACA6L,SAAA,GACAM,aAAAA,GACAM,eAAA,GACAM,SAAA,EACAd,OAAA,GACAF,OAAAA,GACAwB,yBAAA,KAEA,EAEArX,SAAA,CACA4V,WAAAA,GACA,OAAAmH,GAAA,KAAAxX,KAAA,KAAA3C,cAAA,KAAAuL,aACA,EAEA2I,iBAAAA,GACA,MAAAkG,EAAA,KAAAzG,eAAAgB,QAAAjR,GAAAA,EAAA9G,KAAAwb,cAAAS,SAAA,KAAAjF,eAAAwE,iBAEA,OAAAgC,CACA,GAGA9C,OAAAA,GACA,IAAAhZ,EAAAC,OAAAC,SAAAC,KACAC,EAAA,IAAAC,IAAAL,GAEA,IAAA+b,EAAA3b,EAAAG,aAAAC,IAAA,aACAwb,EAAA5b,EAAAG,aAAAC,IAAA,YACAga,EAAApa,EAAAG,aAAAC,IAAA,SACA,KAAAiU,SACAsH,GAAA,SAAAA,GAAA,cAAAA,EACAC,GAAA,SAAAA,GAAA,cAAAA,EACA,GAAAD,KAAAC,IACAD,EACAC,GAAA,SAAAA,GAAA,cAAAA,EACAA,EACAxB,EACA,KAAA3F,OAAAzU,EAAAG,aAAAC,IAAA,WACA,KAAAE,YAAAN,EAAAG,aAAAC,IAAA,eACA,KAAAsU,SAAA1U,EAAAG,aAAAC,IAAA,aACA,KAAAG,aAAAP,EAAAG,aAAAC,IAAA,gBACA,KAAA6U,eAAA,KAAAzS,UACA,KAAA6S,gCAAA,KAAAwG,SAAA,KAAAC,uBAAA,IACA,EAEAxc,MAAA,CACAkW,iBAAAA,CAAAuG,GACA,KAAApG,kBAAA,IAAAoG,EAAAvR,MACA,EACAhI,UAAA,CACA0W,WAAA,EACAJ,OAAAA,CAAAkD,GACAA,GAAAA,EAAAxR,SACA,KAAAyK,eAAA,IAAA+G,GAEA,IAIAxc,QAAA,CACAqc,QAAAA,CAAAI,EAAA1S,GACA,IAAA2S,EACA,mBAAAC,GACA,MAAAC,EAAA,KACAC,aAAAH,GACAA,EAAAnK,YAAA,IAAAkK,EAAAzQ,MAAA4Q,EAAAD,IAAA5S,EACA,CACA,EACA,4BAAAuS,CAAAQ,GACA,IAAA9C,EAAA8C,GAAA5C,cAMA,GAHA,KAAA6C,SAAA,EAGA,IAAA/C,EAAAhP,OACA,OAIA,MAAAgS,EAAA,KAAAvH,eAAAgB,QAAAR,GAAAA,EAAAvX,KAAAwb,cAAAS,SAAAX,KAQA,GALAgD,EAAAhS,OAAA,IACA,KAAA+R,SAAA,GAIA/C,EAAAhP,QAAA,OAAAgS,EAAAhS,OAAA,CACA,KAAA+K,SAAA,EACA,IACA,MAAAoF,QAAA1C,GAAAwC,KAAA,yBACAva,QAAA,KAAAuU,OACAgI,QAAA,CACAve,KAAAsb,EACAtD,OAAA,WACAwG,OAAA,KAGA,IAAAC,EAAAhC,GAAAzb,MAAAsD,WAAAtD,MAAA,GAEA,GAAAyd,EAAAnS,OAAA,GAEA,MAAAoS,EAAAD,EAAA1G,QACA4G,IAAA,KAAA5H,eAAA6H,MAAAC,GAAAA,EAAApV,KAAAkV,EAAAlV,OAGAiV,EAAApS,OAAA,IACA,KAAAyK,eAAA,KAAAA,eAAA+H,OAAAJ,GACA,KAAAL,SAAA,EAEA,CACA,OAAA9S,GACA2C,QAAA3C,MAAA,qCAAAA,EACA,SACA,KAAA8L,SAAA,CACA,CACA,CACA,EACAvP,kBAAAA,CAAAiX,GACA,KAAAlX,MAAA,mBAAAkX,EACA,EAEAC,SAAAA,GACA,KAAA5B,YAAA,CACA,EAEAtG,cAAAA,GACA,KAAAF,gBAAA,EACA,KAAAI,eAAA,EACA,EAEAN,eAAAA,GACA,KAAAE,gBAAA,KAAAA,cACA,EAEAY,SAAAA,CAAAD,GACA,KAAA0H,uBAAA1H,GAAA9N,IACA,KAAAiO,aAAA,EACA,KAAAC,iBAAAJ,EACA,KAAAX,gBAAA,KAAAA,eACA,KAAAI,eAAA,EACA,EAEAc,UAAAA,GACA,KAAAJ,aAAA,EACA,KAAAC,iBAAA,IACA,EAEA,4BAAAsH,CAAA7C,GACA,KAAAvE,yBAAA,KACA,IACA,WAAA7W,SAAA+Y,GAAA7X,IAAA,sBAAAka,aAAA,KAAA7F,UACA,KAAAsB,yBAAA7W,GAAAke,QACA,OAAAtC,GACA,KAAArY,aAAA,mBACAsP,YAAA,SAAAtP,aAAA,SACA2J,QAAAC,IAAAyO,EACA,CACA,ICvSoR,MCOhR,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QClB5Bvd,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAAO,iBAAiB,CAAE7D,EAAI0R,MAAM1E,OAAQ9M,EAAG,MAAM,CAACM,YAAY,2BAA2B8F,MAAO,CAAEuZ,OAAQ,GAAG7f,EAAI8f,QAAQxP,MAAMC,WAAWwP,mBAAqB,CAAC7f,EAAG,MAAM,CAACM,YAAY,iBAAiBR,EAAI2I,GAAI3I,EAAI0R,OAAO,SAASlG,EAAK3C,GAAG,OAAO3I,EAAG,MAAM,CAAC4I,IAAID,GAAG,CAAC3I,EAAG,YAAY,CAAC2D,MAAM,CAAC,KAAO2H,EAAK,MAAQ3C,GAAGhD,GAAG,CAAC,cAAc,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,cAAeD,EAAO,MAAM,EAAE,IAAG,GAAGpI,EAAG,MAAM,CAACM,YAAY,kBAAkB,CAACN,EAAG,MAAM,CAACM,YAAY,iBAAiBqF,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,gBAAgB,IAAI,CAACvI,EAAIgJ,GAAG,oBAAmB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,mBAAmB,KAAI,OAAO7D,EAAIwK,MACxuB,EACIlK,GAAkB,GCFlBP,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,2BAA2B,CAACN,EAAG,MAAM,CAACM,YAAY,iCAAiCqF,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,cAAevI,EAAIkK,MAAM,IAAI,CAAClK,EAAIgJ,GAAG,oBAAmB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,mBAAmB,KAAI,GAAI7D,EAAIggB,MAAO9f,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAACN,EAAG,MAAM,CAACM,YAAY,aAAa,CAACR,EAAIgJ,GAAG,aAAY,WAAW,MAAO,CAAC9I,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAIkW,QAAQ,IAAM,cAAc,KAAI,GAAGhW,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAIwL,KAAK9K,MAAM,SAASR,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAACN,EAAG,MAAM,CAACM,YAAY,aAAa,CAACR,EAAIgJ,GAAG,aAAY,WAAW,MAAO,CAAC9I,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAImW,QAAQ,IAAM,cAAc,KAAI,GAAGjW,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAIwL,KAAK9K,MAAM,UACr3B,EACIJ,GAAkB,GCmCtB,MAAM,UAAN2f,IAAAC,EAAA,MAEA,QACAxf,KAAA,YACAO,WAAA,CACAqL,QAAAA,GAGAvB,MAAA,CACAS,KAAA,CAAApI,KAAAiL,OAAAC,UAAA,GACApE,MAAA,CAAA9G,KAAA2X,OAAAzM,UAAA,IAGA5B,MAAA,gBAEAhL,IAAAA,GACA,OACAwU,QAAAI,GACAH,QAAAI,GAEA,EAEArV,SAAA,CACA8e,KAAAA,GACA,OAAAC,GAAA,KAAAzU,KACA,IC9DkR,MCO9Q,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QCWhC,IACA9K,KAAA,YACAO,WAAA,CACAqL,QAAA,EACA6T,SAAAA,IAGApV,MAAA,CACA2G,MAAA,CAAAtO,KAAAqJ,MAAA6B,UAAA,IAGA5B,MAAA,iCCxCmR,MCO/Q,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QClB5B3M,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAAO,iBAAiB,CAAE7D,EAAIwR,aAActR,EAAG,MAAM,CAACM,YAAY,sBAAsB8F,MAAO,CAAEuZ,OAAQ,GAAG7f,EAAI8f,QAAQxP,MAAMC,WAAWwP,mBAAqB,CAAC7f,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,OAAO,CAACM,YAAY,gBAAgBN,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACN,EAAG,MAAM,CAACM,YAAY,kBAAkB,CAACN,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAAER,EAAIwR,aAAa5E,SAAU1M,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAIwR,aAAa5E,UAAU,OAAO5M,EAAIwK,KAAKtK,EAAG,iBAAiB,CAAC2D,MAAM,CAAC,QAAU7D,EAAIwR,aAAapD,QAAQ,MAAQpO,EAAIyG,KAAK4J,MAAM,kBAAkBrQ,EAAI2H,eAAe,eAAe3H,EAAI4H,YAAY,OAAQ,GAAMa,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,MAAS,KAAM1B,EAAIogB,QAASlgB,EAAG,MAAM,CAACM,YAAY,kBAAkBqD,MAAM,CAAC,IAAM7D,EAAIqgB,UAAU7d,OAAQxC,EAAIsgB,QAASpgB,EAAG,QAAQ,CAACM,YAAY,kBAAkBqD,MAAM,CAAC,SAAW,KAAK,CAAC3D,EAAG,SAAS,CAAC2D,MAAM,CAAC,IAAM7D,EAAIqgB,UAAU7d,SAAUxC,EAAIugB,YAAargB,EAAG,MAAM,CAACM,YAAY,sCAAsC,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACR,EAAIgJ,GAAG,aAAY,WAAW,MAAO,CAAC9I,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAImW,WAAW,KAAI,GAAGjW,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAIqgB,UAAU3f,MAAM,OAAQV,EAAIqgB,UAAUpK,UAAW/V,EAAG,MAAM,CAACM,YAAY,wCAAwC,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAIqgB,UAAUpK,WAAW,OAAOjW,EAAIwK,OAAOxK,EAAIwK,SAAStK,EAAG,MAAM,CAACM,YAAY,kBAAkB,CAACN,EAAG,MAAM,CAACM,YAAY,iBAAiBqF,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,gBAAgB,IAAI,CAACvI,EAAIgJ,GAAG,oBAAmB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,mBAAmB,KAAI,OAAO7D,EAAIwK,MAClzD,EACIlK,GAAkB,GCFlBP,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,6BAA6BI,MAAM,CAAE,oBAAqBZ,EAAIwgB,aAAc,CAAExgB,EAAI2H,eAAgBzH,EAAG,MAAM,CAACU,MAAM,CAAE,oBAAqBZ,EAAIwgB,aAAc,CAAExgB,EAAI2H,eAAgB3H,EAAI2I,GAAI3I,EAAIygB,0BAA0B,SAAS5D,EAAQhU,GAAG,OAAO3I,EAAG,MAAM,CAAC4I,IAAID,EAAErI,YAAY,wBAAwB,CAAmB,UAAjBqc,EAAQzZ,KAAkBlD,EAAG,OAAO,CAACM,YAAY,eAAeR,EAAI2I,GAAIkU,EAAQ6D,OAAO,SAASC,EAAKzW,GAAO,OAAOhK,EAAGygB,EAAKne,IAAM,IAAM,OAAO,CAACsG,IAAIoB,EAAM0W,IAAI,YAAYhgB,MAAM,CACpiB,oBAAqBZ,EAAIwgB,WACzB,gBAAiBG,EAAKE,KACtB,kBAAmB7gB,EAAImM,SAAWwU,EAAKG,OACvC,kBAAmBH,EAAKI,OACxB,qBAAsBJ,EAAKK,UAC3B,wBAAyBhhB,EAAIwgB,YAAcG,EAAKM,OAChD,2BAA4BjhB,EAAIwgB,YAAcG,EAAKO,UACnD,gBAAiBlhB,EAAIwgB,aAAexgB,EAAImhB,OAASR,EAAKC,KACtD/c,MAAM,CAAC,KAAO8c,EAAKpe,KAAK,OAASoe,EAAKpe,KAAOvC,EAAI4H,YAAY6G,OAAS,KAAK,IAAMkS,EAAKpe,KAAOvC,EAAI4H,YAAYwZ,IAAM,OAAO,CAAClhB,EAAG,iBAAiB,CAAC2D,MAAM,CAAC,QAAU7D,EAAIqhB,UAAU,CAACrhB,EAAIS,GAAGT,EAAIwJ,GAAGmX,EAAK/S,WAAW,EAAE,IAAG,GAAG1N,EAAG2c,EAAQra,IAAM,IAAM,OAAO,CAACoe,IAAI,YAAYhgB,MAAM,CAC7Q,oBAAqBZ,EAAIwgB,WACzB,gBAAiB3D,EAAQgE,KACzB,kBAAmB7gB,EAAImM,SAAW0Q,EAAQiE,OAC1C,kBAAmBjE,EAAQkE,OAC3B,qBAAsBlE,EAAQmE,UAC9B,wBAAyBhhB,EAAIwgB,YAAc3D,EAAQoE,OACnD,2BAA4BjhB,EAAIwgB,YAAc3D,EAAQqE,UACtD,gBAAiBlhB,EAAIwgB,aAAexgB,EAAImhB,OAAStE,EAAQ+D,KACzD/c,MAAM,CAAC,KAAOgZ,EAAQta,KAAK,OAASsa,EAAQta,KAAOvC,EAAI4H,YAAY6G,OAAS,KAAK,IAAMoO,EAAQta,KAAOvC,EAAI4H,YAAYwZ,IAAM,OAAO,CAAClhB,EAAG,OAAO,CAACM,YAAY,eAAe,CAACN,EAAG,iBAAiB,CAAC2D,MAAM,CAAC,QAAU7D,EAAIqhB,UAAU,CAACrhB,EAAIS,GAAGT,EAAIwJ,GAAGqT,EAAQjP,WAAW,MAAM,EAAE,IAAG5N,EAAIwK,MAAM,GAAGtK,EAAG,MAAM,CAACF,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAIshB,kBAAkB,QACrV,EACIhhB,GAAkB,G,UCpBtB,MAAMihB,GAAUrB,EAAQ,MAGxB,QAAgBsB,EAAMC,KAEpBD,EAAOE,GAAeF,GAEtB,MAAMG,EAAOC,GAAcJ,GAErBK,EAAOC,GAAcH,GAErBI,EAAS,GAAGvC,OAAOxR,MAAM,GAAI6T,GAInC,OAFIJ,GAAWO,GAAcD,GAEtBA,CACR,EAED,MAAML,GAAiBF,IAErBA,EAAOA,EAAKS,QAAQ,QAAS,KAE7B,MAAMC,EAAmB,IAAIV,EAAKW,SAAS,iBAAiBvJ,KAAI1I,GAAKA,EAAEhG,QAEvE,IAAIkY,EAAU,GACVC,GAAW,EAEf,IAAK,IAAIxZ,EAAI,EAAGA,EAAI2Y,EAAKxU,OAAQnE,IAAK,CACpC,GAAgB,MAAZ2Y,EAAK3Y,KAAewZ,EAAU,CAEhC,MAAMC,EAAeJ,EAAiB5C,MAAKiD,GAAS1Z,IAAM0Z,GAAS1Z,IAAM0Z,EAAQf,EAAKgB,MAAMD,GAAO9P,QAAQ,IAAK,KAEhH,IAAK6P,EACH,QAEJ,CAEAF,GAAWZ,EAAK3Y,EAClB,CAEA,OAAOuZ,CAAO,EAGVK,GAAe,CACnB5B,KAAM,IACNC,OAAQ,IACRC,OAAQ,IACRC,UAAW,KAGP0B,GAAiB,CACrB,CAACD,GAAa5B,MAAO,CACnB8B,IAAK,KAAO,CAACF,GAAa5B,MAC1B+B,cAAe,IACfxf,KAAM,QAER,CAACqf,GAAa3B,QAAS,CACrB6B,IAAK,CAACF,GAAa3B,QACnB8B,cAAe,OACfxf,KAAM,UAER,CAACqf,GAAa1B,QAAS,CACrB4B,IAAK,CAACF,GAAa1B,QACnB6B,cAAe,IACfxf,KAAM,UAER,CAACqf,GAAazB,WAAY,CACxB2B,IAAK,CAACF,GAAazB,WACnB4B,cAAe,IACfxf,KAAM,aAER,MAAO,CACLuf,IAAK,MACLC,cAAe,SACfxf,KAAM,kBAER,IAAK,CACHuf,IAAK,IACLC,cAAe,IACfxf,KAAM,eAER,YAAa,CACXwf,cAAe,IACfD,IAAK,aACLvf,KAAM,QAIV,SAASwe,GAAciB,GACrB,IAAId,EAAS,GACTe,GAAc,EACdC,EAAgB,KAEpB,MAAMC,EAAQzB,GAAQtG,KAAK4H,GAC3B,IAAII,GAAmB,EAgBvB,GAdID,EAAMhW,OAAS,IACjB8V,EAAaD,EAAIpQ,QAAQuQ,EAAM,GAAGpV,OAClCqV,GAAmB,GAGrB5U,OAAO2L,KAAK0I,IAAgBQ,SAAQC,IAClC,MAAMC,EAAKP,EAAIpQ,QAAQ0Q,GACnBC,GAAM,IAAMN,EAAa,GAAKM,EAAKN,KACrCA,EAAaM,EACbL,EAAgBI,EAChBF,GAAmB,EACrB,IAGEA,IAAuC,IAAnBF,EAAsB,CAC5C,MAAMM,EAAUR,EAAIS,OAAO,EAAGR,GACxBS,EAAUV,EAAIS,OAAOR,EAAYE,EAAM,GAAGpV,MAAMZ,QAChDwW,EAAWX,EAAIS,OAAOR,EAAaE,EAAM,GAAGpV,MAAMZ,QAIxD,OAHA+U,EAAO0B,KAAKJ,GACZtB,EAAO0B,KAAKF,GACZxB,EAASA,EAAOvC,OAAOoC,GAAc4B,IAC9BzB,CACT,CAEA,GAAIgB,EAAe,CACjB,IAAIM,EAAUR,EAAIS,OAAO,EAAGR,GAC5B,MAAMY,EAAOX,EACb,IAAIS,EAAWX,EAAIS,OAAOR,EAAaY,EAAK1W,QAE5C,GAAI6V,EAAIZ,QAAQ,MAAO,IAAIjV,SAAyB,EAAd0W,EAAK1W,OACzC,MAAO,CAAC6V,GAGV,MAAMc,EAAQH,EAASG,MACrB,IAAIC,OACF,MACGlB,GAAegB,GAAMd,eAAiB,KACvC,KACCF,GAAegB,GAAMf,IAAM,IAAM,IAClC,KACCD,GAAegB,GAAMf,IAAM,IAAMD,GAAegB,GAAMf,IAAM,IAAM,IACrE,MAGJ,GAAKgB,GAAUA,EAAM,GAGd,CACDN,GACFtB,EAAO0B,KAAKJ,GAEd,MAAMQ,EAAS,CACbtB,MAAOmB,EACPtV,QAASwT,GAAc+B,EAAM,IAC7BhB,IAAKgB,EAAM,GACXvgB,KAAMsf,GAAegB,GAAMtgB,MAE7B2e,EAAO0B,KAAKI,GACZL,EAAWA,EAASF,OAAOK,EAAM,GAAG3W,OACtC,MAdEqW,GAAoBK,EACpB3B,EAAO0B,KAAKJ,GAed,OADAtB,EAASA,EAAOvC,OAAOoC,GAAc4B,IAC9BzB,CACT,CACE,OAAIc,EACK,CAACA,GAED,EAGb,CAEA,SAASf,GAAcH,GACrB,MAAMI,EAAS,GAYf,OAVAJ,EAAKuB,SAAQ7W,IACS,kBAATA,EACT0V,EAAO0B,KAAK,CAAEK,MAAO,GAAIlW,MAAOvB,IAE5BqW,GAAerW,EAAKkW,QACtBR,EAAO0B,KAAKM,GAAa1X,GAE7B,IAGK0V,CACT,CAEA,SAASgC,GAAa1X,GACpB,MAAM0V,EAAS,GAyBf,OAvBA1V,EAAK+B,QAAQ8U,SAAQc,IACD,kBAAPA,EACTjC,EAAO0B,KAAK,CACVK,MAAO,CAACzX,EAAKjJ,MACbwK,MAAOoW,IAGTA,EAAG5V,QAAQ8U,SAAQra,IACA,kBAANA,EACTkZ,EAAO0B,KAAK,CACVK,MAAO,CAACE,EAAG5gB,MAAMoc,OAAO,CAACnT,EAAKjJ,OAC9BwK,MAAO/E,IAGTkZ,EAAO0B,KAAK,CACVK,MAAO,CAACjb,EAAEzF,MAAMoc,OAAO,CAACwE,EAAG5gB,OAAOoc,OAAO,CAACnT,EAAKjJ,OAC/CwK,MAAOmW,GAAalb,IAExB,GAEJ,IAGKkZ,CACT,CAEA,SAASC,GAAciC,GACrB,MAAMlC,EAAS,GAiBf,OAfAkC,EAAMf,SAAQgB,IACZ,MAAMlB,EAAQzB,GAAQtG,KAAKiJ,EAAItW,OAE/B,GAAIoV,EAAMhW,OAAQ,CAChB,MAAMmX,EAASD,EAAItW,MAAMqU,QAAQe,EAAM,GAAGpV,MAAO,IACjDmU,EAAO0B,KAAK,CAAEK,MAAOI,EAAIJ,MAAOlW,MAAOuW,IAEvCD,EAAIJ,MAAQ,CAAC,OAAOtE,OAAO0E,EAAIJ,OAC/BI,EAAI3hB,KAAOygB,EAAM,GAAGzgB,KACpB2hB,EAAItW,MAAQoV,EAAM,GAAGpV,KACvB,CAEAmU,EAAO0B,KAAKS,EAAI,IAGXnC,CACT,C,cCnLA,IACArhB,KAAA,gBACAO,WAAA,CAAAqL,QAAA,EAAA8X,cAAAA,GAAAA,GACArZ,MAAA,CACAqD,QAAA,CAAAhL,KAAA,CAAA4H,OAAA+P,QAAAzM,UAAA,GACAnC,QAAA,CAAA/I,KAAAmJ,QAAAtB,SAAA,GACAoF,MAAA,CAAAjN,KAAAqJ,MAAAxB,QAAAA,IAAA,IACAsW,QAAA,CAAAne,KAAAmJ,QAAAtB,SAAA,GACAuV,WAAA,CAAApd,KAAAmJ,QAAAtB,SAAA,GACAkW,MAAA,CAAA/d,KAAAmJ,QAAAtB,SAAA,GACAtD,eAAA,CAAAvE,KAAAmJ,QAAA+B,UAAA,GACA1G,YAAA,CAAAxE,KAAAiL,OAAAC,UAAA,GACAoC,eAAA,CAAAtN,KAAA4H,OAAAC,QAAAA,IAAA,IACA0F,QAAA,CAAAvN,KAAAmJ,QAAAtB,SAAA,IAEAyB,MAAA,kBACAxL,SAAA,CACAmgB,OAAAA,GACA,YAAA3Q,eACA,EACA2T,gBAAAA,GACA,SAAAjW,QACA,YAEA,IAAAyO,EAAAyH,GACA,KAAAC,WAAA,KAAAnW,SACA,KAAAmT,UAAA,KAAA3Z,YAAA4c,SACA,KAAA5c,aAgBA,OAbAiV,EAAAqG,SAAAhT,IACAA,EAAAtC,MAAA,KAAA6W,UAAAvU,EAAAtC,OACAsC,EAAA1N,IAAA,KAAAkiB,UAAAxU,EAAA,OACAA,EAAA2Q,KAAA,KAAA6D,UAAAxU,EAAA,QACAA,EAAA4Q,OAAA,KAAA4D,UAAAxU,EAAA,UACAA,EAAA6Q,OAAA,KAAA2D,UAAAxU,EAAA,UACAA,EAAA8Q,UAAA,KAAA0D,UAAAxU,EAAA,aACAA,EAAA+Q,OAAA,KAAAyD,UAAAxU,EAAA,eACAA,EAAAgR,UAAA,KAAAwD,UAAAxU,EAAA,kBACAA,EAAA0Q,IAAA,KAAA8D,UAAAxU,EAAA,OACAA,EAAAjG,MAAA,KAAA0a,eAAAzU,EAAA,IAGA,KAAA0U,oBAAA/H,EACA,EACA4D,wBAAAA,GACA,OAAAhU,MAAAoY,QAAA,KAAAR,kBAEA,KAAAA,iBAAA5L,QAAAvI,GACA,UAAAA,EAAA9M,MAAA8M,EAAAwQ,OAAA1T,QACAkD,EAAAtC,OAAAsC,EAAAtC,MAAAqO,SAJA,EAMA,GAEAja,QAAA,CACA4iB,mBAAAA,CAAArgB,GACA,MAAAugB,EAAA,GACA,IAAAC,EAAA,GACA,MAAAC,EAAAA,KACAD,EAAA/X,OAAA,IACA8X,EAAArB,KAAA,CACArgB,KAAA,QACAsd,MAAAqE,EAAAnM,KAAA1I,IAAA,IAAAA,QAEA6U,EAAA,GACA,EAEA,UAAAxW,KAAAhK,EACA,OAAAgK,EAAAX,OAAA,SAAAW,EAAAX,OAAA,QAAAqX,KAAA1W,EAAAX,QACAoX,IACAF,EAAArB,KAAAlV,IAEAwW,EAAAtB,KAAAlV,GAIA,OADAyW,IACAF,CACA,EACAL,SAAAA,CAAAjD,GAEA,OAAAA,EAAAS,QAAA,kCACA,EACAyC,SAAAA,CAAA7H,EAAAzZ,GACA,WAAAyZ,EAAAiH,MAAArR,QAAArP,EACA,EACAuhB,cAAAA,CAAA9H,GACA,IAAA3S,EAAA2S,EAAAjP,MAAAsX,YAAA,KACA,MAAAC,EAAAtI,EAAAjP,MAAAsX,YAAA,KACAC,EAAAjb,IAAAA,GAAA,GACA,MAAA9G,EAAAyZ,EAAAjP,MAAAwX,UAAAlb,EAAA,EAAA2S,EAAAjP,MAAAZ,QACAqY,EAAAnb,EAAA,GAAAob,GAAAA,GAAAhG,MAAA9X,GAAApE,EAAA8Y,cAAAS,SAAAnV,KAEA,OADA6d,GAAA,KAAAE,aAAA1I,GACAwI,CACA,EACAE,YAAAA,CAAA1I,GACA,MAAA5S,EAAA,IAAAub,MAGA,SAAAC,EAAAC,GACA,IAAAA,GAAA7a,KAEA,YADAZ,EAAA0b,oBAAA,OAAAF,GAGA,MAAAG,EAAAF,GAAA7a,KAAA,GAAAgb,MAAA,IACAhJ,EAAAlJ,OAAAmS,KAAAC,MAAAL,GAAA7a,KAAA,IAAA8I,OAAAiS,GAAA,KACA3b,EAAA0b,oBAAA,OAAAF,EACA,CAVAxb,EAAA+b,IAAAnJ,EAAAjP,MACA3D,EAAAyK,iBAAA,OAAA+Q,EAUA,EACAlB,UAAAA,CAAAnW,GACA,IAAAA,EACA,OAAAA,EAEA,MAAA6X,EAAA,YACAC,EAAA,aACAC,EAAA,IAAA/X,EAAA+T,SAAA,IAAAyB,OAAAqC,EAAA,QAAArN,KAAApK,GAAAA,EAAAtE,QACAkc,EAAAhY,EAMA,OALA+X,EAAAjD,SAAAhZ,IACA,MAAA+M,EAAAmP,EAAAhB,UAAAlb,EAAA+b,EAAAjZ,OAAAoZ,EAAA3T,QAAAyT,EAAAhc,IACA/H,EAAA,KAAAkO,MAAA4K,MAAA9Y,GAAAA,EAAAwH,MAAAsN,IACA7I,EAAAA,EAAAiY,WAAApP,EAAA,IAAA9U,GAAAyK,UAAA,gBAEAwB,CACA,EACAkY,OAAAA,CAAAzJ,GACA,SAAA2D,YAAA,KAAAkE,UAAA7H,EAAA,QACA,MAAA1a,EAAA,KAAAkO,MAAA4K,MAAAsL,IAAA,IAAA1J,EAAAjP,MAAA6E,QAAA8T,EAAA3Z,YACA,KAAArE,MAAA,gBAAApG,EACA,CACA,ICpL8Q,MCO1Q,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QCiDhC,MAAM,YAANqkB,GAAA,YAAAC,IAAAvG,EAAA,MAEA,QACAxf,KAAA,mBACAO,WAAA,CACAqL,QAAA,EACAoa,cAAAA,IAGA3b,MAAA,CACAtE,KAAA,CAAArD,KAAAiL,OAAAC,UAAA,GACAkD,aAAA,CAAApO,KAAAiL,OAAApD,QAAA,MACAtD,eAAA,CAAAvE,KAAAmJ,QAAA+B,UAAA,GACA1G,YAAA,CAAAxE,KAAAiL,OAAAC,UAAA,IAGA5B,MAAA,kBAEAhL,IAAAA,GACA,OACAyU,QAAAI,GAEA,EAEArV,SAAA,CACAmf,SAAAA,GACA,YAAA7O,aAAAE,MAAA,KAAAF,aAAAE,MAAA,KACA,EACA0O,OAAAA,GACA,OAAAoG,GAAA,KAAAnG,UACA,EACAC,OAAAA,GACA,OAAAmG,GAAA,KAAApG,UACA,EAEAE,WAAAA,GACA,YAAA/O,aAAAE,QAAA,KAAA4O,UAAA,KAAAF,OACA,ICxG0R,MCOtR,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QClB5BrgB,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAAO,iBAAiB,CAAE7D,EAAIoR,eAAepE,OAAQ9M,EAAG,MAAM,CAACM,YAAY,uBAAuB8F,MAAO,CAAEuZ,OAAQ,GAAG7f,EAAI8f,QAAQxP,MAAMC,WAAWwP,mBAAqB/f,EAAI2I,GAAI3I,EAAIoR,gBAAgB,SAASzF,EAAMzB,GAAO,OAAOhK,EAAG,MAAM,CAAC4I,IAAI6C,EAAMnL,YAAY,oBAAoBI,MAAM,CAAE,2BAA4BsJ,IAAUlK,EAAI2mB,YAAa9gB,GAAG,CAAC,UAAY,SAASyC,GAAQtI,EAAI2mB,WAAazc,CAAK,EAAE,MAAQ,SAAS5B,GAAQ,OAAOtI,EAAIuI,MAAM,eAAgBoD,EAAM,IAAI,CAAC3L,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGmC,GAAO,MAAM,IAAG,GAAG3L,EAAIwK,MACtlB,EACIlK,GAAkB,GCoBtB,IACAI,KAAA,aAEAqK,MAAA,CACAqG,eAAA,CAAAhO,KAAAqJ,MAAA6B,UAAA,GACA2E,WAAA,CAAA7P,KAAAmJ,QAAAtB,QAAA,MACA2b,eAAA,CAAAxjB,KAAA2X,OAAA9P,QAAA,OAGAyB,MAAA,+BAEAhL,IAAAA,GACA,OACAilB,WAAA,KAEA,EAEA7kB,MAAA,CACAsP,cAAAA,GACA,KAAAuV,WAAA,CACA,EACA1T,UAAAA,CAAAqB,GACAA,GACA,KAAA/L,MAAA,oBAAA6I,eAAA,KAAAuV,YAEA,EACAC,cAAAA,GACA,KAAAA,eAAA,QAAAD,WAAA,KAAAvV,eAAApE,OAAA,EACA,KAAA2Z,aACA,KAAAC,eAAA,QAAAD,WAAA,GACA,KAAAA,aAEA,KAAApe,MAAA,gBACA,ICvDoR,MCOhR,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QClB5BxI,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACuP,IAAIzP,EAAI6c,QAAQlT,IAAInJ,YAAY,sBAAsBqD,MAAM,CAAC,GAAK7D,EAAI6c,QAAQlT,MAAM,CAAE3J,EAAI6mB,SAAU3mB,EAAG,MAAM,CAACM,YAAY,+BAA+B,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAI6c,QAAQ/P,MAAM,OAAO9M,EAAIwK,KAAMxK,EAAI0F,aAAa1F,EAAI0G,SAASoD,SAAW9J,EAAI6c,QAAQlT,IAAKzJ,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAIqP,aAAayX,cAAc,OAAO9mB,EAAIwK,KAAMxK,EAAI6c,QAAQkK,OAAQ7mB,EAAG,MAAM,CAACM,YAAY,iCAAiC,CAACN,EAAG,iBAAiB,CAAC2D,MAAM,CAAC,QAAU7D,EAAI6c,QAAQzO,QAAQ,MAAQpO,EAAIgnB,UAAU,kBAAkBhnB,EAAI2H,eAAe,eAAe3H,EAAI4H,YAAY,mBAAmB5H,EAAI0Q,iBAAiB,CAAC1Q,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACV,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKV,EAAI0B,MAAM,KAAI,IAAI,GAAGxB,EAAG,MAAM,CAACM,YAAY,kBAAkBI,MAAM,CAAE,qBAA6C,IAAvBZ,EAAI6c,QAAQoK,SAAgB,CAACjnB,EAAIgJ,GAAG,WAAU,WAAW,MAAO,CAAC9I,EAAG,MAAM,CAACM,YAAY,wBAAwBI,MAAM,CACv9B,+BAAgCZ,EAAIknB,gBACnC,CAAChnB,EAAG,MAAM,CAACM,YAAY,mBAAmBI,MAAM,CAC/C,wBAAyBZ,EAAImnB,eAC7B,oBAAqBnnB,EAAI6c,QAAQuK,OAAgC,IAAvBpnB,EAAI6c,QAAQoK,OACtD,sBAA8C,IAAvBjnB,EAAI6c,QAAQoK,OACnC,sBAAuBjnB,EAAI6c,QAAQ1Q,QACnC,WAAYnM,EAAI6c,QAAQnL,MAAQ1R,EAAI6c,QAAQnL,MAAM,GAAGqG,QAAU,MAC/DlU,MAAM,CAAC,GAAK,GAAG7D,EAAI6c,QAAQlT,aAAa9D,GAAG,CAAC,UAAY7F,EAAIqnB,eAAe,WAAarnB,EAAIsnB,iBAAiB,CAAEtnB,EAAI2Q,SAAW3Q,EAAIknB,cAAehnB,EAAG,MAAM,CAACM,YAAY,oBAAoBI,MAAM,CAC/L,sBAAuBZ,EAAI6c,QAAQ1Q,SAAWnM,EAAI6c,QAAQ0K,eACzD,CAACrnB,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAI6c,QAAQjQ,eAAe5M,EAAIwK,MAAOxK,EAAI6c,QAAQ1Q,SAAWnM,EAAI6c,QAAQ0K,aAAcrnB,EAAG,gBAAgB,CAAC2D,MAAM,CAAC,QAAU7D,EAAI6c,QAAQ,aAAa7c,EAAIgnB,UAAU,kBAAkBhnB,EAAI2H,eAAe,eAAe3H,EAAI4H,YAAY,WAAW5H,EAAI2Q,SAAS9K,GAAG,CAAC,oBAAoB,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,oBAAqBvI,EAAI6c,QAAQ,IAAI,CAAC7c,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACV,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKV,EAAI0B,MAAM,KAAI,GAAG1B,EAAIwK,KAAMxK,EAAI6c,QAAQ1Q,QAASjM,EAAG,MAAM,CAACF,EAAIgJ,GAAG,gBAAe,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAACM,YAAY,mBAAmBqD,MAAM,CAAC,KAAO,aAAa,IAAG3D,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAIqP,aAAamY,qBAAqB,GAAKxnB,EAAI6c,QAAQnL,OAAU1R,EAAI6c,QAAQnL,MAAM1E,QAAiShN,EAAIynB,SAAWznB,EAAI6c,QAAQnL,MAAM1E,OAAS,EAAG9M,EAAG,gBAAgB,CAAC2D,MAAM,CAAC,kBAAkB7D,EAAI8D,cAAc,QAAU9D,EAAI6c,QAAQ,aAAa7c,EAAIgnB,UAAU,kBAAkBhnB,EAAI2H,eAAe,eAAe3H,EAAI4H,YAAY,mBAAmB5H,EAAI0Q,gBAAgB7K,GAAG,CAAC,YAAY7F,EAAIiG,SAAS,mBAAmBjG,EAAIqI,kBAAkB,CAACrI,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACV,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKV,EAAI0B,MAAM,KAAI,GAAG,CAACxB,EAAG,MAAM,CAACU,MAAM,CAAE,cAAeZ,EAAI6c,QAAQnL,MAAM,GAAGqG,UAAW,CAAC7X,EAAG,eAAe,CAAC2D,MAAM,CAAC,IAAM7D,EAAI6c,QAAQnL,MAAM,GAAGlP,KAAKqD,GAAG,CAAC,uBAAuB,SAASyC,GAAQtI,EAAI0nB,aAAepf,CAAM,EAAE,uBAAuB,SAASA,GAAQtI,EAAI2nB,mBAAqBrf,CAAM,IAAI,CAACtI,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACV,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKV,EAAI0B,MAAM,KAAI,GAAK1B,EAAI6c,QAAQ1Q,QAAgGnM,EAAIwK,KAA3FtK,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAI0nB,cAAc,OAAgBxnB,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAAER,EAAI6c,QAAQ+K,SAAW5nB,EAAI6c,QAAQ1Q,QAASjM,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACR,EAAIgJ,GAAG,eAAc,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,YAAY,KAAI,GAAG7D,EAAIwK,KAAKtK,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAI6c,QAAQ9P,cAAe/M,EAAI6nB,mBAAoB3nB,EAAG,OAAO,CAACF,EAAIgJ,GAAG,kBAAiB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAACM,YAAY,iBAAiBqD,MAAM,CAAC,KAAmC,SAA5B7D,EAAI6c,QAAQiL,aAA2B9nB,EAAI6c,QAAQuK,MAE/0EpnB,EAAI6c,QAAQiL,YACZ,mBACA,YAHA,OAGY,MAAQ9nB,EAAI6c,QAAQkL,KAAO,OAAS,MAAM,GAAE,KAAK,CAAElL,QAAS7c,EAAI6c,WAAY,GAAG7c,EAAIwK,QAAQ,IAJinBtK,EAAG,iBAAiB,CAAC2D,MAAM,CAAC,QAAU7D,EAAI6c,QAAQzO,QAAQ,MAAQpO,EAAIgnB,UAAU,kBAAkBhnB,EAAI2H,eAAe,eAAe3H,EAAI4H,YAAY,mBAAmB5H,EAAI0Q,iBAAiB,CAAC1Q,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACV,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKV,EAAI0B,MAAM,KAAI,GAIh4B1B,EAAIgoB,cAAe9nB,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAAER,EAAI6c,QAAQ+K,SAAW5nB,EAAI6c,QAAQ1Q,QAASjM,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACR,EAAIgJ,GAAG,eAAc,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,YAAY,KAAI,GAAG7D,EAAIwK,KAAKtK,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAI6c,QAAQ9P,cAAe/M,EAAI6nB,mBAAoB3nB,EAAG,OAAO,CAACwN,WAAW,CAAC,CAAChN,KAAK,UAAUiN,QAAQ,YAAYC,MAAO5N,EAAI6c,QAAQpS,OAAQoD,WAAW,oBAAoB,CAAC7N,EAAIgJ,GAAG,kBAAiB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAACM,YAAY,iBAAiBqD,MAAM,CAAC,OAAS7D,EAAI6c,QAAQpS,OAAO,KAAOzK,EAAI6c,QAAQoL,OACvrB,QAC4B,SAA5BjoB,EAAI6c,QAAQiL,aAA2B9nB,EAAI6c,QAAQuK,MAEnDpnB,EAAI6c,QAAQiL,YACZ,mBACA,YAHA,OAGY,MAAQ9nB,EAAI6c,QAAQkL,KAAO,OAAS,MAAM,GAAE,KAAK,CAAElL,QAAS7c,EAAI6c,WAAY,GAAG7c,EAAIwK,OAAOxK,EAAIwK,KAAKtK,EAAG,kBAAkB,CAAC2D,MAAM,CAAC,kBAAkB7D,EAAI8D,cAAc,QAAU9D,EAAI6c,QAAQ,kBAAkB7c,EAAI8G,eAAe,kBAAkB9G,EAAImU,cAAc,uBAAuBnU,EAAIqH,mBAAmB,eAAerH,EAAIyQ,YAAY,gBAAgBzQ,EAAIkoB,aAAa,mBAAmBloB,EAAImoB,eAAe,uBAAuBnoB,EAAI2nB,mBAAmB,sBAAsB3nB,EAAIkG,mBAAmBL,GAAG,CAAC,eAAe,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,gBAAgB,EAAM,EAAE,uBAAuB,SAASD,GAAQtI,EAAIkoB,aAAe5f,CAAM,EAAE,wBAAwB,SAASA,GAAQtI,EAAIooB,cAAgB9f,CAAM,EAAE,sBAAsB,SAASA,GAAQtI,EAAIiS,YAAc3J,CAAM,EAAE,yBAAyBtI,EAAI6Q,qBAAqB,wBAAwB7Q,EAAIkI,oBAAoB,qBAAqBlI,EAAIqoB,iBAAiB,qBAAqBroB,EAAIuF,mBAAmB,CAACvF,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACV,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKV,EAAI0B,MAAM,KAAI,IAAI,GAAGxB,EAAG,oBAAoB,CAAC2D,MAAM,CAAC,kBAAkB7D,EAAI8D,cAAc,QAAU9D,EAAI6c,SAAShX,GAAG,CAAC,wBAAwB7F,EAAIkI,wBAAwB,GAAG,GAAE,KAAK,CAAE2U,QAAS7c,EAAI6c,WAAY,IAC1vC,EACIvc,GAAkB,GCtBlBP,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,oBAAoBqF,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,oBAAqBvI,EAAI6c,QAAQ,IAAI,CAAE7c,EAAI2Q,QAASzQ,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGxJ,EAAIsoB,eAAe,OAAOtoB,EAAIwK,KAAMxK,EAAIogB,QAASlgB,EAAG,MAAM,CAACM,YAAY,6BAA6B,CAACN,EAAG,MAAM,CAACM,YAAY,4CAA4C8F,MAAO,CACpb,mBAAoB,QAAQtG,EAAIqgB,UAAU7d,aACpCxC,EAAIsgB,QAASpgB,EAAG,MAAM,CAACM,YAAY,6BAA6B,CAACN,EAAG,QAAQ,CAAC2D,MAAM,CAAC,MAAQ,OAAO,OAAS,OAAO,SAAW,KAAK,CAAC3D,EAAG,SAAS,CAAC2D,MAAM,CAAC,IAAM7D,EAAIqgB,UAAU7d,WAAYxC,EAAIuoB,aAAcroB,EAAG,MAAM,CAACM,YAAY,mCAAmC,CAACN,EAAG,MAAM,CAACM,YAAY,UAAUqD,MAAM,CAAC,IAAM7D,EAAIwoB,SAAS,IAAM,YAAY,OAAS,QAAQtoB,EAAG,OAAO,CAACM,YAAY,QAAQ,CAACR,EAAIS,GAAGT,EAAIwJ,GAAG,GAAGxJ,EAAIqgB,UAAU3f,YAAYR,EAAG,OAAO,CAACM,YAAY,OAAO,CAACR,EAAIS,GAAGT,EAAIwJ,GAAG,GAAGxJ,EAAIqgB,UAAUpK,kBAAkBjW,EAAIwK,KAAKtK,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACN,EAAG,iBAAiB,CAAC2D,MAAM,CAAC,QAAU7D,EAAI6c,QAAQ0K,aAAanZ,QAAQ,MAAQpO,EAAIgnB,UAAU,kBAAkBhnB,EAAI2H,eAAe,eAAe3H,EAAI4H,YAAY,OAAQ,GAAMa,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,MAAS,IACx4B,EACIpB,GAAkB,G,w4CC4CtB,MAAAkmB,YAAA,GAAAC,YAAAA,IAAAvG,EAAA,MAEA,QACAxf,KAAA,eACAO,WAAA,CACAylB,cAAAA,IAGA3b,MAAA,CACA8R,QAAA,CAAAzZ,KAAAiL,OAAAC,UAAA,GACA3G,eAAA,CAAAvE,KAAAmJ,QAAA+B,UAAA,GACA1G,YAAA,CAAAxE,KAAAiL,OAAAC,UAAA,GACA0Y,UAAA,CAAA5jB,KAAAqJ,MAAA6B,UAAA,GACAqC,QAAA,CAAAvN,KAAAmJ,QAAAtB,SAAA,IAGAyB,MAAA,sBAEAhL,IAAAA,GACA,OACA8mB,SAAAC,GAEA,EAEAvnB,SAAA,CACAonB,aAAAA,GACA,eAAA1b,GAAA,KAAAiQ,QAAA0K,aACA,OAAA3a,GAAA,EACA,EACAyT,SAAAA,GACA,YAAAxD,QAAA0K,aAAA7V,MAAA,KAAAmL,QAAA0K,aAAA7V,MAAA,KACA,EACA6W,YAAAA,GACA,YAAA1L,QAAA0K,aAAA7V,KACA,EAEA0O,OAAAA,GACA,OAAAoG,GAAA,KAAAnG,UACA,EACAC,OAAAA,GACA,OAAAmG,GAAA,KAAApG,UACA,ICzFsR,MCOlR,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QClB5BtgB,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,8BAA8BI,MAAM,CAAE,aAAcZ,EAAI6c,QAAQ0K,eAAgB,CAACrnB,EAAG,MAAM,CAACM,YAAY,YAAY,CAA4B,IAA1BR,EAAI0oB,WAAW1b,OAAc9M,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,eAAe,CAAC2D,MAAM,CAAC,KAAO7D,EAAI0oB,WAAW,GAAG,kBAAkB1oB,EAAI8D,cAAc,QAAU9D,EAAI6c,QAAQ,MAAQ,GAAGhX,GAAG,CAAC,YAAY,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,YAAaD,EAAO,GAAGG,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,MAAS,GAAI1B,EAAI0oB,WAAW1b,QAAU,EAAG9M,EAAG,MAAMF,EAAI2I,GAAI3I,EAAI0oB,YAAY,SAASld,EAAKuF,GAAK,OAAO7Q,EAAG,MAAM,CAAC4I,IAAIiI,EAAM,KAAKvQ,YAAY,oBAAoB,CAACN,EAAG,eAAe,CAAC2D,MAAM,CAAC,KAAO2H,EAAK,kBAAkBxL,EAAI8D,cAAc,QAAU9D,EAAI6c,QAAQ,MAAQ9L,GAAKlL,GAAG,CAAC,YAAY,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,YAAaD,EAAO,GAAGG,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,MAAS,EAAE,IAAG,GAA8B,IAA1B1B,EAAI0oB,WAAW1b,OAAc9M,EAAG,MAAMF,EAAI2I,GAAI3I,EAAI0oB,YAAY,SAASld,EAAKuF,GAAK,OAAO7Q,EAAG,MAAM,CAAC4I,IAAIiI,EAAM,KAAKvQ,YAAY,uBAAuB,CAACN,EAAG,eAAe,CAAC2D,MAAM,CAAC,KAAO2H,EAAK,kBAAkBxL,EAAI8D,cAAc,QAAU9D,EAAI6c,QAAQ,MAAQ9L,GAAKlL,GAAG,CAAC,YAAY,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,YAAaD,EAAO,GAAGG,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,MAAS,EAAE,IAAG,GAAGxB,EAAG,MAAM,CAACF,EAAI2I,GAAI3I,EAAI0oB,WAAWlG,MAAM,EAAG,IAAI,SAAShX,EAAKuF,GAAK,OAAO7Q,EAAG,MAAM,CAAC4I,IAAIiI,EAAM,KAAKvQ,YAAY,uBAAuB,CAACN,EAAG,eAAe,CAAC2D,MAAM,CAAC,KAAO2H,EAAK,kBAAkBxL,EAAI8D,cAAc,QAAU9D,EAAI6c,QAAQ,MAAQ9L,GAAKlL,GAAG,CAAC,YAAY,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,YAAaD,EAAO,GAAGG,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,MAAS,EAAE,IAAGxB,EAAG,MAAM,CAACM,YAAY,kCAAkCI,MAAM,CAAE,sBAAuBZ,EAAI0oB,WAAW,GAAG3Q,SAAUlS,GAAG,CAAC,MAAQ,SAASyC,GAAgC,OAAxBA,EAAOyF,iBAAwB/N,EAAI2oB,eAAe3oB,EAAI0oB,WAAY1oB,EAAI0oB,WAAW,GAAG,IAAI,CAACxoB,EAAG,eAAe,CAAC2D,MAAM,CAAC,KAAO7D,EAAI0oB,WAAW,GAAG,kBAAkB1oB,EAAI8D,cAAc,QAAU9D,EAAI6c,QAAQ,MAAQ,GAAGhX,GAAG,CAAC,YAAY,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,YAAaD,EAAO,GAAGG,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,KAAQxB,EAAG,OAAO,CAACM,YAAY,cAAc,CAACR,EAAIS,GAAG,KAAKT,EAAIwJ,GAAGxJ,EAAI0oB,WAAW1b,OAAS,GAAG,QAAQ,IAAI,KAAMhN,EAAI4oB,WAAY1oB,EAAG,MAAMF,EAAI2I,GAAI3I,EAAI4oB,YAAY,SAASpd,EAAKuF,GAAK,OAAO7Q,EAAG,MAAM,CAAC4I,IAAIiI,EAAM,KAAKvQ,YAAY,uBAAuB,CAACN,EAAG,eAAe,CAAC2D,MAAM,CAAC,KAAO2H,EAAK,kBAAkBxL,EAAI8D,cAAc,QAAU9D,EAAI6c,QAAQ,MAAQ9L,GAAKlL,GAAG,CAAC,YAAY,SAASyC,GAAQ,OAAOtI,EAAIuI,MAAM,YAAaD,EAAO,GAAGG,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,KAAQxB,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAI6c,QAAQ9P,cAAe/M,EAAI6nB,mBAAoB3nB,EAAG,OAAO,CAACF,EAAIgJ,GAAG,kBAAiB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAACM,YAAY,iBAAiBqD,MAAM,CAAC,KAAO7D,EAAI6c,QAAQiL,YAAc,mBAAqB,YAAY,MAAQ9nB,EAAI6c,QAAQkL,KAAO,OAAS,MAAM,GAAE,KAAK,CAAElL,QAAS7c,EAAI6c,WAAY,GAAG7c,EAAIwK,QAAQ,EAAE,IAAG,GAAGxK,EAAIwK,KAAKxK,EAAI2I,GAAI3I,EAAI6oB,YAAY,SAASrd,EAAKuF,GAAK,OAAO7Q,EAAG,MAAM,CAAC4I,IAAIiI,EAAM,IAAIvQ,YAAY,qBAAqB,CAACN,EAAG,eAAe,CAAC2D,MAAM,CAAC,KAAO2H,EAAKuM,SAAWvM,EAAKS,MAAM,SAAU,EAAM,MAAQT,EAAKS,SAAS/L,EAAG,MAAM,CAACM,YAAY,qBAAqBqF,GAAG,CAAC,MAAQ,SAASyC,GAAiC,OAAzBA,EAAOwN,kBAAyB9V,EAAIiG,SAASuF,EAAM,WAAW,IAAI,CAACtL,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAAqB,QAAnBgL,EAAKyK,UAAqB/V,EAAG,QAAQ,CAACU,MAAM,CAAE,WAAY4K,EAAKuM,SAAWvM,EAAKS,OAAQpI,MAAM,CAAC,IAAM,GAAG2H,EAAKuM,QAAUvM,EAAKsd,SAAWtd,EAAKhJ,MAAM,KAAO,qBAAqBtC,EAAG,MAAM,CAACM,YAAY,YAAYI,MAAM,CAAE,WAAY4K,EAAKuM,SAAWvM,EAAKS,OAAQpI,MAAM,CAAC,IAAyB,QAAnB2H,EAAKyK,UAAsBjW,EAAIkW,QAAUlW,EAAImW,QAAQ,IAAM,gBAAgBjW,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,MAAM,CAACM,YAAY,YAAYqD,MAAM,CAAC,IAAyB,QAAnB2H,EAAKyK,UAAsBjW,EAAIkW,QAAUlW,EAAImW,QAAQ,IAAM,cAAcjW,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACR,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGsS,mBAAmBtQ,EAAKyK,UAAY,GAAGzK,EAAK9K,QAAQ8K,EAAKyK,YAAczK,EAAK9K,OAAO,OAAOR,EAAG,MAAM,CAACM,YAAY,gBAAgBqD,MAAM,CAAC,IAA6B,IAAvB7D,EAAI6c,QAAQoK,OAAejnB,EAAI+oB,aAAe/oB,EAAIgpB,iBAAiB,IAAM,qBAAqB9oB,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,MAAM,CAACM,YAAY,cAAcN,EAAG,MAAM,CAACM,YAAY,QAAQ,CAACN,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAI6c,QAAQ9P,cAAe/M,EAAI6nB,mBAAoB3nB,EAAG,OAAO,CAACF,EAAIgJ,GAAG,kBAAiB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAACM,YAAY,iBAAiBqD,MAAM,CAAC,KAAO7D,EAAI6c,QAAQiL,YAAc,mBAAqB,YAAY,MAAQ9nB,EAAI6c,QAAQkL,KAAO,OAAS,MAAM,GAAE,KAAK,CAAElL,QAAS7c,EAAI6c,WAAY,GAAG7c,EAAIwK,YAAY,EAAE,IAAGtK,EAAG,iBAAiB,CAAC2D,MAAM,CAAC,QAAU7D,EAAI6c,QAAQzO,QAAQ,MAAQpO,EAAIgnB,UAAU,kBAAkBhnB,EAAI2H,eAAe,eAAe3H,EAAI4H,YAAY,mBAAmB5H,EAAI0Q,gBAAgBjI,YAAYzI,EAAI0I,GAAG,CAAC1I,EAAI2I,GAAI3I,EAAI4I,cAAc,SAASC,EAAEnI,GAAM,MAAO,CAACoI,IAAIpI,EAAKqI,GAAG,SAASrH,GAAM,MAAO,CAAC1B,EAAIgJ,GAAGtI,EAAK,KAAK,KAAKgB,GAAM,EAAE,KAAI,MAAK,MAAS,EACp/K,EACIpB,GAAkB,G,ogLCFlBP,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,8BAA8B,CAAER,EAAIogB,QAASlgB,EAAG,MAAM,CAACuP,IAAI,WAAazP,EAAIkK,MAAM1J,YAAY,8BAA8BqF,GAAG,CAAC,UAAY,SAASyC,GAAQtI,EAAIipB,YAAa,CAAI,EAAE,WAAa,SAAS3gB,GAAQtI,EAAIipB,YAAa,CAAK,EAAE,MAAQ,SAAS3gB,GAAiC,OAAzBA,EAAOwN,kBAAyB9V,EAAIiG,SAAS,UAAU,IAAI,CAAC/F,EAAG,eAAe,CAAC2D,MAAM,CAAC,KAAO7D,EAAIkpB,cAAc,SAAU,EAAM,MAAQlpB,EAAIwL,KAAKS,SAAS/L,EAAG,MAAM,CAACM,YAAY,oBAAoBI,MAAM,CAC7hB,cAAeZ,EAAIkpB,eAAwC,IAAvBlpB,EAAI6c,QAAQoK,OAChD,OAAQjnB,EAAI6c,QAAQzO,SACpB9H,MAAO,CACP,mBAAoB,QAAQtG,EAAIkpB,eAAgBlpB,EAAIwL,KAAKsd,UAA2B9oB,EAAIwL,KAAKhJ,QAC7F,aAAc,GAAGxC,EAAImpB,gBAAgB5W,gBACnC,CAACrS,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAAO,mBAAmB,CAAG7D,EAAIkpB,cAA2clpB,EAAIwK,KAAhctK,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACN,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAACN,EAAG,OAAO,CAACM,YAAY,kBAAkB,CAACR,EAAIS,GAAGT,EAAIwJ,GAAGxJ,EAAIwL,KAAKuB,cAAe/M,EAAI6nB,mBAAoB3nB,EAAG,OAAO,CAACF,EAAIgJ,GAAG,kBAAiB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAACM,YAAY,iBAAiBqD,MAAM,CAAC,KAAO7D,EAAIwL,KAAKsc,YAAc,mBAAqB,YAAY,MAAQ9nB,EAAIwL,KAAKuc,KAAO,OAAS,MAAM,GAAE,KAAK,CAAEvc,KAAMxL,EAAIwL,QAAS,GAAGxL,EAAIwK,YAAqB,IAAI,GAAIxK,EAAIsgB,QAASpgB,EAAG,MAAM,CAACM,YAAY,sBAAsBI,MAAM,CAC5lB,OAAQZ,EAAIynB,UACX,CAACvnB,EAAG,eAAe,CAAC2D,MAAM,CAAC,KAAO7D,EAAIkpB,cAAc,SAAU,EAAM,MAAQlpB,EAAIwL,KAAKS,SAAS/L,EAAG,MAAM,CAACU,MAAM,CAC7G,cAAeZ,EAAIkpB,eAAwC,IAAvBlpB,EAAI6c,QAAQoK,SAC/C,CAAC/mB,EAAG,QAAQ,CAAC2D,MAAM,CAAC,MAAQ,OAAO,OAAS,OAAO,SAAW,KAAK,CAAC3D,EAAG,SAAS,CAAC2D,MAAM,CAAC,IAAM7D,EAAIkpB,cAAgBlpB,EAAIwL,KAAKsd,SAAW9oB,EAAIwL,KAAKhJ,YAAY,GAAGxC,EAAIwK,MAC3K,EACIlK,GAAkB,GCZlBP,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAQF,EAAIuJ,KAAMrJ,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAAER,EAAI+X,QAAS7X,EAAG,MAAM,CAACM,YAAY,iCAAiC,CAACN,EAAG,OAAOA,EAAG,OAAOA,EAAG,SAASF,EAAIwK,KAAMxK,EAAIiM,MAAO/L,EAAG,MAAM,CAACA,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAI8N,UAAU,IAAM,kBAAkB9N,EAAIwK,OAAOxK,EAAIwK,IACtU,EACIlK,GAAkB,GCatB,IACAI,KAAA,cAEAqK,MAAA,CACAxB,KAAA,CAAAnG,KAAAmJ,QAAAtB,SAAA,GACA8M,QAAA,CAAA3U,KAAAmJ,QAAAtB,SAAA,GACAgB,MAAA,CAAA7I,KAAAmJ,QAAAtB,SAAA,IAGAvJ,IAAAA,GACA,OACAoM,UAAAI,EAEA,GC5B4Q,MCOxQ,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QCgDhC,MAAAsY,YAAA,GAAAC,YAAA,eAAA2C,IAAAlJ,EAAA,MAEA,QACAxf,KAAA,cACAO,WAAA,CAAAqL,QAAA,EAAA+c,YAAAA,IAEAte,MAAA,CACA8R,QAAA,CAAAzZ,KAAAiL,OAAAC,UAAA,GACA9C,KAAA,CAAApI,KAAAiL,OAAAC,UAAA,GACApE,MAAA,CAAA9G,KAAA2X,OAAAzM,UAAA,IAGA5B,MAAA,cAEAhL,IAAAA,GACA,OACAynB,gBAAA,GACAG,cAAA,EACAL,YAAA,EAEA,EAEA/nB,SAAA,CACAumB,OAAAA,GACA,OAAA2B,GAAA,KAAA5d,KACA,EAEA0d,aAAAA,GACA,YAAA1d,KAAAuM,SAAA,KAAAvM,KAAAS,KACA,EACAmU,OAAAA,GACA,OAAAoG,GAAA,KAAAhb,KACA,EACA8U,OAAAA,GACA,OAAAmG,GAAA,KAAAjb,KACA,EACAqc,kBAAAA,GACA,OACA,SAAAhL,QAAAoK,SACA,KAAApK,QAAA1Q,UACA,KAAA0Q,QAAAuK,OAAA,KAAAvK,QAAAiL,aAAA,KAAAjL,QAAAkL,KAEA,GAGAjmB,MAAA,CACA0J,KAAA,CACAkQ,WAAA,EACAJ,OAAAA,GACA,KAAAiO,cACA,IAIA/lB,OAAAA,GACA,MAAAiM,EAAA,KAAAa,MAAA,gBAAApG,OAEAuF,IACA,KAAA0Z,gBAAA,CACA5W,UAAA9C,EAAA+Z,YAAA,GACAC,UAAAha,EAAAsQ,aAAA,KAGA,EAEA/d,QAAA,CACAunB,YAAAA,GACA,IAAA/C,GAAA,KAAAhb,MAAA,OACA,KAAA8d,cAAA,EACA,MAAArf,EAAA,IAAAub,MACAvb,EAAA+b,IAAA,KAAAxa,KAAAhJ,IACAyH,EAAAyK,iBAAA,iBAAA4U,cAAA,GACA,EACArjB,QAAAA,CAAAyjB,GACA,KAAAR,eACA,KAAA3gB,MAAA,aAAAiD,KAAA,KAAAA,KAAAke,UAEA,IC/IqR,MCOjR,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QC0KhC,MAAM,iBAANC,GAAAlD,YAAA,GAAAD,YAAAA,IAAAtG,EAAA,MAEA,QACAxf,KAAA,eACAO,WAAA,CAAAqL,QAAA,EAAAoa,cAAA,GAAAkD,YAAA,GAAAP,YAAAA,IAEAte,MAAA,CACAjH,cAAA,CAAAV,KAAA,CAAA4H,OAAA+P,QAAAzM,UAAA,GACAuO,QAAA,CAAAzZ,KAAAiL,OAAAC,UAAA,GACA0Y,UAAA,CAAA5jB,KAAAqJ,MAAA6B,UAAA,GACA3G,eAAA,CAAAvE,KAAAmJ,QAAA+B,UAAA,GACA1G,YAAA,CAAAxE,KAAAiL,OAAAC,UAAA,GACAoC,eAAA,CAAAtN,KAAA4H,OAAAsD,UAAA,IAGA5B,MAAA,iCAEAhL,IAAAA,GACA,OACAwU,QAAAI,GACAH,QAAAI,GACAwS,aAAAc,GACAb,iBAAAc,GAEA,EAEA5oB,SAAA,CACAwnB,UAAAA,GACA,YAAA7L,QAAAnL,MAAA+G,QAAAjN,GAAAgb,GAAAhb,IACA,EACAod,UAAAA,GACA,YAAA/L,QAAAnL,MAAA+G,QAAAjN,GAAAib,GAAAjb,IACA,EACAqd,UAAAA,GACA,YAAAhM,QAAAnL,MAAA+G,QAAAjN,IAAAme,GAAAne,IACA,EACAqc,kBAAAA,GACA,OACA,SAAAhL,QAAAoK,SACA,KAAApK,QAAA1Q,UACA,KAAA0Q,QAAAuK,OAAA,KAAAvK,QAAAiL,aAAA,KAAAjL,QAAAkL,KAEA,GAGA/lB,QAAA,CACAiE,QAAAA,CAAAuF,EAAAke,GACAle,EAAAuM,SAAAvM,EAAAS,OACA,KAAA1D,MAAA,aAAAiD,OAAAke,UAEA,EACAf,cAAAA,CAAAD,EAAAld,GACAA,EAAAuM,SAAAvM,EAAAS,OACA,KAAA1D,MAAA,mBAAAmgB,EAEA,ICnPsR,MCOlR,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QClB5B3oB,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,+BAA+B,CAAGR,EAAI6c,QAAQ5Q,MAKuJjM,EAAIwK,KALpJtK,EAAG,MAAM,CAACM,YAAY,wBAAwBI,MAAM,CAAE,gBAAiBZ,EAAI6c,QAAQnL,OAAQpL,MAAO,CACxO4N,QAASlU,EAAI2nB,mBAAqB,OAAS,UAC3C9B,OAAO7lB,EAAI+pB,uBAAuB/c,QAAUhN,EAAIqH,mBAAqB,UACnE,CAACnH,EAAG,mBAAmB,CAAC2D,MAAM,CAAC,KAAO,iBAAiB,IAAM,SAAS,CAAE7D,EAAIgqB,kBAAoBhqB,EAAIiqB,mBAAoB/pB,EAAG,MAAM,CAAC4I,IAAI,IAAItI,YAAY,qBAAqBI,MAAM,CAC/K,iBAAyC,IAAvBZ,EAAI6c,QAAQoK,UAC5BjnB,EAAIwK,KAAMxK,EAAIgqB,iBAAkB9pB,EAAG,MAAM,CAAC4I,IAAI,IAAI2G,IAAI,aAAajP,YAAY,qCAAqCqF,GAAG,CAAC,MAAQ7F,EAAIkqB,cAAc,CAAClqB,EAAIgJ,GAAG,iBAAgB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,WAAW,MAAQ,aAAa,KAAI,GAAG7D,EAAIwK,QAAQ,GAAaxK,EAAI+pB,uBAAuB/c,OAAQ9M,EAAG,aAAa,CAAC2D,MAAM,CAAC,KAA8B,IAAvB7D,EAAI6c,QAAQoK,OAAe,iBAAmB,oBAAoB,CAAEjnB,EAAIooB,cAAeloB,EAAG,MAAM,CAACwN,WAAW,CAAC,CAAChN,KAAK,gBAAgBiN,QAAQ,kBAAkBC,MAAO5N,EAAImqB,aAActc,WAAW,iBAAiB4B,IAAI,cAAcjP,YAAY,mBAAmBI,MAAM,CACzmB,iBAAkBZ,EAAI6c,QAAQoK,QAC9B3gB,MAAO,CAAEuN,IAAK,GAAG7T,EAAIoqB,qBAAuB,CAAClqB,EAAG,MAAM,CAACM,YAAY,iBAAiBR,EAAI2I,GAAI3I,EAAI+pB,wBAAwB,SAASL,GAAQ,OAAOxpB,EAAG,MAAM,CAAC4I,IAAI4gB,EAAOhpB,MAAM,CAACR,EAAG,MAAM,CAACM,YAAY,gBAAgBqF,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAI6Q,qBAAqB6Y,EAAO,IAAI,CAAC1pB,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGkgB,EAAOW,OAAO,QAAQ,IAAG,KAAKrqB,EAAIwK,OAAOxK,EAAIwK,KAAMxK,EAAI6c,QAAQ5Q,MAAO/L,EAAG,MAAM,CAACM,YAAY,qBAAqBqF,GAAG,CAAC,MAAQ,SAASyC,GAAgC,OAAxBA,EAAOyF,iBAAwB/N,EAAIuI,MAAM,qBAAqB,IAAI,CAACrI,EAAG,MAAM,CAAC2D,MAAM,CAAC,IAAM7D,EAAI8N,UAAU,IAAM,kBAAkB9N,EAAIwK,MAAM,EAC/kB,EACIlK,GAAkB,GCiFtB,IACAI,KAAA,iBACAO,WAAA,CACAqL,QAAAA,GAGAoB,WAAA,CACA+P,aAAAC,KAAAA,WAGA3S,MAAA,CACA8R,QAAA,CAAAzZ,KAAAiL,OAAAC,UAAA,GACAxH,eAAA,CAAA1D,KAAAqJ,MAAA6B,UAAA,GACA6F,cAAA,CAAA/Q,KAAAgR,eAAAnJ,QAAA,MACA5D,mBAAA,CAAAjE,KAAAmJ,QAAA+B,UAAA,GACAmC,YAAA,CAAArN,KAAAmJ,QAAA+B,UAAA,GACA4Z,aAAA,CAAA9kB,KAAAmJ,QAAA+B,UAAA,GACA6Z,eAAA,CAAA/kB,KAAA,CAAA4H,OAAA+P,QAAA9P,QAAA,MACA0c,mBAAA,CAAAvkB,KAAAmJ,QAAA+B,UAAA,IAGA5B,MAAA,CACA,sBACA,wBACA,uBACA,eACA,yBACA,wBACA,qBACA,0BAGAhL,IAAAA,GACA,OACA0oB,eAAA,EACAhC,eAAA,EACAkC,gBAAA,EACArY,aAAA,EACAnE,UAAAI,EAEA,EAEAhN,SAAA,CACA8oB,gBAAAA,GACA,OACA,KAAAD,uBAAA/c,QACA,KAAAkb,eACA,KAAArL,QAAA1Q,UACA,KAAA0Q,QAAA0N,iBACA,KAAA5C,kBAEA,EACAsC,kBAAAA,GACA,OACA,KAAA5iB,oBACA,KAAA6gB,eACA,KAAArL,QAAA1Q,UACA,KAAA0Q,QAAA2N,mBACA,KAAA7C,kBAEA,EACAoC,sBAAAA,GACA,gBAAAlN,QAAAoK,OAAA,KAAAngB,eAAA,KAAAA,eAAA2R,QAAAoE,IAAAA,EAAA4N,QACA,GAGA3oB,MAAA,CACAmQ,WAAAA,CAAAqC,GACA,KAAA/L,MAAA,sBAAA+L,GACAA,IAAA,KAAA8T,eAAA,EACA,EACA3X,WAAAA,CAAA6D,GACAA,IACA,KAAAoW,aACA,KAAAP,eAEA,EACA/B,aAAAA,CAAA9T,GACA,KAAA/L,MAAA,wBAAA+L,EACA,GAGAtS,QAAA,CACAkoB,WAAAA,GACA,KAAAI,iBAEA,KAAAlC,eAAA,KAAAA,cACA,KAAAA,gBAEA,KAAA7f,MAAA,mBAEAgM,YAAA,KACA,SAAAJ,gBAAA,KAAA7D,MAAAqa,cAAA,KAAAra,MAAAsa,WACA,OAGA,MAAAR,EAAA,KAAA9Z,MAAAqa,YAAAhV,wBAAAhC,OAEAkX,EAAA,KAAAva,MAAAsa,WAAAjV,wBAAA9B,IACA6B,EAAA,KAAAvB,cAAAwB,wBAAA9B,IAEAiX,EAAApV,EAAAmV,EAAAT,EAAA,GAEA,KAAAA,eAAAU,EAAA,IACAV,CAAA,KAEA,EACAD,YAAAA,GACA,KAAA/B,eAAA,EACA,KAAAkC,gBAAA,EACA,KAAAS,qBACAxW,YAAA,SAAA+V,gBAAA,OACA,EAEAI,UAAAA,GACA,KAAAzY,aAAA,EACA,KAAA8Y,oBACA,EACAA,kBAAAA,GACA,KAAA5C,iBAAA,KAAAtL,QAAAlT,KACA,KAAApB,MAAA,0BAEA,EACAsI,oBAAAA,CAAA6Y,GACA,KAAAS,eAEA,KAAA5hB,MAAA,yBAAAmhB,EACA,ICzNwR,MCOpR,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QClB5B3pB,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAASF,EAAI6c,QAAQ1Q,QAI8FnM,EAAIwK,KAJzFtK,EAAG,mBAAmB,CAAC2D,MAAM,CAAC,KAAO,iBAAiB,IAAM,SAAS7D,EAAI2I,GAAI3I,EAAI6c,QAAQmO,WAAW,SAASC,EAASniB,GAAK,OAAO5I,EAAG,SAAS,CAACwN,WAAW,CAAC,CAAChN,KAAK,OAAOiN,QAAQ,SAASC,MAAOqd,EAASje,OAAQa,WAAW,oBAAoB/E,IAAIA,EAAM,EAAEtI,YAAY,sBAAsBI,MAAM,CACxX,mBAA4D,IAAzCqqB,EAASxY,QAAQzS,EAAI8D,gBACxCwC,MAAO,CACP4kB,MAA8B,IAAvBlrB,EAAI6c,QAAQoK,OAAe,QAAU,QAC3CphB,GAAG,CAAC,MAAQ,SAASyC,GAAQ,OAAOtI,EAAIkI,oBAAoB,CAAE0M,QAAS9L,GAAOmiB,EAAS,IAAI,CAACjrB,EAAIS,GAAG,IAAIT,EAAIwJ,GAAGV,IAAM5I,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIwJ,GAAGyhB,EAASje,YAAY,IAAG,EAC5K,EACI1M,GAAkB,GCetB,IACAI,KAAA,mBAEAqK,MAAA,CACAjH,cAAA,CAAAV,KAAA,CAAA4H,OAAA+P,QAAAzM,UAAA,GACAuO,QAAA,CAAAzZ,KAAAiL,OAAAC,UAAA,IAGA5B,MAAA,0BAEA1K,QAAA,CACAkG,mBAAAA,CAAAyD,EAAAsf,GACA,KAAA1iB,MAAA,yBAAAoD,QAAAsf,YACA,IClC0R,MCOtR,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QClB5BlrB,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,MAAM,CAACM,YAAY,iBAAiBqF,GAAG,CAAC,MAAQ7F,EAAImrB,WAAW,CAAEnrB,EAAIorB,UAAWprB,EAAIgJ,GAAG,oBAAmB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,iBAAiB,IAAG7D,EAAIgJ,GAAG,mBAAkB,WAAW,MAAO,CAAC9I,EAAG,WAAW,CAAC2D,MAAM,CAAC,KAAO,gBAAgB,KAAI,GAAG3D,EAAG,gBAAgB,CAAC2D,MAAM,CAAC,WAAa7D,EAAIqrB,UAAUxlB,GAAG,CAAC,kBAAkB7F,EAAIsrB,iBAAiB,uBAAuB,SAAShjB,GAAQ,OAAOtI,EAAIuI,MAAM,uBAAwBD,EAAO,KAAKpI,EAAG,QAAQ,CAAC2D,MAAM,CAAC,GAAK7D,EAAIurB,aAAa,IAAMvrB,EAAIwrB,gBAAgB,IAC1oB,EACIlrB,GAAkB,GCFlBP,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACuP,IAAI,WAAWjP,YAAY,iBAAiBqF,GAAG,CAAC,UAAY7F,EAAIyrB,YAAY,UAAY,SAASnjB,GAAQ,OAAOtI,EAAIuI,MAAM,wBAAwB,EAAK,EAAE,SAAW,SAASD,GAAQ,OAAOtI,EAAIuI,MAAM,wBAAwB,EAAM,IAAI,CAACrI,EAAG,MAAM,CAACM,YAAY,uBAAuB,CAACN,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAACN,EAAG,MAAM,CAACM,YAAY,oBAAoB8F,MAAO,CAAEuf,MAAO,GAAG7lB,EAAI0rB,iBAAmBxrB,EAAG,MAAM,CAACM,YAAY,eAAeI,MAAM,CAAE,uBAAwBZ,EAAI2rB,aAAcrlB,MAAO,CAAEslB,KAAM,GAAG5rB,EAAI0rB,sBACllB,EACIprB,GAAkB,GCoBtB,IACAyK,MAAA,CACA2gB,WAAA,CAAAtoB,KAAA2X,OAAA9P,QAAA,IAGAyB,MAAA,2CAEAhL,IAAAA,GACA,OACAiqB,aAAA,EAEA,EAEA3pB,QAAA,CACAypB,WAAAA,CAAAtW,GACA,KAAAwW,aAAA,EACA,MAAAE,EAAA,KAAAC,0BAAA3W,EAAA,KAAA7E,MAAA+a,UACA,KAAA9iB,MAAA,kBAAAsjB,GACAjgB,SAAA8I,iBAAA,iBAAAqX,aACAngB,SAAA8I,iBAAA,eAAAsX,UACA,EACAA,SAAAA,CAAA7W,GACA,KAAAwW,aAAA,EACA/f,SAAA+Z,oBAAA,eAAAqG,WACApgB,SAAA+Z,oBAAA,iBAAAoG,aACA,MAAAF,EAAA,KAAAC,0BAAA3W,EAAA,KAAA7E,MAAA+a,UACA,KAAA9iB,MAAA,kBAAAsjB,EACA,EACAE,WAAAA,CAAA5W,GACA,MAAA0W,EAAA,KAAAC,0BAAA3W,EAAA,KAAA7E,MAAA+a,UACA,KAAA9iB,MAAA,kBAAAsjB,EACA,EACAC,yBAAAA,CAAA3W,EAAA8W,GACA,MAAAC,EAAAD,EAAAtW,wBAAAkQ,MACAsG,EAAAF,EAAAtW,wBAAAiW,KACA,IAAAQ,GAAAjX,EAAAkX,QAAAF,GAAAD,EAKA,OAHAE,EAAAA,EAAA,IAAAA,EACAA,EAAAA,EAAA,IAAAA,EAEAA,CACA,IC/DsR,MCOlR,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QCShC,IACA1rB,KAAA,cACAO,WAAA,CACAqL,QAAA,EACAggB,aAAAA,IAGAvhB,MAAA,CACAib,IAAA,CAAA5iB,KAAA4H,OAAAC,QAAA,OAGAyB,MAAA,gDAEAhL,IAAAA,GACA,OACA0pB,WAAA,EACAmB,SAAA,KAAAC,gBAAA,GACAC,WAAA,KAAAD,gBAAA,GACAnB,SAAA,EAEA,EAEAnqB,SAAA,CACAqqB,YAAAA,GACA,0BAAAmB,MACA,EACAlB,WAAAA,GACA,YAAAxF,IAAA,KAAAA,KACA,KAAA2G,gBACA,KACA,GAGAnpB,OAAAA,GACA,KAAAopB,OAAAhhB,SAAAihB,eAAA,KAAAtB,cAEA,KAAAqB,OAAAlY,iBAAA,cACA,KAAA0W,WAAA,KAGA,KAAAwB,OAAAlY,iBAAA,mBACA,KAAAiY,gBACA,KAAAJ,SAAA,KAAAC,gBAAA,KAAAI,OAAAL,UACA,KAAAO,oBAAA,IAGA,KAAAF,OAAAlY,iBAAA,kBAAAqY,aACA,EAEA/qB,QAAA,CACAwqB,eAAAA,CAAAQ,GACA,WAAAC,KAAA,IAAAD,GAAAE,cAAA5J,OAAA,KACA,EACA6H,QAAAA,GACA,KAAAK,cAEA,KAAAJ,UAAA,KAAAwB,OAAAO,QACA5Y,YAAA,SAAAqY,OAAAQ,SAEA,KAAAhC,WAAA,KAAAA,UACA,EACAuB,aAAAA,GACA,KAAAvB,WAAA,KAAAwB,OAAAO,QAEA,KAAAZ,SAAA,KAAAC,gBAAA,GACA,KAAAC,WAAA,KAAAD,gBAAA,GACA,KAAAnB,SAAA,EACA,KAAAD,WAAA,EACA,KAAA0B,oBACA,EACAC,YAAAA,GACA,KAAAN,WAAA,KAAAD,gBAAA,KAAAI,OAAAS,aACA,KAAAhC,SAAA,KAAAuB,OAAAS,YAAA,KAAAT,OAAAL,SAAA,IACA,KAAAO,oBACA,EACAxB,gBAAAA,CAAAc,GACAA,IAAA,KAAAQ,OAAAS,YAAAjB,EAAA,KAAAQ,OAAAL,SACA,EACAO,kBAAAA,GACA,KAAAvkB,MAAA,4BAAA8iB,SAAA,OAAAoB,WAAA,KAAAF,SACA,IC3GqR,MCOjR,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QCyMhC,MAAM,mBAANe,IAAApN,EAAA,OACAkJ,YAAAA,IAAAlJ,EAAA,MAEA,QACAxf,KAAA,UACAO,WAAA,CACAqL,QAAA,EACAoa,cAAA,GACA6G,YAAA,GACAC,aAAA,GACAC,aAAA,GACAC,eAAA,GACAC,iBAAAA,IAGA5iB,MAAA,CACAjH,cAAA,CAAAV,KAAA,CAAA4H,OAAA+P,QAAAzM,UAAA,GACAe,aAAA,CAAAjM,KAAAiL,OAAAC,UAAA,GACApE,MAAA,CAAA9G,KAAA2X,OAAAzM,UAAA,GACAuO,QAAA,CAAAzZ,KAAAiL,OAAAC,UAAA,GACA/J,SAAA,CAAAnB,KAAAqJ,MAAA6B,UAAA,GACA8B,cAAA,CAAAhN,KAAAiL,OAAAC,UAAA,GACA0Y,UAAA,CAAA5jB,KAAAqJ,MAAAxB,QAAAA,IAAA,IACAnE,eAAA,CAAA1D,KAAAqJ,MAAA6B,UAAA,GACA6F,cAAA,CAAA/Q,KAAAgR,eAAAnJ,QAAA,MACAuF,YAAA,CAAApN,KAAAqJ,MAAAxB,QAAAA,IAAA,IACA5D,mBAAA,CAAAjE,KAAAmJ,QAAA+B,UAAA,GACAhH,uBAAA,CAAAlE,KAAAmJ,QAAA+B,UAAA,GACA3G,eAAA,CAAAvE,KAAAmJ,QAAA+B,UAAA,GACA1G,YAAA,CAAAxE,KAAAiL,OAAAC,UAAA,GACAmC,YAAA,CAAArN,KAAAmJ,QAAA+B,UAAA,GACAoC,eAAA,CAAAtN,KAAA4H,OAAAsD,UAAA,GACApI,kBAAA,CAAA9C,KAAAoJ,SAAAvB,QAAAA,KAAA,KACA0F,QAAA,CAAAvN,KAAAmJ,QAAAtB,SAAA,GACAvF,aAAA,CAAAtC,KAAAiL,OAAAC,UAAA,GACA5H,OAAA,CAAAtD,KAAA,CAAA4H,OAAA+P,QAAAzM,UAAA,IAGA5B,MAAA,CACA,eACA,gBACA,YACA,yBACA,wBACA,mBACA,qBACA,qBAGAhL,IAAAA,GACA,OACAymB,eAAA,KACAD,cAAA,EACAE,eAAA,EACAnW,aAAA,EACA2b,WAAA,GACAlG,aAAA,QACAC,oBAAA,EAEA,EAEAzmB,SAAA,CACA8mB,aAAAA,GACA,YAAAnL,QAAAnL,OAAA,KAAAmL,QAAAnL,OAAA,KAAAmL,QAAA1Q,OACA,EACA0a,QAAAA,GACA,YAAA3c,MAAA,QAAA2S,QAAA/P,OAAA,KAAAvI,SAAA,KAAA2F,MAAA,GAAA4C,IACA,EACAoa,aAAAA,GACA,gBAAAhd,OAEA,KAAAA,MAAA,QAAA2S,QAAAjQ,WAAA,KAAArI,SAAA,KAAA2F,MAAA,GAAA0C,QACA,EACAua,cAAAA,GACA,YAAA/W,cAAAzG,MAAA,KAAAkT,QAAAlT,KAAA,KAAAwe,iBAAA,KAAAtL,QAAAlT,GACA,EACA8d,OAAAA,GACA,YAAA5K,QAAAnL,OAAA4N,MAAA9T,GAAA4d,GAAA5d,IACA,EAEAqc,kBAAAA,GACA,OACA,SAAAhL,QAAAoK,SACA,KAAApK,QAAA1Q,UACA,KAAA0Q,QAAAuK,OACA,KAAAvK,QAAAiL,aACA,KAAAjL,QAAAkL,MACA,cAAAlL,QAAAiL,aACA,KAAAjL,QAAAoL,OAEA,GAGAnmB,MAAA,CACA0O,YAAA,CACAkL,WAAA,EACAC,MAAA,EACAL,OAAAA,CAAAhH,GACA,IAAAA,EAAAtH,SAAA,KAAA1F,uBACA,YAAAsmB,WAAA,GAGA,KAAAA,WAAAtZ,EAAAuZ,QAAA,CAAAC,EAAAC,IAAAA,EAAA7jB,MAAA4jB,EAAA5jB,MAAA6jB,EAAAD,GACA,IAIAtqB,OAAAA,GACA8pB,GAAA,KAAAzQ,SAEA,KAAAtU,MAAA,iBACAsU,QAAA,KAAAA,QACA3S,MAAA,KAAAA,MACAuF,IAAA,KAAAa,MAAA,KAAAuM,QAAAlT,MAEA,EAEA3H,QAAA,CACAqlB,cAAAA,GACA,KAAAa,cAAA,EACA,KAAA8F,mBAAA,KAAA7F,eAAA,KAAAtL,QAAAlT,IACA,EACAqkB,cAAAA,GACA,YAAAnR,QAAA1Q,OACA,EACAmb,cAAAA,GACA,KAAAc,eAAA,KAAAnW,cAAA,KAAAiW,cAAA,GACA,KAAAC,eAAA,IACA,EACAliB,QAAAA,CAAAuF,GACA,KAAAjD,MAAA,aAAAsU,QAAA,KAAAA,QAAArR,QACA,EAEAqF,oBAAAA,CAAA6Y,GACA,KAAAxB,cAAA,EACA,KAAAC,eAAA,KAEA5T,YAAA,KACA,KAAAhM,MAAA,0BAAAmhB,SAAA7M,QAAA,KAAAA,SAAA,GACA,IACA,EACA3U,mBAAAA,EAAA,MAAAyD,EAAA,SAAAsf,IACA,KAAA1iB,MAAA,yBACA0lB,UAAA,KAAApR,QAAAlT,IACAshB,SAAAtf,EACAuiB,OAAAjD,IAAA,IAAAA,EAAAxY,QAAA,KAAA3O,iBAEA,KAAAokB,cAAA,CACA,EACA7f,eAAAA,CAAA3G,GACA,KAAA6G,MAAA,mBAAA7G,EACA,EACA2mB,gBAAAA,CAAA9Z,GACA,KAAAhG,MAAA,qBAAAgG,EACA,EACAhJ,gBAAAA,GACA,KAAAgD,MAAA,qBACA,ICxXwQ,MCOpQ,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QClBhC,IAAgB4lB,EAAOC,EAAOC,EAAO/Z,EAAKga,GAAa,IAChDha,GAAe,KAARA,EAEL6Z,EAAM1V,QAAO8V,GACdD,EAEAhK,GAAaiK,EAAEH,IAAQE,WAAWhK,GAAahQ,KAASgQ,GAAaiK,EAAEF,IAAQC,WAAWhK,GAAahQ,IAEpGgQ,GAAaiK,EAAEH,IAAQzR,SAAS2H,GAAahQ,KAASgQ,GAAaiK,EAAEF,IAAQ1R,SAAS2H,GAAahQ,MAP7E6Z,EAWjC,SAAS7J,GAAakK,GAEpB,OAAOA,EACJtS,cACAuS,UAAU,OACVxM,QAAQ,mBAAoB,GACjC,C,gDChBA,IAAIyM,GACJ,IACEA,GAASxO,EAAQ,KACnB,CAAE,MAAOyO,IACPD,GAAS,CAAEE,SAAS,EACtB,CAEA,MAAM,WAAEC,IAAeH,GAEvB,aACEI,WAAAA,CAAY/X,GACV,GAAI2X,GAAOE,QACT,MAAM,IAAIG,MACR,8GAIJ9uB,KAAK+uB,QAAUjY,EAAOiY,QACtB/uB,KAAKgvB,WAAalY,EAAOkY,WACzBhvB,KAAKivB,WAAa,GAClBjvB,KAAKkvB,QAAU,IAAIN,GAAW,EAAG5uB,KAAKgvB,WAAYhvB,KAAK+uB,QACzD,CAEAI,MAAAA,CAAOC,GACL,MAAMC,EAAa,KACbC,EAAUtvB,KAAKuvB,eAAeH,GACpC,IAAII,EAAYF,EAAQviB,OAExB,IAAK,IAAInE,EAAI,EAAG4mB,GAAa,EAAG5mB,GAAKymB,EAAY,CAC/C,MAAM1D,EAAO2D,EAAQG,SAAS7mB,EAAGA,EAAIymB,GAC/BvK,EAAS9kB,KAAKkvB,QAAQQ,aAAa/D,GACzC3rB,KAAKivB,WAAWzL,KAAK,IAAImM,UAAU7K,IACnC0K,GAAaH,CACf,CACF,CAEAO,MAAAA,GACE5vB,KAAKivB,WAAWzL,KAAKxjB,KAAKkvB,QAAQW,SAClC,MAAM3iB,EAAO,IAAI4iB,KAAK9vB,KAAKivB,WAAY,CAAE9rB,KAAM,cAG/C,OAFAnD,KAAKivB,WAAa,GAEX,CACL/kB,GAAI8iB,KAAK+C,MACT7iB,OACA3K,IAAKC,IAAI6K,gBAAgBH,GAE7B,CAEA8iB,gBAAAA,CAAiBC,EAAOC,GACtB,IAAK,IAAItnB,EAAI,EAAGA,EAAIqnB,EAAMljB,OAAQnE,IAAK,CACrC,MAAMunB,EAAItK,KAAKuK,KAAK,EAAGvK,KAAKwK,IAAI,EAAGJ,EAAMrnB,KACzCsnB,EAAOtnB,GAAKunB,EAAI,EAAQ,MAAJA,EAAiB,MAAJA,CACnC,CACF,CAEAZ,cAAAA,CAAeH,GACb,MAAM3tB,EAAO,IAAI6uB,aAAalB,GACxBmB,EAAM,IAAIC,WAAWpB,EAAYriB,QAEvC,OADA/M,KAAKgwB,iBAAiBvuB,EAAM8uB,GACrBA,CACT,GC1DF,SACE1B,WAAAA,CAAY4B,EAAU,CAAC,GACrBzwB,KAAK0wB,gBAAkBD,EAAQC,gBAC/B1wB,KAAK2wB,eAAiBF,EAAQE,eAC9B3wB,KAAK4wB,eAAiBH,EAAQG,eAC9B5wB,KAAK6wB,UAAYJ,EAAQI,UAEzB7wB,KAAK8wB,eAAiB,CACpB/B,QAAS0B,EAAQ1B,QACjBC,WAAYyB,EAAQzB,YAGtBhvB,KAAK+wB,WAAa,KAClB/wB,KAAKgxB,QAAU,GAEfhxB,KAAKixB,SAAU,EACfjxB,KAAK0R,aAAc,EAEnB1R,KAAKssB,SAAW,EAChBtsB,KAAKkxB,OAAS,EAEdlxB,KAAKmxB,UAAY,CACnB,CAEA7O,KAAAA,GACE,MAAM8O,EAAc,CAClBC,OAAO,EACPC,MAAO,CACLC,aAAc,EACdC,kBAAkB,IAItBxxB,KAAK0wB,iBAAmB1wB,KAAK0wB,gBAAgB,mBAE7Ce,UAAUC,aAAaC,aAAaP,GAAanU,KAAKjd,KAAK4xB,aAAaC,KAAK7xB,OAAOod,MAAMpd,KAAK8xB,UAAUD,KAAK7xB,OAE9GA,KAAKixB,SAAU,EACfjxB,KAAK0R,aAAc,EAEd1R,KAAK+xB,cACR/xB,KAAK+xB,YAAc,IAAInD,GAAW5uB,KAAK8wB,gBAE3C,CAEAkB,IAAAA,GACEhyB,KAAKiyB,OAAOC,YAAYjP,SAAQkP,GAASA,EAAMH,SAC/ChyB,KAAKiwB,MAAMmC,aACXpyB,KAAKqyB,UAAUD,aACfpyB,KAAK2e,QAAQ7U,QAEb,IAAIwoB,EAAS,KAEbA,EAAStyB,KAAK+xB,YAAYnC,SAE1B0C,EAAOhG,SAAWtsB,KAAKssB,SACvBtsB,KAAKgxB,QAAQxN,KAAK8O,GAElBtyB,KAAKmxB,UAAY,EACjBnxB,KAAKssB,SAAW,EAEhBtsB,KAAKixB,SAAU,EACfjxB,KAAK0R,aAAc,EAEnB1R,KAAK4wB,gBAAkB5wB,KAAK4wB,eAAe0B,EAC7C,CAEApF,KAAAA,GACEltB,KAAKiyB,OAAOC,YAAYjP,SAAQkP,GAASA,EAAMH,SAC/ChyB,KAAKiwB,MAAMmC,aACXpyB,KAAKqyB,UAAUD,aAEfpyB,KAAKmxB,UAAYnxB,KAAKssB,SACtBtsB,KAAKixB,SAAU,EAEfjxB,KAAK2wB,gBAAkB3wB,KAAK2wB,eAAe,kBAC7C,CAEAiB,YAAAA,CAAaK,GACXjyB,KAAK2e,QAAU,IAAKvc,OAAOmwB,cAAgBnwB,OAAOowB,oBAClDxyB,KAAKssB,SAAWtsB,KAAKmxB,UACrBnxB,KAAKiwB,MAAQjwB,KAAK2e,QAAQ8T,wBAAwBR,GAClDjyB,KAAKqyB,UAAYryB,KAAK2e,QAAQ+T,sBAAsB1yB,KAAK+wB,WAAY,EAAG,GACxE/wB,KAAKiyB,OAASA,EAEdjyB,KAAKqyB,UAAUM,eAAiBzd,IAC9B,MAAM0d,EAAS1d,EAAG2d,YAAYC,eAAe,GAC7C,IAAIC,EAAM,EAEN/yB,KAAK+xB,aACP/xB,KAAK+xB,YAAY5C,OAAOyD,GAG1B,IAAK,IAAIhqB,EAAI,EAAGA,EAAIgqB,EAAO7lB,SAAUnE,EACnCmqB,GAAOH,EAAOhqB,GAAKgqB,EAAOhqB,GAG5B5I,KAAKssB,SAAW0G,WAAWhzB,KAAKmxB,WAAa6B,WAAWhzB,KAAK2e,QAAQyO,YAAY6F,QAAQ,IACzFjzB,KAAKkxB,OAASrL,KAAKqN,KAAKH,EAAMH,EAAO7lB,QAAQkmB,QAAQ,EAAE,EAGzDjzB,KAAKiwB,MAAMkD,QAAQnzB,KAAKqyB,WACxBryB,KAAKqyB,UAAUc,QAAQnzB,KAAK2e,QAAQyU,YACtC,CAEAtB,SAAAA,CAAU9lB,GACRhM,KAAK6wB,WAAa7wB,KAAK6wB,UAAU7kB,EACnC,GC6GF,MAAM,aAANqnB,GAAA,UAAAC,IAAArT,EAAA,KAEA7B,GAAAA,CAAAI,EAAA+U,KACA,IAAAC,EACA,kBACA,MAAA7U,EAAA,KACAD,EAAA1Q,UACA4Q,aAAA4U,GACAA,EAAAlf,YAAA,IAAAkK,EAAAzQ,MAAA4Q,EAAAD,IAAA6U,EACA,GAGA,QACA9yB,KAAA,OACAO,WAAA,CACAyyB,gBAAA,KACAhZ,OAAA,GACApO,QAAA,EACAqnB,qBAAA,GACAC,WAAA,GACAC,UAAA,GACAC,iBAAA,GACAC,WAAA,GACAC,QAAA,GACAC,YAAA,GACAC,KAAAA,IAGAxmB,WAAA,CACA+P,aAAAC,KAAAA,WAGA3S,MAAA,CACAjH,cAAA,CAAAV,KAAA,CAAA4H,OAAA+P,QAAAzM,UAAA,GACAe,aAAA,CAAAjM,KAAAiL,OAAAC,UAAA,GACA7G,WAAA,CAAArE,KAAAmJ,QAAA+B,UAAA,GACA5G,cAAA,CAAAtE,KAAAmJ,QAAA+B,UAAA,GACAY,SAAA,CAAA9L,KAAAmJ,QAAA+B,UAAA,GACAvK,MAAA,CAAAX,KAAAqJ,MAAA6B,UAAA,GACA5H,OAAA,CAAAtD,KAAA,CAAA4H,OAAA+P,QAAAzM,UAAA,GACA3H,cAAA,CAAAvD,KAAAmJ,QAAA+B,UAAA,GACA/J,SAAA,CAAAnB,KAAAqJ,MAAA6B,UAAA,GACA1H,YAAA,CAAAxD,KAAA4H,OAAAC,QAAA,MACAzG,eAAA,CAAApB,KAAAmJ,QAAA+B,UAAA,GACAzH,YAAA,CAAAzD,KAAAqJ,MAAA6B,UAAA,GACAxH,eAAA,CAAA1D,KAAAqJ,MAAA6B,UAAA,GACAvH,aAAA,CAAA3D,KAAAmJ,QAAA+B,UAAA,GACA1I,aAAA,CAAAxC,KAAAmJ,QAAAtB,SAAA,GACAjE,UAAA,CAAA5D,KAAAmJ,QAAA+B,UAAA,GACArH,UAAA,CAAA7D,KAAAmJ,QAAA+B,UAAA,GACApH,aAAA,CAAA9D,KAAA2X,OAAAzM,UAAA,GACAnH,gBAAA,CAAA/D,KAAA2X,OAAAzM,UAAA,GACAlH,WAAA,CAAAhE,KAAAmJ,QAAA+B,UAAA,GACAjH,mBAAA,CAAAjE,KAAAmJ,QAAA+B,UAAA,GACAhH,uBAAA,CAAAlE,KAAAmJ,QAAA+B,UAAA,GACA/G,WAAA,CAAAnE,KAAAmJ,QAAA+B,UAAA,GACAxG,cAAA,CAAA1E,KAAA4H,OAAAsD,UAAA,GACA3G,eAAA,CAAAvE,KAAAmJ,QAAA+B,UAAA,GACA1G,YAAA,CAAAxE,KAAAiL,OAAAC,UAAA,GACA7I,aAAA,CAAArC,KAAAmJ,QAAA+B,UAAA,GACA3I,iBAAA,CAAAvC,KAAAmJ,QAAA+B,UAAA,GACAzG,sBAAA,CAAAzE,KAAAmJ,QAAA+B,UAAA,GACAvG,cAAA,CAAA3E,KAAAqJ,MAAAxB,QAAA,MACA/E,kBAAA,CAAA9C,KAAAoJ,SAAAvB,QAAAA,KAAA,KACAjG,UAAA,CAAA5B,KAAAqJ,MAAA6B,UAAA,GACA5I,aAAA,CAAAtC,KAAAiL,OAAAC,UAAA,IAGA5B,MAAA,CACA,oBACA,eACA,eACA,iBACA,yBACA,iBACA,wBACA,iBACA,YACA,0BACA,mBACA,qBACA,sBACA,kBACA,uBAGAhL,IAAAA,GACA,OACAuN,eAAA,EACAqE,SAAA6gB,GACA9gB,iBAAA+gB,GACAvX,QAAA,GACAzM,cAAA,GACAoB,aAAA,KACA6iB,cAAA,KACA1kB,iBAAA,EACA2kB,qBAAA,EACA5iB,MAAA,GACA6iB,YAAA,EACAtiB,aAAA,EACAxB,aAAA,EACAO,YAAA,EACAE,oBAAA,EACAV,YAAA,GACAgkB,kBAAA,EACApjB,eAAA,GACAqjB,iBAAA,GACAC,iBAAA,GACAC,sBAAA,GACAtjB,gBAAA,KACAujB,mBAAA,KACAC,wBAAA,KACAvjB,qBAAA,KACAwjB,uBAAA,KACAC,4BAAA,KACAC,uBAAA,KACAC,oBAAA,KACAC,SAAA,IAAAC,GAAAA,EACAC,SAAA,KAAAC,eACA1jB,aAAA,EACA2jB,OAAA,MACAvjB,iBAAA,EACArB,eAAA,GAGA,EAEAxP,SAAA,CACAuF,IAAAA,GACA,YAAA1C,MAAAkX,MAAAxU,GAAAA,EAAAC,SAAA,KAAAA,UAAA,EACA,EACAkJ,cAAAA,GACA,OACA,KAAAnJ,KAAAC,SACA,KAAAnC,SAAAyI,SACA,KAAA2C,kBACA,KAAAlK,YAEA,EACA2J,UAAAA,GACA,MAAAmmB,GACA,KAAAxxB,MAAAiJ,SAAA,KAAAvH,eACA,KAAAgB,KAAAC,SAAA,KAAAC,cAKA,OAHA4uB,IACA,KAAA5lB,iBAAA,GAEA4lB,CACA,EACAzlB,mBAAAA,GACA,YAAAvL,SAAAyI,QAAA,KAAAxI,cACA,EACA4O,cAAAA,GACA,YAAA1B,MAAA1E,SAAA,KAAA6P,QAAAZ,MACA,EACApK,YAAAA,GACA,WAAAob,KAAA,SAAAmI,SAAA7I,UAAAW,cAAA5J,OAAA,KACA,EACAnS,YAAAA,GACA,QACA,KAAAC,eAAApE,UACA,KAAAynB,iBAAAznB,UACA,KAAA2nB,sBAAA3nB,UACA,KAAA0E,MAAA1E,UACA,KAAAwE,YAEA,GAGA1P,MAAA,CACA+a,OAAAA,CAAAvI,GACA,KAAAkhB,iBAAA5nB,MAAA0G,CACA,EACA3E,eAAAA,CAAA2E,GACAA,EACA,KAAA+f,cAAA,MAEA,KAAAA,eAAA,KAAAA,cAAAoB,SACA,KAAAC,eAAA,GAEA,EACAjvB,KAAA,CACAiV,WAAA,EACAJ,OAAAA,CAAAC,EAAAoa,IACApa,EAAA7U,QAAAivB,GAAApa,EAAA7U,SAAAivB,EAAAjvB,QACA,KAAAkvB,eAEA,GAEAhvB,YAAA,CACA8U,WAAA,EACAJ,OAAAA,CAAAhH,GACAA,IAAA,KAAAuI,QAAA,KAAAjW,YACA,GAEArC,SAAA,CACAoX,MAAA,EACAL,OAAAA,CAAAC,EAAAoa,GACA,KAAAnlB,YAAA,GACA+K,EAAA2H,SAAA,CAAArG,EAAAhU,KACA,KAAAvB,wBAAAuV,EAAAgZ,MAAAhZ,EAAAoK,QACA,KAAAzW,YAAAiT,KAAA,CACA9Z,IAAAkT,EAAAlT,IACAO,MAAArB,GAEA,IAEA8sB,GAAA3oB,SAAAuO,GAAAvO,OAAA,IACA,KAAAwD,YAAA,IAEA,KAAA6jB,eACA,KAAAA,cAAAoB,SAEA,KAAAK,0BACAvhB,YAAA,SAAA+f,qBAAA,GACA,GAEA9vB,cAAAA,CAAA8P,GACAA,IAAA,KAAA3E,iBAAA,GACA,KAAA0kB,eAAA,KAAAA,cAAA0B,UACA,GAGAvyB,OAAAA,GACA,KAAAgN,YAAA,GACA,MAAAtB,EAAAokB,KAEA,KAAAkC,iBAAA9gB,iBACA,QACA2J,IAAAlb,IACA,UAAAA,EAAA2F,KAAA3F,EAAA2P,UAAA,KAAAyhB,aACArlB,GACA,KAAA2N,QAAA,KAAAA,QAAA,KACAtI,YAAA,SAAA/B,mBAEA,KAAApB,eAAApE,QACA,KAAAynB,iBAAAznB,QACA,KAAA2nB,sBAAA3nB,QAEA,KAAAjH,eAIAwO,YAAA,KACA,KAAAyhB,iBAAA,KACA,KAAAA,iBAAA,KACA,KAAAA,iBAAA,OACA,OAEA,IAGA,KAAAR,iBAAA9gB,iBAAA,cACAxF,IAAA,KAAAslB,kBAAA,GAEA,KAAAwB,iBAAA,KACA,KAAAA,iBAAA,KACA,KAAAA,iBAAA,QAGA,KAAAR,iBAAA9gB,iBAAA,aACA,KAAAuhB,kBACA/mB,GAAAqF,YAAA,SAAAigB,kBAAA,OAEA,KAAAsB,yBACA,EAEAI,aAAAA,GACA,KAAAtkB,cACA,EAEA5P,QAAA,CAEAwG,kBAAAA,CAAAiX,GACA,KAAAlX,MAAA,mBAAAkX,EACA,EAEAqW,uBAAAA,GACA,IAAArpB,MAAAoY,QAAA,KAAAtgB,UAAA,YAEA,MAAA4xB,EAAA,SAAA5xB,UAAA6xB,UACAC,EAAAF,EAAAlb,MAAA4B,GAAA,IAAAA,EAAAoK,SAEA+I,EAAA,IAAA/C,KAEA,GAAAoJ,EAAA,CACA,MAAAtpB,EAAAspB,EAAAC,KACAC,EAAA,IAAAtJ,KAAA,IAAAlgB,GACAypB,GAAAxG,EAAAuG,GAAA,KACA,KAAAtnB,cAAAunB,EAAA,EACA,MACA,KAAAvnB,eAAA,EAEA,KAAAA,eAAA,KAAAqB,MAAAmmB,cACA,KAAAnmB,MAAAmmB,aAAAC,MAEA,EAEAnnB,mBAAAA,CAAAonB,GACA,KAAAjmB,eAAAimB,CACA,EACAnB,cAAAA,GACA,YAAAllB,MAAAmmB,YACA,EACAtnB,UAAAA,CAAAynB,GACA,SAAAnvB,YAEA,IAAAmvB,EAAAC,eAAA7pB,OAAA,CACA,MAAA8pB,EAAAF,EAAAC,eAAA,GAAAxK,QACA0K,EAAAH,EAAAC,eAAA,GAAAxhB,QAEAX,iBACA,YACAkiB,GAAA,KAAAI,SAAAJ,EAAAE,EAAAC,IACA,CAAAE,MAAA,GAEA,CACA,EACAD,QAAAA,CAAAJ,EAAAE,EAAAC,GACA,OAAAH,EAAAC,eAAA7pB,OAAA,CACA,MAAAkqB,EAAAN,EAAAC,eAAA,GAAAxK,QACA8K,EAAAP,EAAAC,eAAA,GAAAxhB,QAEA+hB,EAAAF,EAAAJ,EAAA,IACAO,EAAAvR,KAAAwR,IAAAH,EAAAJ,GAAA,GAEAK,IAAAC,GACA,KAAA9uB,MAAA,oBAEA,CACA,EACAqtB,aAAAA,GACA,KAAAjmB,iBAAA,EACA,KAAAqB,YAAA,EACA,KAAAE,oBAAA,EACA,KAAAO,cAAA,MAEA,KAAA7K,cACA,KAAAiW,QAAA,KAAAjW,YACA2N,YAAA,SAAA/B,oBAGA,KAAAjO,SAAAyI,QAAA,KAAAxI,iBACA,KAAAmL,iBAAA,GAGA,MAAA4nB,EAAA,KAAAC,QACA,SAAAjzB,WACA+P,IACA,IAAAA,IAAAA,EAAAtH,OAAA,OAEA,MAAAif,EAAA,KAAA3b,MAAAmnB,gBACAxL,IAEAsL,IAEAhjB,YAAA,KACA0X,EAAAyL,SAAA,CAAA7jB,IAAAoY,EAAA0L,eACA,KAAAhoB,iBAAA,KACA,GAGA,EACAiB,cAAAA,EAAA,QAAAiM,EAAA,MAAA3S,EAAA,IAAAuF,IACA,GAAAvF,IAAA,KAAA3F,SAAAyI,OAAA,SACA,MAAA4qB,EAAAnoB,EAAAooB,aAAA,GAEAtjB,YAAA,KAEA,KAAAujB,gBAAA,KAAAxnB,MAAAmnB,iBAAAG,GAIA,IAAA/a,EAAAoK,OAFA,KAAAhW,kBAKA,KAAAD,YAAA,EACA,KAAAE,sBAEA,GAEA,EACAxB,iBAAAA,CAAAvM,GAGA,GAFA,KAAAsN,aAAA,GAEAtN,EAAAsL,OAAA,OAEA,MAAAspB,EAAA,KAAAD,gBAAA30B,EAAAsL,QACAspB,EAAA,UAAA7mB,oBAAA,GACA,KAAAF,WAAA+mB,EAAA,UAAA7mB,mBACA,EACA8kB,gBAAAA,CAAAgC,GACA,SAAAxC,iBAAA,OAEA,GACA,MAAAwC,KACA,KAAAvxB,KAAA4J,OAAA,KAAA5J,KAAA4J,MAAArD,QAAA,GAEA,OAGA,SAAAgrB,IAAA,KAAAjwB,cACA,OAGA,GACA,KAAAitB,yBAAA,KAAAQ,iBAAAyC,eAEA,OAGA,KAAAjD,uBAAA,KAAAQ,iBAAAyC,eAEA,IAAA3lB,EAAA,KAAA0iB,uBAEA,MACA1iB,EAAA,GACA,KAAAuK,QAAAqb,OAAA5lB,EAAA,KAAA0lB,GACA,WAAAnb,QAAAqb,OAAA5lB,EAAA,GAEAA,IAGA,MAAA6lB,EAAA,KAAAtb,QAAAqb,OAAA5lB,EAAA,GACA8lB,GAAAD,EAAAxU,MAAA,kBAEA,GACA,KAAA9G,QAAAqb,OAAA5lB,EAAA,KAAA0lB,GACAG,GAAA,MAAAA,IAAAC,EAcA,KAAAnC,gBAAA+B,OAbA,CACA,MAAAK,EAAA,KAAAxb,QAAAuI,UACA9S,EACA,KAAA0iB,wBAEA,MAAAgD,EACA,KAAAM,aAAAD,GACA,MAAAL,EACA,KAAAO,mBAAAF,GACA,MAAAL,GACA,KAAAQ,wBAAAH,EAEA,CAGA,EACAI,eAAAA,CAAAT,GACA,MAAAU,EAAA,KAAAlD,iBAAAyC,eAEA,IAAA3lB,EAAAomB,EACA,MAAApmB,EAAA,QAAAuK,QAAAqb,OAAA5lB,EAAA,KAAA0lB,EACA1lB,IAGA,IAAAqmB,EAAArmB,EACA,MACA,KAAAuK,QAAAqb,OAAAS,IACA,KAAA9b,QAAAqb,OAAAS,GAAA1c,OAEA0c,IAGA,OAAArmB,WAAAqmB,cACA,EACA,kBAAAL,CAAAD,GACA,IAAAA,EAAA,OAEA,MAAAO,QAAA,KAAA1D,SAAA2D,sBAAAR,GACA,KAAAjnB,eAAAwnB,EAAAhgB,KAAAjN,GAAAA,EAAAiJ,SACA,EACArD,WAAAA,CAAA5F,GAGA,GAFA,KAAA0F,iBAAA,GAEA1F,EAAA,OAEA,eAAA2G,EAAA,YAAAqmB,GAAA,KAAAF,gBAAA,KAEA,KAAA5b,QACA,KAAAA,QAAAyG,OAAA,EAAAhR,EAAA,GACA3G,EACA,KAAAkR,QAAAyG,OAAAqV,EAAA,KAAA9b,QAAA7P,OAAA,GAEA,KAAAioB,oBAAA3iB,EACA,KAAAojB,eACA,EACA6C,kBAAAA,CAAAF,GACA,KAAA5D,iBAAAqE,GACA,KAAAryB,KAAA4J,MACA,WACAgoB,GACA,GACA5f,QAAAtW,GAAAA,EAAAwH,MAAA,KAAA7F,eACA,EACAi1B,aAAAA,CAAA52B,EAAA62B,GAAA,GAGA,GAFA,KAAApE,oBAAA,GAEAzyB,EAAA,OAEA,eAAAmQ,EAAA,YAAAqmB,GAAA,KAAAF,gBAAA,KAEAQ,EAAA,KAAApc,QAAAyG,OAAAqV,EAAAA,GAAA3rB,OACA,GACA,IAEA,KAAA6P,QACA,KAAAA,QAAAyG,OAAA,EAAAhR,GACAnQ,EAAAyK,SACAqsB,EACA,KAAApc,QAAAyG,OAAAqV,EAAA,KAAA9b,QAAA7P,OAAA,GAEA,KAAA0nB,iBAAA,SAAAA,iBAAA,IAAAvyB,IAEA62B,IACA,KAAA/D,oBACA3iB,EAAAnQ,EAAAyK,SAAAI,OAAAisB,EAAAjsB,OAAA,GAGA,KAAA0oB,eACA,EACA8C,uBAAAA,CAAAH,GACA,KAAA1D,sBAAAmE,GACA,KAAA/wB,cACA,MACAswB,GACA,EAEA,EACAa,kBAAAA,CAAAjhB,GAGA,GAFA,KAAA4c,yBAAA,GAEA5c,EAAA,OAEA,eAAA3F,EAAA,YAAAqmB,GAAA,KAAAF,gBAAA,KAEAQ,EAAA,KAAApc,QAAAyG,OAAAqV,EAAAA,GAAA3rB,OACA,GACA,IAEA,KAAA6P,QACA,KAAAA,QAAAyG,OAAA,EAAAhR,EAAA,GACA2F,EAAAuJ,KACAyX,EACA,KAAApc,QAAAyG,OAAAqV,EAAA,KAAA9b,QAAA7P,OAAA,GAEA,KAAAioB,oBACA3iB,EAAA2F,EAAAuJ,KAAAxU,OAAAisB,EAAAjsB,OAAA,EAEA,KAAA0oB,eACA,EACAxiB,oBAAAA,CAAA0I,EAAAud,GACA,KAAA/nB,eAAApE,QACA,KAAAsE,qBAAA6nB,EACAvd,EAAA7N,kBACA,KAAA0mB,iBAAAznB,QACA,KAAA8nB,uBAAAqE,EACAvd,EAAA7N,kBACA,KAAA4mB,sBAAA3nB,SACA,KAAA+nB,4BAAAoE,EACAvd,EAAA7N,iBAEA,EACAkF,UAAAA,GACA,KAAA7B,eAAApE,OACA,KAAAqE,iBAAA,EACA,KAAAojB,iBAAAznB,OACA,KAAA4nB,oBAAA,EACA,KAAAD,sBAAA3nB,SACA,KAAA6nB,yBAAA,EAEA,EACAoB,eAAAA,CAAA+B,EAAA,MACA,MAAAA,EACA,KAAA5mB,eAAA,GACA,MAAA4mB,EACA,KAAAvD,iBAAA,IACA,MAAAuD,IAGA,KAAA5mB,eAAA,GACA,KAAAqjB,iBAAA,IAHA,KAAAE,sBAAA,IAOA,KAAAK,uBAAA,IACA,EACApiB,cAAAA,GACA,KAAAxB,eAAApE,OAAA,KAAAoE,eAAA,GACA,KAAAqjB,iBAAAznB,OAAA,KAAAynB,iBAAA,GACA,KAAAE,sBAAA3nB,OACA,KAAA2nB,sBAAA,GACA,KAAAljB,cACA,EACAA,YAAAA,CAAA2nB,GAAA,EAAAC,GAAA,GACAA,GACA,KAAA9wB,MAAA,uBAGA,KAAAmsB,iBAAA,GACA,KAAAuB,kBACA,KAAAqD,oBACA,KAAAzc,QAAA,GACA,KAAAzM,cAAA,GACA,KAAAoB,aAAA,KACA,KAAAE,MAAA,GACA,KAAAO,aAAA,EACA,KAAAsnB,6BACAhlB,YAAA,SAAAmhB,cAAA0D,IACA,EACAE,iBAAAA,GACA,KAAA9D,mBACA,KAAAA,iBAAAlvB,MAAAqN,OAAA,OAEA,EACA+hB,aAAAA,CAAA0D,GACA9F,MAAA8F,GACA,KAAA5D,mBACA,KAAAA,iBAAAgE,QAEA,KAAAvE,qBACA1gB,YAAA,KACA,KAAAihB,iBAAAiE,kBACA,KAAAxE,oBACA,KAAAA,qBAEA,KAAAA,oBAAA,QAGA,EACAsE,0BAAAA,GACA,KAAA/E,kBAAA,KAAAgB,iBAAAgE,OACA,EACAzzB,WAAAA,GACA,IAAA8W,EAAA,KAAAA,QAAAZ,OAEA,SAAAvK,MAAA1E,SAAA6P,EAAA,OAEA,KAAA6X,iBAAAxR,SAAA/gB,IACA0a,EAAAA,EAAAoF,QACA,IAAA9f,EAAAyK,WACA,YAAAzK,EAAAwH,gBACA,IAGA,MAAA+H,EAAA,KAAAA,MAAA1E,OAAA,KAAA0E,MAAA,KAEA,KAAAtB,cAAAzG,KAEA,KAAAyG,cAAAhC,UAAAyO,GACA,KAAAzM,cAAAsB,OAAA1E,QACA,KAAA0E,MAAA1E,SAEA,KAAAzE,MAAA,gBACA0lB,UAAA,KAAA7d,cAAAzG,IACA+vB,WAAA7c,EACAnL,QACA6V,aAAA,KAAA/V,aACAmoB,SAAA,KAAAjF,mBAIA,KAAAnsB,MAAA,gBACA6F,QAAAyO,EACAnL,QACA6V,aAAA,KAAA/V,aACAmoB,SAAA,KAAAjF,mBAIA,KAAAjjB,cAAA,EACA,EACAzB,gBAAAA,CAAAqkB,GACA,KAAA1kB,gBACA,KAAA0kB,cAAAA,EAIA9f,YACA,KACA,SAAA+f,oBAAA,CAEA,QAAA9vB,iBAAA,KAAAiC,KAAAC,OACA,OAAA2tB,EAAA0B,WAGA,KAAA1B,cAAAA,EACA,KAAA9rB,MAAA,kBACA,KAAA+rB,qBAAA,CARA,CAQA,GAGAf,KAAA,MAEA,EACA1iB,oBAAAA,EAAA,OAAA6Y,EAAA,QAAA7M,IACA,OAAA6M,EAAAhpB,MACA,mBAEA,YAAA6mB,aAAA1K,GACA,kBACA,YAAA7U,YAAA6U,GACA,oBACA,YAAAtU,MAAA,iBAAAsU,GACA,QACA,YAAAtU,MAAA,0BAAAmhB,SAAA7M,YAEA,EACA3U,mBAAAA,CAAA0xB,GACA,KAAArxB,MAAA,wBAAAqxB,EACA,EACArS,YAAAA,CAAA1K,GACA,KAAAzM,cAAA,GACA,KAAAoB,aAAAqL,EACA,KAAA6Y,eACA,EACA1tB,WAAAA,CAAA6U,GACA,KAAApL,eAEA,KAAArB,cAAA,IAAAyM,GAEA,IAAAgd,EAAAhd,EAAAzO,QACA,MAAAgY,EAAAyT,EAEA5T,EAAA,YACAC,EAAA,aAEAC,EAAA,IACA0T,EAAA1X,SAAA,IAAAyB,OAAAqC,EAAA,QACArN,KAAApK,GAAAA,EAAAtE,QAEAic,EAAAjD,SAAAhZ,IACA,MAAA+M,EAAAmP,EAAAhB,UACAlb,EAAA+b,EAAAjZ,OACAoZ,EAAA3T,QAAAyT,EAAAhc,IAGA/H,EAAA,KAAAsE,KAAA4J,MAAA4K,MAAA9Y,GAAAA,EAAAwH,MAAAsN,IAEA4iB,EAAAA,EAAA5X,QACA,GAAAgE,IAAAhP,IAAAiP,IACA,IAAA/jB,GAAAyK,UAAA,aAGA,KAAAmsB,cAAA52B,GAAA,MAGA,KAAA0a,QAAAgd,EAEAhd,EAAAnL,QACA,KAAAA,MAAA,IAAAmL,EAAAnL,QAGA6C,YAAA,SAAAulB,kBACA,EACAhC,eAAAA,CAAA7L,GACA,mBAAA0L,EAAA,aAAA5X,EAAA,UAAAga,GAAA9N,EACA,OAAA0L,EAAA5X,EAAAga,CACA,EACA9oB,cAAAA,GACAsD,YAAA,KACA,MAAA0X,EAAA,KAAA3b,MAAAmnB,gBACAxL,EAAA+N,UAAA5uB,IAAA,qBACA6gB,EAAAyL,SAAA,CAAA7jB,IAAAoY,EAAA0L,aAAAsC,SAAA,WACA1lB,YAAA,IAAA0X,EAAA+N,UAAA9L,OAAA,yBACA,GACA,EACA1b,cAAA6L,IAAA,SAAAlb,GACA,KAAA0Z,QAAA1Z,GAAAsL,QAAAb,MACA,KAAA4mB,kBAAA,EACA,KAAAsF,iBACA,KAAAvxB,MAAA,sBAAAsU,QACA,QACAid,cAAAA,GACA,MAAAI,EAAA,KAAA1E,iBAEA,IAAA0E,EAAA,OAEA,MAAAC,EAAA93B,OACA+3B,iBAAAF,EAAA,MACAG,iBAAA,eACApY,QAAA,SACAiY,EAAA5zB,MAAAqN,OAAA,EACAumB,EAAA5zB,MAAAqN,OAAAumB,EAAAvC,aAAA,EAAAwC,EAAA,IACA,EACAjoB,QAAAA,CAAAvG,GACA,KAAAkR,SAAAlR,EAAAiJ,QACA,KAAA8gB,eAAA,EACA,EACAvjB,gBAAAA,GACA,KAAA7B,MAAA9E,KAAAoC,MAAA,GACA,KAAA2F,mBACA,EACAJ,YAAAA,CAAAmnB,GACA,MAAAnM,EAAA,IAAAmM,EAAAC,eAAApM,OAEAA,GACAA,EAAAjL,SAAA7W,IACA,GAAAA,EAAAjJ,KAAAuZ,SAAA,UACA,MAAAxP,EAAAd,EAAAmuB,YACA,KAAAnoB,aAAA,CAAAlF,GACA,IAGA,EACA,kBAAAkF,CAAAX,GACA,KAAA6iB,YAAA,EACA,KAAAmB,gBAEA,MAAA+E,EAAA,KAAA/oB,MAAAkH,KAAAshB,GAAAA,EAAAx5B,OAEAg6B,EAAAjuB,MAAAkuB,KAAAjpB,GAAA+G,QAAAjN,GAAAA,EAAApI,OAEAw3B,EAAA,QAAAC,IAAAH,IACAE,EAAA1X,SAAA,MAAA1X,EAAA3C,KACA,IACA,MAAAiyB,EAAAr4B,IAAA6K,gBAAA9B,GACAuvB,QAAA7tB,MAAA4tB,GAAA5d,MAAA4Q,GACAA,EAAA3gB,SAEA6tB,EAAAxvB,EAAA9K,KAAAwkB,YAAA,KACA+V,EAAAzvB,EAAA9K,KAAA0kB,UAAA,EAAA4V,GACAP,EAAA9d,SAAAse,IACA,KAAAvpB,MAAA+R,KAAA,CACAtW,KAAA4tB,EACAr6B,KAAAu6B,EACAvwB,KAAAc,EAAAd,KACAuL,UAAAzK,EAAA9K,KAAA0kB,UAAA4V,EAAA,GACAlS,SAAAgS,EACAt4B,IAAAs4B,EACAI,QAAA1vB,EACAuM,SAAA,EACA9L,OAAA,IAGApD,IAAA+xB,EAAA5tB,OAAA,GACAuH,YAAA,KACA,KAAAggB,YAAA,EACA,KAAAxiB,iBAAA,IACA,IAEA,OAAAuL,GAEA/I,YAAA,KACA,KAAAggB,YAAA,EACA,KAAAxiB,iBAAA,IACA,IACA,IAEA,EACAC,UAAAA,CAAA9H,GACA,KAAAwH,MAAAypB,OAAAjxB,EAAA,GACA,KAAAwrB,eACA,EACAL,YAAAA,GAGA,OAFA,KAAA1jB,aAAA,EAEA,IAAAypB,GAAA,CACApM,QAAA,KAAA9nB,aACA+nB,WAAA,KAAA9nB,gBACAwpB,gBAAA,KACAE,eAAA,KACAD,eAAA,KACAE,UAAA,KAAAA,WAEA,EACAA,SAAAA,GACA,KAAAnf,aAAA,EACA,KAAAyjB,SAAA,KAAAC,cACA,EACAvjB,cAAAA,CAAAupB,GAGA,GAFA,KAAA1pB,YAAA0pB,EAEA,KAAAjG,SAAAzjB,YAGA,IACA,KAAAyjB,SAAAnD,OAEA,MAAAM,EAAA,KAAA6C,SAAAnE,QAAA,GAEA,KAAAvf,MAAA+R,KAAA,CACAtW,KAAAolB,EAAAplB,KACAzM,KAAA,cAAA40B,SACA5qB,KAAA6nB,EAAAplB,KAAAzC,KACA6hB,SAAAgG,EAAAhG,SACAnpB,KAAAmvB,EAAAplB,KAAA/J,KACAmuB,OAAA,EACAzI,SAAArmB,IAAA6K,gBAAAilB,EAAAplB,QAGA,KAAAioB,SAAA,KAAAC,eACA,KAAAtvB,aACA,OACAwO,YAAA,SAAA3C,gBAAA,IACA,MArBA2C,YAAA,SAAA6gB,SAAA7S,SAAA,IAuBA,EACA3Q,YAAAA,GACA,QAAAwjB,SAAAzjB,YACA,IACA,KAAAyjB,SAAAnD,OACA,KAAAmD,SAAA,KAAAC,cACA,OACA9gB,YAAA,SAAA3C,gBAAA,IACA,CAEA,EACA3L,QAAAA,EAAA,QAAA4W,EAAA,KAAArR,IACA,KAAAjD,MAAA,aAAAsU,UAAArR,QACA,EACApD,qBAAAA,GACA,KAAAG,MAAA,+BAAAsU,QACA,EACArN,WAAAA,CAAAjB,GACA,KAAAsO,QAAAtO,EACAgG,YAAA,SAAAulB,mBACA,KAAApE,eAAA,EACA,EACAniB,iBAAAA,GACA,KAAAxB,iBAAA,KAAAA,eACA,EACAyB,YAAAA,CAAAlL,GACAA,EAAAgzB,aACA,KAAAjpB,aAAA/J,EAAAgzB,aAAA5pB,OAEA,KAAApB,MAAA9E,KAAAgC,OAEA,EACAnF,eAAAA,CAAA3G,GACA,KAAA6G,MAAA,mBAAA7G,EACA,EAEA6D,gBAAAA,GACA,KAAAgD,MAAA,qBACA,EACAuI,eAAAA,CAAAvC,GACA,MAAA0d,EAAArgB,SAAAihB,eAAA,GAAAte,EAAAgZ,aAAA5d,aACAsiB,IACAA,EAAA3lB,MAAAi1B,OAAA,sBACAtP,EAAAuP,iBACAjnB,YAAA,IAAA0X,EAAA3lB,MAAAi1B,OAAA,aAEA,ICvoCqQ,MCOjQ,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QClBhC,IACEE,YAAa,WACbnsB,WAAY,mBACZwX,aAAc,eACdU,gBAAiB,2BACjB3X,eAAgB,cAChBE,qBAAsB,2BACtB2rB,aAAc,eACdC,OAAQ,SACRC,UAAW,YACXC,UAAW,aACXljB,UAAW,iBCXN,MAAMmjB,GAAqB,CAChCC,MAAO,CACLC,QAAS,CACPC,MAAO,UACPC,gBAAiB,OACjBC,iBAAkB,UAClBC,WAAY,UACZC,aAAc,OACdC,YAAa,oBACbC,qBAAsB,QAGxBC,UAAW,CACTjB,OAAQ,OACRkB,aAAc,MACdC,UAAW,4EAGbC,OAAQ,CACNC,WAAY,OACZC,cAAe,UACfC,cAAe,WAGjBC,OAAQ,CACNH,WAAY,UACZI,iBAAkB,oBAClBC,oBAAqB,UACrBC,gBAAiB,UACjBC,oBAAqB,UACrBC,cAAe,WAGjBhvB,QAAS,CACPwuB,WAAY,WAGdS,SAAU,CACRT,WAAY,OACZU,gBAAiB,UACjBC,iBAAkB,UAClBC,YAAa,UACbC,kBAAmB,WAGrBvxB,SAAU,CACR0wB,WAAY,OACZU,gBAAiB,WAGnBzgB,QAAS,CACP+f,WAAY,OACZc,aAAc,UACdzB,MAAO,UACP0B,aAAc,UACdC,kBAAmB,UACnBC,aAAc,UACdC,cAAe,UACfC,eAAgB,UAChBC,eAAgB,UAChBC,UAAW,UACXC,iBAAkB,UAClBC,YAAa,UACbC,gBAAiB,sBACjBlB,gBAAiB,sBACjBmB,mBAAoB,UACpBC,WAAY,UACZC,SAAU,UACVC,gBAAiB,OACjBC,iBAAkB,UAClBC,wBAAyB,UACzBC,mBAAoB,OACpBC,mBAAoB,OACpBC,oBAAqB,iBACrBC,wBAAyB,OACzBC,yBAA0B,iBAC1BC,qBAAsB,UACtBC,qBAAsB,UACtBC,sBAAuB,oBACvBC,0BAA2B,UAC3BC,2BAA4B,oBAC5BC,uBAAwB,UACxBC,sBAAuB,UACvBC,oBAAqB,sBACrBC,wBAAyB,UACzBC,gCAAiC,UACjCC,mBAAoB,WAGtBC,SAAU,CACR/C,WAAY,2BACZrB,OAAQ,2BACRU,MAAO,UACP2D,WAAY,WAGdn5B,KAAM,CACJq3B,cAAe,UACf+B,aAAc,UACd9B,eAAgB,UAChB+B,iBAAkB,UAClBC,kBAAmB,UACnBC,uBAAwB,UACxBC,kBAAmB,QAGrBt0B,MAAO,CACLixB,WAAY,QAGdsD,MAAO,CACL/0B,OAAQ,UACRC,IAAK,UACLC,OAAQ,UACRC,KAAM,UACNvB,MAAO,UACPo2B,WAAY,OACZ30B,KAAM,UACNC,UAAW,UACX20B,aAAc,OACd10B,KAAM,UACN20B,aAAc,UACd10B,MAAO,UACP8H,cAAe,qBACf7H,SAAU,UACVC,OAAQ,UACRC,UAAW,UACXw0B,cAAe,UACft0B,IAAK,OACLu0B,gBAAiB,OACjBC,0BAA2B,sBAC3BC,aAAc,UACdC,eAAgB,UAChBt0B,WAAY,UACZu0B,UAAW,UACXC,WAAY,UACZC,YAAa,UACbC,aAAc,YAGlBC,KAAM,CACJ/E,QAAS,CACPC,MAAO,OACPC,gBAAiB,UACjBC,iBAAkB,UAClBC,WAAY,OACZC,aAAc,OACdC,YAAa,OACbC,qBAAsB,QAGxBC,UAAW,CACTjB,OAAQ,OACRkB,aAAc,MACdC,UAAW,4EAGbC,OAAQ,CACNC,WAAY,UACZC,cAAe,OACfC,cAAe,WAGjBC,OAAQ,CACNH,WAAY,UACZI,iBAAkB,OAClBC,oBAAqB,UACrBC,gBAAiB,UACjBC,oBAAqB,UACrBC,cAAe,WAGjBhvB,QAAS,CACPwuB,WAAY,WAGdS,SAAU,CACRT,WAAY,UACZU,gBAAiB,UACjBC,iBAAkB,UAClBC,YAAa,OACbC,kBAAmB,WAGrBvxB,SAAU,CACR0wB,WAAY,UACZU,gBAAiB,WAGnBzgB,QAAS,CACP+f,WAAY,UACZc,aAAc,UACdzB,MAAO,OACP0B,aAAc,UACdC,kBAAmB,UACnBC,aAAc,UACdC,cAAe,UACfC,eAAgB,UAChBC,eAAgB,qBAChBC,UAAW,UACXC,iBAAkB,qBAClBC,YAAa,UACbC,gBAAiB,sBACjBlB,gBAAiB,sBACjBmB,mBAAoB,OACpBC,WAAY,UACZC,SAAU,UACVC,gBAAiB,OACjBC,iBAAkB,OAClBC,wBAAyB,UACzBC,mBAAoB,OACpBC,mBAAoB,OACpBC,oBAAqB,OACrBC,wBAAyB,UACzBC,yBAA0B,OAC1BC,qBAAsB,OACtBC,qBAAsB,UACtBC,sBAAuB,OACvBC,0BAA2B,UAC3BC,2BAA4B,OAC5BC,uBAAwB,OACxBC,sBAAuB,UACvBC,oBAAqB,4BACrBC,wBAAyB,UACzBC,gCAAiC,UACjCC,mBAAoB,WAGtBC,SAAU,CACR/C,WAAY,2BACZrB,OAAQ,2BACRU,MAAO,UACP2D,WAAY,WAGdn5B,KAAM,CACJq3B,cAAe,OACf+B,aAAc,UACd9B,eAAgB,UAChB+B,iBAAkB,UAClBC,kBAAmB,UACnBC,uBAAwB,UACxBC,kBAAmB,QAGrBt0B,MAAO,CACLixB,WAAY,WAGdsD,MAAO,CACL/0B,OAAQ,UACRC,IAAK,OACLC,OAAQ,OACRC,KAAM,OACNvB,MAAO,UACPo2B,WAAY,OACZ30B,KAAM,UACNC,UAAW,OACX20B,aAAc,OACd10B,KAAM,OACN20B,aAAc,UACd10B,MAAO,OACP8H,cAAe,OACf7H,SAAU,UACVC,OAAQ,UACRC,UAAW,UACXw0B,cAAe,UACft0B,IAAK,OACLu0B,gBAAiB,OACjBC,0BAA2B,sBAC3BC,aAAc,OACdC,eAAgB,UAChBt0B,WAAY,OACZu0B,UAAW,UACXC,WAAY,UACZC,YAAa,UACbC,aAAc,aAKPE,GAAeA,EAC1BhF,UACAQ,YACAG,SACAI,SACAM,WACAjvB,UACAlC,WACA2Q,UACA8iB,WACAl5B,OACAkF,QACAu0B,YAEO,CAEL,eAAgBlE,EAAQC,MACxB,wBAAyBD,EAAQE,gBACjC,uBAAwBF,EAAQK,aAChC,2BAA4BL,EAAQG,iBACpC,qBAAsBH,EAAQI,WAC9B,sBAAuBJ,EAAQM,YAC/B,wBAAyBN,EAAQO,qBAGjC,0BAA2BC,EAAUjB,OACrC,iCAAkCiB,EAAUC,aAC5C,8BAA+BD,EAAUE,UAGzC,yBAA0BC,EAAOC,WACjC,2BAA4BD,EAAOE,cACnC,2BAA4BF,EAAOG,cAGnC,yBAA0BC,EAAOH,WACjC,4BAA6BG,EAAOC,iBACpC,qCAAsCD,EAAOE,oBAC7C,+BAAgCF,EAAOG,gBACvC,oCAAqCH,EAAOI,oBAC5C,6BAA8BJ,EAAOK,cAGrC,0BAA2BhvB,EAAQwuB,WAGnC,2BAA4BS,EAAST,WACrC,iCAAkCS,EAASC,gBAC3C,kCAAmCD,EAASE,iBAC5C,+BAAgCF,EAASG,YACzC,sCAAuCH,EAASI,kBAGhD,2BAA4BvxB,EAAS0wB,WACrC,iCAAkC1wB,EAASoxB,gBAG3C,0BAA2BzgB,EAAQ+f,WACnC,6BAA8B/f,EAAQ6gB,aACtC,+BAAgC7gB,EAAQ8gB,aACxC,kCAAmC9gB,EAAQ+gB,kBAC3C,+BAAgC/gB,EAAQghB,aACxC,gCAAiChhB,EAAQihB,cACzC,iCAAkCjhB,EAAQkhB,eAC1C,+BAAgClhB,EAAQmhB,eACxC,4BAA6BnhB,EAAQohB,UACrC,iCAAkCphB,EAAQqhB,iBAC1C,8BAA+BrhB,EAAQshB,YACvC,uBAAwBthB,EAAQof,MAChC,gCAAiCpf,EAAQuhB,gBACzC,gCAAiCvhB,EAAQqgB,gBACzC,sCAAuCrgB,EAAQwhB,mBAC/C,qCAAsCxhB,EAAQyhB,WAC9C,2BAA4BzhB,EAAQ0hB,SACpC,gCAAiC1hB,EAAQ2hB,gBACzC,oCAAqC3hB,EAAQ4hB,iBAC7C,yCAA0C5hB,EAAQ6hB,wBAClD,sCAAuC7hB,EAAQ8hB,mBAC/C,mCAAoC9hB,EAAQ+hB,mBAC5C,uCAAwC/hB,EAAQgiB,oBAChD,yCAA0ChiB,EAAQiiB,wBAClD,6CAA8CjiB,EAAQkiB,yBACtD,wCAAyCliB,EAAQmiB,qBACjD,sCAAuCniB,EAAQoiB,qBAC/C,0CAA2CpiB,EAAQqiB,sBACnD,4CAA6CriB,EAAQsiB,0BACrD,gDAAiDtiB,EAAQuiB,2BACzD,2CAA4CviB,EAAQwiB,uBACpD,uCAAwCxiB,EAAQyiB,sBAChD,qCAAsCziB,EAAQ0iB,oBAC9C,yCAA0C1iB,EAAQ2iB,wBAClD,kDAAmD3iB,EAAQ4iB,gCAC3D,sCAAuC5iB,EAAQ6iB,mBAG/C,qBAAsBC,EAAS/C,WAC/B,yBAA0B+C,EAASpE,OACnC,wBAAyBoE,EAAS1D,MAClC,8BAA+B0D,EAASC,WAGxC,6BAA8Bn5B,EAAKq3B,cACnC,4BAA6Br3B,EAAKo5B,aAClC,8BAA+Bp5B,EAAKs3B,eACpC,2BAA4Bt3B,EAAKq5B,iBACjC,4BAA6Br5B,EAAKs5B,kBAClC,6BAA8Bt5B,EAAKu5B,uBACnC,0BAA2Bv5B,EAAKw5B,kBAGhC,wBAAyBt0B,EAAMixB,WAG/B,2BAA4BsD,EAAM/0B,OAClC,wBAAyB+0B,EAAM90B,IAC/B,2BAA4B80B,EAAM70B,OAClC,yBAA0B60B,EAAM50B,KAChC,0BAA2B40B,EAAMn2B,MACjC,gCAAiCm2B,EAAMC,WACvC,yBAA0BD,EAAM10B,KAChC,8BAA+B00B,EAAMz0B,UACrC,kCAAmCy0B,EAAME,aACzC,yBAA0BF,EAAMx0B,KAChC,kCAAmCw0B,EAAMG,aACzC,0BAA2BH,EAAMv0B,MACjC,mCAAoCu0B,EAAMzsB,cAC1C,6BAA8BysB,EAAMt0B,SACpC,2BAA4Bs0B,EAAMr0B,OAClC,8BAA+Bq0B,EAAMp0B,UACrC,mCAAoCo0B,EAAMI,cAC1C,wBAAyBJ,EAAMl0B,IAC/B,qCAAsCk0B,EAAMK,gBAC5C,kCAAmCL,EAAMM,0BACzC,kCAAmCN,EAAMO,aACzC,oCAAqCP,EAAMQ,eAC3C,+BAAgCR,EAAM9zB,WACtC,+BAAgC8zB,EAAMS,UACtC,gCAAiCT,EAAMU,WACvC,iCAAkCV,EAAMW,YACxC,kCAAmCX,EAAMY,gBCzXvC,gBAANG,GAAA,sBAAAC,IAAAhhB,EAAA,MAEA,QACAxf,KAAA,gBACAO,WAAA,CACAkgC,KAAA,GACAC,YAAA,EACAzmB,WAAA,EACA0mB,aAAA,GACAC,YAAAA,IAGAv2B,MAAA,CACA3K,SAAA,CAAAgD,KAAAiL,OAAAC,UAAA,GACAizB,MAAA,CAAAn+B,KAAA4H,OAAAC,QAAA,SACAu2B,OAAA,CAAAp+B,KAAAiL,OAAApD,QAAAA,KAAA,KACAxD,WAAA,CAAArE,KAAAmJ,QAAAtB,SAAA,GACAw2B,gBAAA,CAAAr+B,KAAAmJ,QAAAtB,SAAA,GACAoE,aAAA,CAAAjM,KAAAiL,OAAApD,QAAA,MACAnH,cAAA,CAAAV,KAAA,CAAA4H,OAAA+P,QAAA9P,QAAA,IACAlH,MAAA,CAAAX,KAAAqJ,MAAAxB,QAAAA,IAAA,IACAxF,aAAA,CAAArC,KAAAmJ,QAAAtB,SAAA,GACA3G,YAAA,CAAAlB,KAAAmJ,QAAAtB,SAAA,GACAjH,WAAA,CAAAZ,KAAAiL,OAAApD,QAAA,MACAhH,aAAA,CAAAb,KAAAiL,OAAApD,QAAA,MACA/G,WAAA,CAAAd,KAAAmJ,QAAA+B,UAAA,GACAnK,iBAAA,CAAAf,KAAAmJ,QAAA+B,UAAA,GACAjK,kBAAA,CAAAjB,KAAAmJ,QAAA+B,UAAA,GACAlK,iBAAA,CAAAhB,KAAAmJ,QAAA+B,UAAA,GACA5H,OAAA,CAAAtD,KAAA,CAAA4H,OAAA+P,QAAA9P,QAAA,MACAtE,cAAA,CAAAvD,KAAAmJ,QAAAtB,SAAA,GACArF,aAAA,CAAAxC,KAAAmJ,QAAAtB,SAAA,GACA1G,SAAA,CAAAnB,KAAAqJ,MAAAxB,QAAAA,IAAA,IACAzG,eAAA,CAAApB,KAAAmJ,QAAAtB,SAAA,GACAy2B,YAAA,CAAAt+B,KAAAqJ,MAAAxB,QAAAA,IAAA,IACApE,YAAA,CAAAzD,KAAAqJ,MAAAxB,QAAAA,IAAA,IACAnE,eAAA,CACA1D,KAAAqJ,MACAxB,QAAAA,IAAA,CACA,CAAAvK,KAAA,eAAA2pB,MAAA,WAIAsX,WAAA,CAAAv+B,KAAAmJ,QAAAtB,SAAA,GACA22B,YAAA,CAAAx+B,KAAAmJ,QAAAtB,SAAA,GACAlE,aAAA,CAAA3D,KAAAmJ,QAAAtB,SAAA,GACAjE,UAAA,CAAA5D,KAAAmJ,QAAAtB,SAAA,GACAhE,UAAA,CAAA7D,KAAAmJ,QAAAtB,SAAA,GACA/D,aAAA,CAAA9D,KAAA2X,OAAA9P,QAAA,KACA9D,gBAAA,CAAA/D,KAAA2X,OAAA9P,QAAA,OACA7D,WAAA,CAAAhE,KAAAmJ,QAAAtB,SAAA,GACA5D,mBAAA,CAAAjE,KAAAmJ,QAAAtB,SAAA,GACA3D,uBAAA,CAAAlE,KAAAmJ,QAAAtB,SAAA,GACA1D,WAAA,CAAAnE,KAAAmJ,QAAAtB,SAAA,GACAtD,eAAA,CAAAvE,KAAAmJ,QAAAtB,SAAA,GACArD,YAAA,CACAxE,KAAAiL,OACApD,QAAAA,KAAA,CAAAuZ,UAAA,EAAA/V,OAAA,SAAA2S,IAAA,QAEAvZ,sBAAA,CAAAzE,KAAAmJ,QAAAtB,SAAA,GACArE,YAAA,CAAAxD,KAAA4H,OAAAC,QAAA,IACAnD,cAAA,CAAA1E,KAAA4H,OAAAC,QAAA,KACAlD,cAAA,CAAA3E,KAAAqJ,MAAAxB,QAAA,MACAxG,aAAA,CAAArB,KAAAiL,OAAApD,QAAAA,QACArG,OAAA,CAAAxB,KAAAqJ,MAAA6B,UAAA,GACAzJ,WAAA,CAAAzB,KAAAmJ,QAAA+B,UAAA,GACAxJ,WAAA,CAAA1B,KAAAqJ,MAAA6B,UAAA,GACAtJ,UAAA,CAAA5B,KAAAqJ,MAAA6B,UAAA,GACAvJ,eAAA,CAAA3B,KAAAmJ,QAAA+B,UAAA,GACA/I,iBAAA,CAAAnC,KAAAoJ,SAAA8B,UAAA,GACA9I,mBAAA,CAAApC,KAAAoJ,SAAA8B,UAAA,GACA5I,aAAA,CAAAtC,KAAAiL,OAAAC,UAAA,GACAuzB,eAAA,CAAAz+B,KAAAmJ,QAAA+B,UAAA,GACAlJ,eAAA,CAAAhC,KAAAmJ,QAAA+B,UAAA,GACA5J,SAAA,CAAAtB,KAAAqJ,MAAA6B,UAAA,GACA3J,QAAA,CAAAvB,KAAAiL,OAAApD,QAAAA,QACA5F,gBAAA,CAAAjC,KAAAmJ,QAAA+B,UAAA,GACAhJ,iBAAA,CAAAlC,KAAAmJ,QAAA+B,UAAA,GACAnJ,QAAA,CAAA/B,KAAAmJ,QAAA+B,UAAA,GACA3I,iBAAA,CAAAvC,KAAAmJ,QAAA+B,UAAA,GACArJ,aAAA,CAAA7B,KAAA4H,OAAAC,QAAA,IACA/F,eAAA,CACA9B,KAAAiL,OACApD,QAAAA,KAAA,CAAAkD,QAAA,UAAAC,QAAA,kBAIA1B,MAAA,CACA,oBACA,iBACA,eACA,eACA,iBACA,YACA,yBACA,wBACA,iBACA,0BACA,WACA,sBACA,qBACA,kBACA,sBACA,gBACA,uBAGAhL,IAAAA,GACA,OACA+E,KAAA,KAAAhC,cAAA,GACA0E,aAAA,GACA24B,eAAA,GACAC,kBAAA,EACAr6B,eAAA,EACAwH,UAAA,EACAjG,cAAA,EACAzC,aAAA,EACA4C,cAAA,EACAC,eAAA,GACA24B,WAAA,EAGA,EAEA9gC,SAAA,CACAsG,CAAAA,GACA,UACAy6B,MACA,KAAA5yB,aAEA,EAEA9I,OAAAA,GACA,MAAA27B,EAAApG,GAAA,KAAAyF,OACAY,EAAA,GASA,OAPA9zB,OAAA2L,KAAAkoB,GAAAtpB,KAAA9P,IACAq5B,EAAAr5B,GAAA,IACAo5B,EAAAp5B,MACA,KAAA04B,OAAA14B,IAAA,GACA,IAGAk4B,GAAAmB,EACA,GAGArgC,MAAA,CACAiC,MAAA,CACA2X,WAAA,EACAC,MAAA,EACAL,OAAAA,CAAAC,EAAAoa,GAKA,GAJApa,EAAA,IAAAA,EAAAN,MAAAxU,GAAAA,EAAAC,SAAA,KAAAD,KAAAC,WACA,KAAAgB,eAAA,IAGA,KAAAq6B,kBAAA,KAAAp7B,eAAA4U,EAAA,MAAAoa,GAAApa,EAAAvO,SAAA2oB,EAAA3oB,QACA,QAAAtG,OAAA,CACA,MAAAD,EAAA8U,EAAAN,MAAAmnB,GAAAA,EAAA17B,SAAA,KAAAA,UAAA,GACA,KAAA27B,UAAA,CAAA57B,QAEA,WAAAA,MAAA,IAAA4H,OAAA2L,KAAA,KAAAvT,MAAAuG,OACA,KAAAq1B,UAAA,CAAA57B,KAAA,KAAAA,OAEA,KAAAiB,eAAA,CAGA,GAGAjC,YAAAA,CAAA6O,GACAA,IAAA,KAAA7N,KAAA,GACA,EAEAC,OAAA,CACAgV,WAAA,EACAJ,OAAAA,CAAAC,EAAAoa,GACA,GAAApa,IAAA,KAAA9V,cAAA,KAAA1B,MAAAiJ,OAAA,CACA,MAAAvG,EAAA,KAAA1C,MAAAkX,MAAAmnB,GAAAA,EAAA17B,SAAA6U,IACA,KAAA8mB,UAAA,CAAA57B,QACA,MAAAkvB,IAAApa,IACA,KAAA9U,KAAA,GAEA,GAGAA,IAAAA,CAAA6N,GACAA,GAAA,IAAAjG,OAAAi0B,QAAAhuB,GAAAtH,SAEAi0B,GAAA3sB,GAEAA,EAAAjE,MAAA6S,SAAA/gB,IACA++B,GAAA/+B,EAAA,IAEA,EAEAs/B,eAAAA,CAAAntB,GACA,KAAA5M,cAAA4M,CACA,EAEAutB,cAAAA,CAAAvtB,GACAA,GAAA,KAAA9N,cACA,KAAAA,aAAA,EAEA,GAGA4U,OAAAA,GACA,KAAApV,eACA,EAEAhE,QAAA,CACAsH,gBAAAA,GACA,KAAAF,cAAA,EACA,KAAAC,eAAA,EACA,EAEAb,kBAAAA,CAAAiX,GACA,KAAAlX,MAAA,mBAAAkX,EACA,EAEAzZ,aAAAA,CAAA0qB,GACA,MAAA6R,EAAA9nB,KAAAA,YAAA+nB,SACA,KAAAj6B,MAAA,kBACA9B,KAAA,KAAAA,KACAiqB,UACA8R,OAAAD,GAEA,EAEAx8B,WAAAA,CAAA8W,GACA,KAAAtU,MAAA,mBACAsU,EACAnW,OAAA,KAAAD,KAAAC,OACAkW,MAAA,KAAAnW,KAAAmW,OAEA,EAGA5U,WAAAA,CAAA6U,GACA,KAAAtU,MAAA,mBAAAsU,EAAAnW,OAAA,KAAAD,KAAAC,QACA,EAGAuB,aAAAA,CAAA4U,GACA,KAAAtU,MAAA,kBAAAsU,UAAAnW,OAAA,KAAAD,KAAAC,QACA,EAEAT,QAAAA,EAAA,QAAA4W,EAAA,KAAArR,IACA,eAAAA,EAAAke,OAGA,OAFA,KAAAtgB,cAAA,OACA,KAAAC,eAAAmC,EAAAA,MAGA,KAAAjD,MAAA,aAAAsU,UAAArR,QACA,EAEA1F,iBAAAA,EAAA,OAAA4jB,EAAA,IAAA+Y,IACA,KAAAl6B,MAAA,uBACAmhB,SACA+Y,OAEA,EAEAv6B,mBAAAA,CAAA0xB,GACA,KAAArxB,MAAA,4BACAqxB,EACAlzB,OAAA,KAAAD,KAAAC,QAEA,EACAyB,aAAAA,CAAA0U,GACA,KAAAtU,MAAA,kBACAsU,UACAnW,OAAA,KAAAD,KAAAC,QAEA,EACA0B,qBAAAA,CAAAyU,GACA,KAAAtU,MAAA,2BACAsU,UACAnW,OAAA,KAAAD,KAAAC,QAEA,EACA2B,eAAAA,CAAA3G,GACA,MAAAkI,EAAAlI,EAAAkX,KAAAshB,IAAA,CACA/vB,GAAA+vB,EAAAx5B,KACAA,KAAAw5B,EAAAx5B,KAAA,IAAAw5B,EAAAjkB,UACAtJ,IAAAutB,EAAA13B,IACA6H,MAAA6vB,EAAA13B,IACAuK,UAAAmtB,EAAAntB,UACAD,KAAAotB,EAAAptB,KACAF,SAAAstB,EAAAttB,SACA9C,OAAAowB,EAAAvwB,QAEA,KAAAR,aAAAS,EACA,KAAAX,cAAA,CACA,EACAC,aAAAA,GACA,KAAAD,cAAA,CACA,EAEAy5B,aAAAA,GACA,KAAAl8B,aAAA,KAAAA,WACA,EACAm8B,SAAAA,GACA,KAAAX,WAAA,KAAAA,SACA,EACAY,SAAAA,GACA,KAAAZ,WAAA,KAAAA,SACA,IC/VkQ,MCQ9P,IAAY,OACd,GACA,EACA,GACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,Q,sBCnB5BjiC,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,cAAcI,MAAM,CAAE,UAAWZ,EAAI6iC,SAAU,CAAC7iC,EAAIO,GAAG,IAChJ,EACID,GAAkB,CAAC,WAAY,IAAIN,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACM,YAAY,2BAA2B,CAACN,EAAG,OAAOA,EAAG,OAAOA,EAAG,QAChJ,GCQA,IACAQ,KAAA,UACAqK,MAAA,YCbsP,MCOlP,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QCjBzB,MAAM+3B,GAAeh2B,IAC1B,IAAIwpB,EAAO,IAAIrJ,KAAY,IAAPngB,GACpB,OAAOwpB,EAAKyM,eAAe,QAAS,CAClCC,KAAM,UACNC,OAAQ,UACRC,QAAQ,GACR,EAgBSC,GAAWA,IACtBrd,KAAKsd,SACFC,SAAS,IACTphB,QAAQ,WAAY,IACpBqB,OAAO,EAAG,IAEFkD,GAAchb,IACzB,MAAM8Z,EAAc,CAAC,MAAO,MAAO,OAAQ,OAAQ,MAAO,OAC1D,GAAK9Z,GAASA,EAAKyK,UACnB,OAAOqP,EAAYhG,MAAK9X,GAAKgE,EAAKyK,UAAUiG,cAAcS,SAASnV,IAAG,EChC3D87B,GAAiBA,CAACv2B,EAAWuoB,EAAS,MACjD,IAAKvoB,EAAW,OAEhB,MAAMD,EAAO,IAAImgB,KAAiB,IAAZlgB,GAEtB,GAAe,UAAXuoB,EACF,MAAO,GAAGiO,GAAQz2B,EAAK02B,WAAY,MAAMD,GAAQz2B,EAAK22B,aAAc,KAC/D,GAAe,iBAAXnO,EAA2B,CACpC,MAAM5E,EAAU,CAAEgT,MAAO,OAAQC,KAAM,UAAWC,IAAK,WACvD,MAAO,GAAG,IAAIC,KAAKC,eAAe,QAASpT,GAAS4E,OAAOxoB,IAC7D,CAAO,GAAe,aAAXwoB,EAAuB,CAChC,MAAM5E,EAAU,CAAEgT,MAAO,UAAWC,KAAM,UAAWC,IAAK,WAC1D,MAAO,GAAG,IAAIC,KAAKC,eAAe,QAASpT,GAAS4E,OAAOxoB,IAC7D,CAAO,GAAe,mBAAXwoB,EAA6B,CACtC,MAAM5E,EAAU,CAAEgT,MAAO,OAAQE,IAAK,WACtC,MAAO,GAAG,IAAIC,KAAKC,eAAe,QAASpT,GAAS4E,OAAOxoB,OAAUy2B,GAAQz2B,EAAK02B,WAAY,MAAMD,GAClGz2B,EAAK22B,aACL,IAEJ,CAEA,OAAO32B,CAAI,EAGPy2B,GAAUA,CAACQ,EAAKC,IACbh5B,OAAO+4B,GAAKE,SAASD,EAAK,KAGtBE,GAAYA,CAACC,EAAIC,IACrBD,EAAGE,gBAAkBD,EAAGC,eAAiBF,EAAGG,aAAeF,EAAGE,YAAcH,EAAGI,YAAcH,EAAGG,UAI5FC,GAAalO,GACpBA,EACK,IAAIrJ,KAAKqJ,GAAMmO,mBAAmB,QAAS,CAChDb,IAAK,UACLF,MAAO,QACPC,KAAM,aAGD,IAAI1W,MAAOwX,mBAAmB,QAAS,CAC5Cb,IAAK,UACLF,MAAO,QACPC,KAAM,YCuBZ,QACAjjC,KAAA,OAEAO,WAAA,CACAyjC,WAAA,GACAC,QAAA,GACAzQ,KAAAA,IAGAxyB,IAAAA,GACA,OACAyD,SAAA,EACAT,SAAA,GACAC,QAAA,KACAS,gBAAA,EACAE,kBAAA,EACAP,gBAAA,EACAM,iBAAA,EACAlB,kBAAA,EACAC,kBAAA,EACAC,mBAAA,EACAsS,OAAAiuB,GACA5/B,UAAA,GACAJ,OAAA,GACAZ,WAAA,GACAC,aAAA,GACAC,YAAA,EACAY,WAAA,GACAD,YAAA,EACAJ,aAAA,GACAH,aAAA,EACAmB,cAAA,EACA1B,MAAA,GACAQ,SAAA,GACAd,YAAA,EACAK,cAAA,GACA+gC,cAAA,KACArgC,gBAAA,EACA9B,QAAA,KACAkK,SAAA,KACA/J,UAAA,KACAE,aAAA,KACA+hC,OAAA,GACAC,2BAAA,GACA7/B,eAAA,GACAS,kBAAA,EACAC,cAAA,EACAX,aAAA,GACA+/B,aAAA,GACA/tB,OAAA,KACA6D,SAAA,KACA8B,MAAA,KACAjZ,SAAA,EACAC,cAAA,EACAqhC,QAAA,EACAvhC,oBAAA,EAEA,EAEAxC,SAAA,KACAW,EAAAA,EAAAA,IAAA,iCAGAuZ,OAAAA,GACA,MAAA1U,EAAA,KAAAnF,OAAAoY,OAAAxP,GACA/J,EAAA,KAAAe,OAAAC,MAAAhB,SAEA,KAAAA,SAAA,CAAAwM,SAAAxM,EAAA0C,aACA,KAAAJ,QAAAtC,EAAAsC,QACA,KAAAG,UAAAzC,EAAAyC,UACA,KAAAE,aAAA3C,EAAA2C,aACA,KAAA6J,SAAAxM,EAAA0C,YACA,KAAAgiC,OAAA1kC,EAAA0kC,QAAA,0BAEA,IAAA1iC,EAAAC,OAAAC,SAAAC,KACA2iC,EAAA,IAAAziC,IAAAL,GACA,KAAA6U,OAAAiuB,EAAAviC,aAAAC,IAAA,WACA,KAAAkY,SAAAoqB,EAAAviC,aAAAC,IAAA,kBACA,KAAAga,MAAAsoB,EAAAviC,aAAAC,IAAA,eAEA,KAAAuiC,mBACA,KAAAC,eAAA1+B,GACA,KAAA2+B,WACA,EAEArjC,QAAA,KACAC,EAAAA,EAAAA,IAAA,CACA,cACA,iBACA,kBACA,kBAGA,sBAAAkjC,GACA,KAAAF,QAAA,EACA,IACA,MAAAK,EAAA,IAAAC,gBAAAljC,OAAAC,SAAA6I,QACArI,EAAAwiC,EAAA1iC,IAAA,eACAqU,EAAAquB,EAAA1iC,IAAA,WAEAua,QAAA1C,GAAA7X,IAAA,aAAAE,aAAAmU,KAEA,GAAAkG,EAAAzb,KAAAS,KAAA,CACA,MAAAA,EAAAgb,EAAAzb,KAAAS,KACAqjC,EAAArjC,EAAAsjC,aAAA7sB,KAAA8sB,GACAA,EAAAzjB,QAAA,aAEA9f,EAAAnB,SACA,KAAA4C,aAAA4hC,EAAA7oB,SAAA,UAGA,KAAAhZ,QAAAxB,EAAAwjC,MACA,KAAAV,QAAA,CACA,MACA,KAAAvhC,oBAAA,EACA,KAAAuhC,QAAA,CAEA,OAAAh5B,GACA,KAAAg5B,QAAA,EACAr2B,QAAA3C,MAAA,gDAAAA,EACA,CACA,EAIAo5B,SAAAA,GACA,MAAAO,EAAA,QAAA/iC,aAAA,KAAAE,eACA,IAAA8iC,EAAA,IAAAC,KAAA,yBACAC,UAAA,EACAC,QAAA,KACAC,aAAA,GAAAlvB,GAAAC,yBACAkvB,KAAA,CACAvsB,OAAA,CACAjX,QAAA,KAAAA,YAKAmjC,EAAAM,WAAArU,KAAA,sBAGA,MAAAsU,EAAAP,EAAAR,UAAA,WAAAO,GACAQ,EAAAtU,KAAA,eAAAuU,IACA,MAAA93B,EAAA83B,EAAAxpB,QACA,GAAAtO,EAAA0Y,OAAA,OAGA,MAAArK,EAAA,KAAAA,MAAAqF,QAAA,QACAqkB,EAAA/3B,EAAA+3B,OAAArkB,QAAA,QAEA,GAAArF,IAAA0pB,EAAA,CACA,MAAAzpB,EAAA,KAAA0pB,cAAAh4B,GAAA,GAEA,KAAAhK,SAAAkf,KAAA5G,EACA,KAEAupB,EAAAtU,KAAA,cAAAuU,IAEA,MAAA3kC,EAAA2kC,EAAA3tB,OAGAxO,EAAA,KAAA3F,SAAAiiC,WACA3pB,GAAA7R,OAAA6R,EAAA4pB,UAAAz7B,OAAAtJ,EAAAyI,KAAAa,OAAA6R,EAAAlT,OAAAqB,OAAAtJ,EAAAyI,MAGA,QAAAD,EAAA,CACA,IAAA0jB,EAAA,KAAArpB,SAAA2F,GACA,aAAAxI,EAAAgX,SACAkV,EAAA9F,aAAA,GAGA,QAAApmB,EAAAgX,SACAkV,EAAA9F,aAAA,EACA8F,EAAA7F,MAAA,GAGA,UAAArmB,EAAAgX,SACAkV,EAAA3F,QAAA,EACA2F,EAAAnjB,OAAA/I,GAAAglC,eAGA,KAAAniC,SAAA2F,GAAA0jB,EACA,KAAA+Y,aACA,MACA/3B,QAAAC,IAAA,YACA,GAEA,EAEA,oBAAAu2B,CAAA1+B,GACA,IACA,MAAAkgC,EAAAnsB,GAAA7X,IACA,0CAAAF,WAEAmkC,QAAAC,QAAAC,IAAA,CAAAH,IACA,KAAAnjC,YAAA,EAEA,KAAAuB,UAAA6hC,EAAA,IAAAnlC,MAAAA,MAAA,GAEA,KAAA4B,aAAA,EACA,OAAAga,GACA1O,QAAAC,IAAAyO,GACA,KAAAha,aAAA,EACA,CACA,EAEA0jC,eAAAA,CAAAj6B,GACA,MAAAD,EAAA,IAAAmgB,KAAA,IAAAlgB,GACAk6B,EAAA/C,GAAAp3B,EAAA,IAAAmgB,MAAA,mBACAlL,EAAAuhB,GAAAv2B,EAAAk6B,GACA,gBAAAA,EAAA,UAAAllB,IAAAA,CACA,EAEA7b,iBAAAA,CAAAO,GACAA,GACA,KAAA3B,WAAA2B,EAAA7B,OAAAgU,KAAAshB,GAAAA,EAAA/vB,KACA,KAAAvF,OAAA,KAAAA,OAAAgU,KAAAshB,IAAA,IACAA,EACAxzB,OAAAD,EAAAC,OACAwgC,SAAA,KAAApiC,WAAA6X,SAAAud,EAAA/vB,SAGA,KAAArF,WAAA,GAEA,KAAAD,YAAA,KAAAA,UACA,EAEAiB,iBAAAA,EAAA,OAAA4jB,EAAA,IAAA+Y,IACA,YAAA/Y,EAAAhpB,MACA,KAAAymC,UAAA1E,EAGA,EAEA,eAAA0E,CAAA1E,GACA,MAAA2E,EAAA,KAAArjC,MAAAyiC,WAAAtM,GAAAA,EAAAuI,MAAAA,IACA4E,GAAA,KAAAtjC,MAAAqjC,GAAAC,OACA,IACA,MAAAC,EAAA,cAAA7E,aAAA,KAAA//B,WACA,KAAAhB,SAAA+Y,GAAAwC,KAAAqqB,EAAA,CAAAD,WACA,IAAA3lC,EAAA0b,GAGA,UAAA2R,MAFA,KAAAhrB,MAAAqjC,GAAAC,OAAAA,CAIA,OAAA/pB,GACA,KAAAlY,gBAAA,EACAwJ,QAAAC,IAAAyO,EACA,CACA,EAGAjX,cAAAA,CAAAoZ,GAEA,KAAAulB,aAAAuC,QAAA9nB,EAAA5C,SAEA,KAAAtY,SAAA,GACA,IAAAA,EAAA,KAAAygC,aAAApsB,KAAAshB,GACA,KAAAqM,cAAArM,KAGA,MAAAsN,EAAA,KAAAC,aAAAljC,GAEAijC,EAAAtkB,SAAA3U,IACA,KAAAhK,SAAAgjC,QAAAh5B,EAAA,IAGA,KAAAo4B,aACA,EAEA,mBAAA3gC,EAAA,KAAAS,EAAA,QAAAiqB,EAAA,UAAA8R,IACA,KAAA58B,cAAA,EACA8qB,EAAAgX,QACA,KAAAnjC,SAAA,GAEA,KAAAC,gBAAA,GAGA,IAAAmjC,EAAA7hB,KAAAC,MAAAkH,KAAA+C,MAAA,KACA,MAAAsG,EACA,KAAA/xB,SAAAyI,OAAA,OAAAzI,SAAA,GAAA+xB,KAAAqR,EAEArR,IAAAqR,IACA,KAAAxjC,kBAAA,EACA,KAAAH,WAAA,IAGA,KAAAW,QAAA,CACA4G,OAAAi3B,EAAAj3B,OACAgD,IAAA,aACAsO,QAAA,qBAEA,MAAAM,QAAA1C,GACA7X,IAAA,yBAAAqU,iBAAA,KAAA2F,QAAA,CACAgrB,YAAApF,EAAAhmB,QAEAa,MAAA,KAAAwqB,kBAAA,KAAAC,aAAA,GAEA,GAAA3qB,EAAA,CACA,KAAAvX,cAAA,EACA,WAAAlE,GAAAyb,EACA,UAAAzb,EAAAgX,OAAA,CACA,KAAAssB,aAAAtjC,EAAA6C,SAEA,MAAAwjC,EAAAn8B,SAAAo8B,uBAAA,mBACAC,EAAAF,EAAA,SAUA,GATAxzB,YAAA,KACA0zB,IACAr8B,SAAAihB,eAAA,SAAAqb,UAAAH,EAAA/6B,OACA,GACA,KAEA,KAAAm7B,gBAAA,WACA,KAAA3jC,gBAAA,EAEA,IAAA9C,EAAA6C,SAAAyI,OACA,OAGA,KAAAzI,SAAA,GACA,IAAAA,EAAA,KAAAygC,aAAApsB,KAAAshB,GACA,KAAAqM,cAAArM,KAGA,MAAAsN,EAAA,KAAAC,aAAAljC,GAEAijC,EAAAtkB,SAAA3U,IACA,KAAAhK,SAAAgjC,QAAAh5B,EAAA,IAGA,KAAAo4B,cACA,KAAArjC,aAAA,EACA,MACAsL,QAAAC,IAAAnN,EAGA,CACA,EAEAmmC,iBAAAA,CAAAvqB,GACA1O,QAAAC,IAAAyO,GACA1O,QAAAC,IAAA,oBACA,EAEAs5B,eAAAA,CAAA55B,GACA,KAAA5J,QAAA4J,IAAAA,EACA,KAAA7J,SAAA+e,KAAA,KAAA9e,SACA,KAAAA,QAAA,IACA,EAGA8iC,YAAAA,CAAAljC,GACA,MAAA6jC,EAAA,GACA,IAAAvrB,EAAA,GAEA,QAAAhU,EAAA0F,KAAAhK,EAAA+9B,UAAA,CACA,GAAA/zB,EAAAmD,MAEA,CACA,MAAAA,EAAA,CACA,IACAnD,EAAAmD,MAAA,GACA3E,UAAAwB,EAAAxB,UACA+a,YAAAvZ,EAAAuZ,YACAC,KAAAxZ,EAAAwZ,KACAjb,KAAAyB,EAAAzB,KACAF,SAAA2B,EAAA3B,SACAjD,IAAA4E,EAAA5E,MAIA,OAAAd,EACAgU,EAAA,IAAAtO,EAAAmD,aACA,CACA,MAAA22B,EAAAD,EAAAp7B,OAAA,EACAs7B,EAAAF,EAAAC,GAAA32B,OAAA1E,OAAA,EAEA,GACAo7B,EAAAC,GAAA32B,OACAnD,EAAA0Y,SAAAmhB,EAAAC,GAAAphB,QACA1Y,EAAA3B,WAAAw7B,EAAAC,GAAAz7B,WACAw7B,EAAAC,GAAAj6B,SACAoY,GAAAjY,EAAAmD,MAAA,KACA8U,GAAA4hB,EAAAC,GAAA32B,MAAA42B,MACA/5B,EAAAH,QACA,CACAg6B,EAAAC,GAAA32B,MAAA61B,QAAA71B,EAAA,IACA,QACA,CACAmL,EAAA,IAAAtO,EAAAmD,QAEA,CACA,MAnCAmL,EAAAtO,EAoCA65B,EAAA3kB,KAAA5G,GACAA,EAAA,EACA,CAEA,OAAAurB,CACA,EAGA7B,aAAAA,CAAAh4B,EAAAg6B,GAAA,GACA,MAAAhhB,EAAAhZ,EAAAi6B,YACA,CACAp6B,QAAAG,EAAAk6B,YAAA,GAAAl6B,EAAAm6B,cACA/+B,IAAA4E,EAAAi6B,YACA57B,SAAA2B,EAAA7N,MAAA6N,EAAA9E,WACAiI,MAAAnD,EAAAk6B,aAEA,KACA,OACA9+B,IAAA4E,EAAApE,GACAiE,QAAAG,GAAAo6B,QAAAp6B,EAAAo6B,QAAAp6B,EAAAO,KACAlC,SAAA2B,EAAA7N,KAAA6N,EAAA7N,MAAA6N,EAAA9E,WAAA,GACAqD,KAAA03B,GAAA,IAAAj2B,EAAA+nB,MACAvpB,UAAA+1B,GAAAv0B,EAAA+nB,MACArO,OAAA,WAAA1Z,EAAAmK,OACAjO,OAAA8D,EAAAm4B,cACAtf,MAAA,SAAA7Y,EAAAmK,OACAoP,YAAA,SAAAvZ,EAAAmK,QAAA,cAAAnK,EAAAmK,OACAqP,KAAA,SAAAxZ,EAAAmK,OACAhH,MAAAnD,EAAAmD,MACA6V,aAAAA,EACAN,OAAAshB,EAAA,EAAAh6B,EAAA0Y,OACAqP,KAAA/nB,EAAA+nB,KAEA,EAEAsS,SAAAA,GACA,MAAAC,EAAAC,aAAAC,QAAA,aACA,OAAAF,EAAAG,KAAA9pB,MAAA2pB,GAAA,EACA,EAEAI,SAAAA,CAAAC,GACA,KAAAC,gBAAAD,GACA,MAAAE,EAAAJ,KAAAK,UAAAH,GACAJ,aAAAQ,QAAA,YAAAF,EACA,EAEAzC,WAAAA,GAEA,MAAAkC,EAAA,KAAAD,YACA7a,EAAA,CACAzZ,IAAA,EACAxK,OACA,IAAA++B,EAAA,KAAAhE,gBAAAvwB,IACA,KACAu0B,EAAA,KAAAhE,gBAAA/6B,QAEA++B,EAAA,KAAAhE,eAAA9W,EACA,KAAAkb,UAAAJ,EACA,EAEA9iC,WAAAA,CAAA8W,GACAA,EAAAnL,MACA,KAAA63B,cAAA1sB,GAEA,KAAA2sB,SAAA3sB,EAEA,EAEA,cAAA2sB,CAAAj7B,GACA,cAAAH,EAAA,aAAAmZ,GAAAhZ,EACA,IACA,MAAAk7B,EAAAtG,KACAuG,EAAA,KAAAnlC,SAAAyI,OACAD,EAAA+1B,IAAA,IAAA7V,MAAA0c,UAAA,KACA,IAAAnnC,EAAA,OACA,KAAA+B,SAAAkf,KAAA,CACA9Z,IAAA8/B,EACAr7B,QAAAA,EACAxB,SAAA,KAAAA,SACAE,KAAA03B,KACAz3B,YACAqa,OAAA,EACAH,OAAA,EACAM,aAAAA,EACAO,YAAA,SAGA,IAAA9K,EAAA,CACAta,QAAA,KAAAuU,OACA2F,MAAA,KAAAA,MACAC,QAAAzO,EACA0M,SAAA,KAAAA,UAGAyM,IACAvK,EAAA,IACAA,EACAwrB,YAAAjhB,EAAA5d,IACA++B,cAAAnhB,EAAAnZ,SAEA5L,EAAA,SAGA,KAAAmD,kBAAA,EACA,WAAAjE,SAAA+Y,GAAAwC,KAAA,OAAAza,IAAAwa,GACAkd,EAAAx4B,EAAAkoC,aACAloC,EAAA0b,IACA,KAAAzX,kBAAA,EACA,KAAA+T,KAAA,KAAAnV,SAAAmlC,EAAA,IACA,KAAAnlC,SAAAmlC,GACAt7B,QAAA1M,EAAAmoC,YACAj9B,SAAAstB,EAAAx5B,MAAAw5B,EAAAzwB,WACAqD,KAAA03B,GAAA,IAAAtK,EAAA5D,MACAvpB,UAAA+1B,GAAA5I,EAAA5D,MACAlP,OAAA,EACAH,OAAAlM,OAAAmf,EAAAjT,QACAM,aAAAA,EACAO,aAAA,EACA2e,OAAAvM,EAAA/vB,OAGA,KAAA/E,gBAAA,EACA,KAAAH,aAAAvD,EAAAmb,SAAA,yBACA,KAAAlX,kBAAA,EAEA,OAAA2X,GACA,KAAArY,aAAAqY,EAAAH,UAAAzb,MAAAmb,SAAA,yBACA,KAAAlX,kBAAA,EACA,KAAAP,gBAAA,EACAwJ,QAAAC,IAAAyO,EACA,CACA,EAEAisB,aAAAA,CAAAh7B,GACA,MAAAxB,EAAA+1B,IAAA,IAAA7V,MAAA0c,UAAA,KAEAp7B,EAAAmD,MAAAwR,SAAA,CAAAhT,EAAArH,KACA,MAAAuF,EAAA,IAAAvF,EAAA0F,EAAAH,QAAA,GACAwf,EAAA,CACAjkB,IAAAw5B,KACA/0B,QAAAA,EACAxB,SAAA,KAAAA,SACAE,KAAA03B,KACAz3B,YACAqa,OAAA,EACAH,OAAA,EACAvV,MAAA,KAAAxB,EAAAnD,YAAAqa,OAAA,KAGA,KAAA7iB,SAAA,SAAAA,SAAAqpB,GACA,KAAAkc,YAAA17B,EAAAG,EAAA7H,OAAAknB,EAAA,GAEA,EAGA,iBAAAkc,CAAA17B,EAAA1H,EAAA6H,GAEA,IAAAw7B,EAAA,IAAAC,SACAD,EAAAE,OAAA,OAAA17B,EAAAmD,MAAA,GAAAwpB,SACA6O,EAAAE,OAAA,UAAA77B,GACA27B,EAAAE,OAAA,aAAArtB,OACAmtB,EAAAE,OAAA,eAAAhzB,QACA8yB,EAAAE,OAAA,gBAAAnvB,UAEA,IACA,WAAApZ,SAAA+Y,GAAAwC,KAAA,aAAA8sB,GACA,IAAAroC,EAAA0b,GAGA,UAAA2R,MAFA,KAAAmb,iBAAA,EAAA37B,EAAA5E,IAAAjI,EAIA,OAAA4b,GACA,KAAAlY,gBAAA,EACA,KAAAH,aAAA,sBACA,KAAAilC,iBAAA,EAAA37B,EAAA5E,KACA,KAAApF,SAAAoK,MACAC,QAAAC,IAAAyO,EACA,CACA,EAGA4sB,eAAAA,CAAAj+B,EAAA9B,EAAAzI,GACA,MAAA6M,EAAA,KAAAhK,SAAA0W,MAAA/K,GAAAA,EAAAvG,MAAAQ,IACAoE,IAEAtC,GACAsC,EAAAmD,MAAA,GAAAzF,OAAA,EACAsC,EAAAmD,MAAA,GAAAqG,SAAA,IAEAxJ,EAAAk4B,OAAA/kC,EAAAusB,UACA1f,EAAAmD,MAAA,GAAAqG,SAAA,EACAxJ,EAAAmD,MAAA,GAAAlP,IAAAd,EAAAc,IAAAd,EAAAc,IAAA+L,EAAAmD,MAAA,GAAAlP,IACA+L,EAAA5E,IAAAjI,EAAAusB,WAEA,EAEAhoB,QAAAA,EAAA,KAAAuF,IACAnJ,OAAA8nC,KAAA3+B,EAAAA,KAAAhJ,IAAA,SACA,EAEA+C,gBAAAA,GACA,KAAAH,gBAAA,KAAAA,eACA,KAAAH,aAAA,EACA,EAEAO,kBAAAA,GACA,KAAAF,kBAAA,KAAAA,iBACA,KAAAJ,eAAA,EACA,EAEAiB,YAAAA,GACA,KAAAikC,eACA,EAEAhkC,iBAAAA,CAAAK,GACA,MAAAjE,EAAA,QAAAsiC,mBAAA,KAAAjiC,qBAAA4D,EAAA6R,aACAjW,OAAA8nC,KAAA3nC,EACA,ICrqByP,MCQrP,IAAY,OACd,GACA,EACA,GACA,EACA,KACA,KACA,MAIF,GAAe,GAAiB,QCfhC6nC,EAAAA,GAAIC,IAAIC,EAAAA,IAER,MAAMC,GAAS,CACb,CACE3/B,KAAM,IACN4/B,MAAO,cACP/pC,KAAM,QACNC,UAAW+pC,IAEb,CACE7/B,KAAM,kBACN8/B,SAAU,CAAEjqC,KAAM,WAIhBkqC,GAAS,IAAIL,EAAAA,GAAU,CAE3BM,KAAMrwB,SACNgwB,UACAM,qBAAsB,gBAGxB,UCtBAT,EAAAA,GAAIC,IAAIS,EAAAA,IAER,WAAmBA,EAAAA,GAAAA,MAAW,CAC5B3pC,MAAO,CACLhB,SAAU,KACViB,aAAa,EACbhB,WAAW,EACX2qC,UAAU,EACVC,YAAa,GACbC,YAAY,EACZC,cAAc,EACdC,cAAe,KACf1lC,aAAc,CAAC,GAGjB2lC,UAAW,CACTroC,YAAaA,CAAC5B,EAAOM,KACnBN,EAAMhB,SAAWsB,CAAI,EAEvB0oC,cAAehpC,IACbA,EAAMC,aAAeD,EAAMC,WAAW,EAExCiqC,WAAYlqC,IACVA,EAAMf,WAAY,CAAI,EAExBiD,YAAaA,CAAClC,EAAOM,KACnBN,EAAM4pC,SAAWtpC,CAAI,EAEvB6B,eAAgBA,CAACnC,EAAOmN,KACtBnN,EAAM6pC,YAAc18B,CAAG,EAEzBg9B,UAAWA,CAACnqC,EAAOM,KACjBN,EAAM8pC,WAAaxpC,CAAI,EAEzB8pC,YAAaA,CAACpqC,EAAOM,KACnBN,EAAM+pC,aAAezpC,CAAI,EAE3B+pC,iBAAkBA,CAACrqC,EAAOgN,KACxBhN,EAAMgqC,cAAgBh9B,CAAO,EAE/B+6B,gBAAiBA,CAAC/nC,EAAO8nC,KACvB9nC,EAAMsE,aAAewjC,CAAM,GAI/BwC,QAAS,CACP,YAAMC,CAAOC,EAAKlpC,GAEhBkM,QAAQC,IAAInM,GAGZkpC,EAAIC,OAAO,aAIb,K,WCnDJxB,EAAAA,GAAItzB,OAAO+0B,eAAgB,EAC3BzB,EAAAA,GAAIC,IAAIyB,GAAAA,IAEI,IAAI1B,EAAAA,GAAI,CAClBO,OAAM,GACNoB,MAAK,GACLjsC,OAAQksC,GAAKA,EAAEC,KACdC,OAAO,O,uGCfH,MAAM7mB,EAAc,CAAC,MAAO,MAAO,OAAQ,OAAQ,MAAO,OACpD8mB,EAAc,CAAC,MAAO,YAAa,OAAQ,aAC3CC,EAAc,CAAC,MAAO,YAAa,MAAO,OAAQ,O,uBCFxD,SAASpL,EAAgBlT,GAC9B,MAAMue,EAAgB,CACpB,CAAExjC,IAAK,SAAU1F,KAAM,CAAC,SAAU,WAClC,CAAE0F,IAAK,WAAY1F,KAAM,CAAC,WAC1B,CAAE0F,IAAK,QAAS1F,KAAM,CAAC,WAGnBmpC,EAAWA,CAACxe,EAAKhjB,IACdA,EAAMyhC,OAAMC,IACjB,IAAIC,GAAY,EAQhB,OANqB,UAAjBD,EAAKrpC,KAAK,IAAkBqJ,MAAMoY,QAAQkJ,EAAI0e,EAAK3jC,OAE5C2jC,EAAKrpC,KAAK6X,MAAKzT,GAAKA,WAAaumB,EAAI0e,EAAK3jC,UADnD4jC,GAAY,GAKPA,GAAaC,EAAiB5e,EAAK0e,EAAK3jC,IAAI,IAIvD,IAAKyjC,EAASxe,EAAKue,GACjB,MAAM,IAAIvd,MAAM,oGAEpB,CAEO,SAASmS,EAAsBnT,GACpC,MAAM6e,EAAuB,CAC3B,CAAE9jC,IAAK,MAAO1F,KAAM,CAAC,SAAU,WAC/B,CAAE0F,IAAK,WAAY1F,KAAM,CAAC,YAGtBmpC,EAAWA,CAACxe,EAAKhjB,IACdA,EAAMyhC,OAAMC,IACjB,MAAMC,EAAYD,EAAKrpC,KAAK6X,MAAKzT,GAAKA,WAAaumB,EAAI0e,EAAK3jC,OAC5D,OAAO4jC,GAAaC,EAAiB5e,EAAK0e,EAAK3jC,IAAI,IAIvD,IAAKyjC,EAASxe,EAAK6e,GACjB,MAAM,IAAI7d,MAAM,0FAEpB,CAEO,SAASzB,EAAmBS,GACjC,MAAM8e,EAAmB,CACvB,CAAE/jC,IAAK,MAAO1F,KAAM,CAAC,SAAU,WAC/B,CAAE0F,IAAK,UAAW1F,KAAM,CAAC,SAAU,UAAWkL,UAAU,GACxD,CAAExF,IAAK,WAAY1F,KAAM,CAAC,SAAU,YAGhCmpC,EAAWA,CAACxe,EAAKhjB,IACdA,EAAMyhC,OAAMC,IACjB,IAAKA,GAAMn+B,SACT,OAAO,EAGT,MAAMo+B,EAAYD,EAAKrpC,KAAK6X,MAAKzT,GAAKA,WAAaumB,EAAI0e,EAAK3jC,OAC5D,OAAO4jC,GAAaC,EAAiB5e,EAAK0e,EAAK3jC,IAAI,IAIvD,IAAKyjC,EAASxe,EAAK8e,GACjB,MAAM,IAAI9d,MACR,uHAGN,CAEA,SAAS4d,EAAiB5e,EAAKjlB,GAC7B,OAAOuF,OAAOy+B,UAAUC,eAAeC,KAAKjf,EAAKjlB,IAAqB,OAAbilB,EAAIjlB,SAA8BmkC,IAAblf,EAAIjlB,EACpF,C,mWCrEA,SAASokC,EAAeppB,EAAOtY,GAC7B,GAAKA,GAASA,EAAKyK,UACnB,OAAO6N,EAAMxE,MAAK9X,GAAKgE,EAAKyK,UAAUiG,cAAcS,SAASnV,IAC/D,CAEO,SAASgf,EAAYhb,GAC1B,OAAO0hC,EAAe5nB,EAAAA,GAAa9Z,EACrC,CAEO,SAASib,EAAYjb,GAC1B,OAAO0hC,EAAed,EAAAA,GAAa5gC,EACrC,CAEO,SAASme,EAAiBne,GAC/B,OAAO0hC,EAAe5nB,EAAAA,GAAa9Z,IAAS0hC,EAAed,EAAAA,GAAa5gC,EAC1E,CAEO,SAAS4d,EAAY5d,GAC1B,OAAO0hC,EAAeb,EAAAA,GAAa7gC,EACrC,CAEO,SAASyU,EAAUzU,GACxB,OAAO0hC,EAAe,CAAC,OAAQ1hC,EACjC,C,sBCzBO,SAAS8nB,IACd,MAAM6Z,EAAYC,IAEZC,EAAgBF,EAAU7pB,OAAO,EAAG,GAE1C,MACE,sVAAsV2B,KACpVkoB,IAEF,8+CAA8+CloB,KAC5+CooB,EAGN,CAEA,SAASD,IACP,MAAMD,EAAYzb,UAAUyb,WAAazb,UAAU4b,QAAUjrC,OAAOkrC,OAAS,KAE7E,IAAKJ,EAAW,MAAM,IAAIpe,MAAM,8CAEhC,OAAOoe,CACT,CAEO,SAAS5Z,IACd,MACE,CAAC,OAAQ,SAAU,QAAQ5W,SAAS+U,UAAU8b,WAC7C9b,UAAUyb,UAAUxwB,SAAS,QAAU,eAAgB/Q,QAE5D,C,mFC3BI6hC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBV,IAAjBW,EACH,OAAOA,EAAaC,QAGrB,IAAIC,EAASL,EAAyBE,GAAY,CAGjDE,QAAS,CAAC,GAOX,OAHAE,EAAoBJ,GAAUX,KAAKc,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAGpEI,EAAOD,OACf,CAGAH,EAAoBx9B,EAAI69B,E,WCzBxB,IAAIC,EAAW,GACfN,EAAoBO,EAAI,SAASlsB,EAAQmsB,EAAUnlC,EAAIolC,GACtD,IAAGD,EAAH,CAMA,IAAIE,EAAeC,IACnB,IAASxlC,EAAI,EAAGA,EAAImlC,EAAShhC,OAAQnE,IAAK,CACrCqlC,EAAWF,EAASnlC,GAAG,GACvBE,EAAKilC,EAASnlC,GAAG,GACjBslC,EAAWH,EAASnlC,GAAG,GAE3B,IAJA,IAGIylC,GAAY,EACPC,EAAI,EAAGA,EAAIL,EAASlhC,OAAQuhC,MACpB,EAAXJ,GAAsBC,GAAgBD,IAAa9/B,OAAO2L,KAAK0zB,EAAoBO,GAAGzB,OAAM,SAAS1jC,GAAO,OAAO4kC,EAAoBO,EAAEnlC,GAAKolC,EAASK,GAAK,IAChKL,EAAS/S,OAAOoT,IAAK,IAErBD,GAAY,EACTH,EAAWC,IAAcA,EAAeD,IAG7C,GAAGG,EAAW,CACbN,EAAS7S,OAAOtyB,IAAK,GACrB,IAAIu5B,EAAIr5B,SACEkkC,IAAN7K,IAAiBrgB,EAASqgB,EAC/B,CACD,CACA,OAAOrgB,CArBP,CAJCosB,EAAWA,GAAY,EACvB,IAAI,IAAItlC,EAAImlC,EAAShhC,OAAQnE,EAAI,GAAKmlC,EAASnlC,EAAI,GAAG,GAAKslC,EAAUtlC,IAAKmlC,EAASnlC,GAAKmlC,EAASnlC,EAAI,GACrGmlC,EAASnlC,GAAK,CAACqlC,EAAUnlC,EAAIolC,EAwB/B,C,eC5BAT,EAAoBc,EAAI,SAASV,GAChC,IAAIW,EAASX,GAAUA,EAAOY,WAC7B,WAAa,OAAOZ,EAAO,UAAY,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAJ,EAAoBiB,EAAEF,EAAQ,CAAEjgC,EAAGigC,IAC5BA,CACR,C,eCNAf,EAAoBiB,EAAI,SAASd,EAASe,GACzC,IAAI,IAAI9lC,KAAO8lC,EACXlB,EAAoBmB,EAAED,EAAY9lC,KAAS4kC,EAAoBmB,EAAEhB,EAAS/kC,IAC5EuF,OAAOygC,eAAejB,EAAS/kC,EAAK,CAAEimC,YAAY,EAAMnsC,IAAKgsC,EAAW9lC,IAG3E,C,eCPA4kC,EAAoBsB,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOhvC,MAAQ,IAAIuM,SAAS,cAAb,EAChB,CAAE,MAAOrJ,GACR,GAAsB,kBAAXd,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxBqrC,EAAoBmB,EAAI,SAAS9gB,EAAK0e,GAAQ,OAAOp+B,OAAOy+B,UAAUC,eAAeC,KAAKjf,EAAK0e,EAAO,C,eCCtGiB,EAAoBtL,EAAI,SAASyL,GACX,qBAAXqB,QAA0BA,OAAOC,aAC1C9gC,OAAOygC,eAAejB,EAASqB,OAAOC,YAAa,CAAEvhC,MAAO,WAE7DS,OAAOygC,eAAejB,EAAS,aAAc,CAAEjgC,OAAO,GACvD,C,eCNA8/B,EAAoB0B,EAAI,Q,eCKxB,IAAIC,EAAkB,CACrB,IAAK,GAaN3B,EAAoBO,EAAEM,EAAI,SAASe,GAAW,OAAoC,IAA7BD,EAAgBC,EAAgB,EAGrF,IAAIC,EAAuB,SAASC,EAA4B9tC,GAC/D,IAKIisC,EAAU2B,EALVpB,EAAWxsC,EAAK,GAChB+tC,EAAc/tC,EAAK,GACnBguC,EAAUhuC,EAAK,GAGImH,EAAI,EAC3B,GAAGqlC,EAAS5uB,MAAK,SAASnV,GAAM,OAA+B,IAAxBklC,EAAgBllC,EAAW,IAAI,CACrE,IAAIwjC,KAAY8B,EACZ/B,EAAoBmB,EAAEY,EAAa9B,KACrCD,EAAoBx9B,EAAEy9B,GAAY8B,EAAY9B,IAGhD,GAAG+B,EAAS,IAAI3tB,EAAS2tB,EAAQhC,EAClC,CAEA,IADG8B,GAA4BA,EAA2B9tC,GACrDmH,EAAIqlC,EAASlhC,OAAQnE,IACzBymC,EAAUpB,EAASrlC,GAChB6kC,EAAoBmB,EAAEQ,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAO5B,EAAoBO,EAAElsB,EAC9B,EAEI4tB,EAAqBC,KAAK,wBAA0BA,KAAK,yBAA2B,GACxFD,EAAmBzsB,QAAQqsB,EAAqBzd,KAAK,KAAM,IAC3D6d,EAAmBlsB,KAAO8rB,EAAqBzd,KAAK,KAAM6d,EAAmBlsB,KAAKqO,KAAK6d,G,IC/CvF,IAAIE,EAAsBnC,EAAoBO,OAAEhB,EAAW,CAAC,MAAM,WAAa,OAAOS,EAAoB,KAAO,IACjHmC,EAAsBnC,EAAoBO,EAAE4B,E", "sources": ["webpack://chat-app/./src/App.vue", "webpack://chat-app/./src/views/Logout.vue", "webpack://chat-app/src/views/Logout.vue", "webpack://chat-app/./src/views/Logout.vue?c44d", "webpack://chat-app/./src/views/Logout.vue?190f", "webpack://chat-app/./src/Layout/Layout.vue", "webpack://chat-app/src/Layout/Layout.vue", "webpack://chat-app/./src/Layout/Layout.vue?ef49", "webpack://chat-app/./src/Layout/Layout.vue?c114", "webpack://chat-app/src/App.vue", "webpack://chat-app/./src/App.vue?c036", "webpack://chat-app/./src/App.vue?0e40", "webpack://chat-app/./src/views/Activities.vue", "webpack://chat-app/./src/vuechat/lib/ChatWindow.vue", "webpack://chat-app/./src/vuechat/components/Carousel/Carousel.vue", "webpack://chat-app/./src/vuechat/components/SvgIcon/SvgIcon.vue", "webpack://chat-app/src/vuechat/components/SvgIcon/SvgIcon.vue", "webpack://chat-app/./src/vuechat/components/SvgIcon/SvgIcon.vue?e016", "webpack://chat-app/./src/vuechat/components/SvgIcon/SvgIcon.vue?c566", "webpack://chat-app/src/vuechat/components/Carousel/Carousel.vue", "webpack://chat-app/./src/vuechat/components/Carousel/Carousel.vue?ac16", "webpack://chat-app/./src/vuechat/components/Carousel/Carousel.vue?a990", "webpack://chat-app/./src/vuechat/components/ErrorModal/ErrorModal.vue", "webpack://chat-app/src/vuechat/components/ErrorModal/ErrorModal.vue", "webpack://chat-app/./src/vuechat/components/ErrorModal/ErrorModal.vue?2c6b", "webpack://chat-app/./src/vuechat/components/ErrorModal/ErrorModal.vue?cc36", "webpack://chat-app/./src/vuechat/components/SuccessModal/SuccessModal.vue", "webpack://chat-app/src/vuechat/components/SuccessModal/SuccessModal.vue", "webpack://chat-app/./src/vuechat/components/SuccessModal/SuccessModal.vue?203d", "webpack://chat-app/./src/vuechat/components/SuccessModal/SuccessModal.vue?1b04", "webpack://chat-app/./src/vuechat/components/ImageViewer/ImageViewer.vue", "webpack://chat-app/src/vuechat/components/ImageViewer/ImageViewer.vue", "webpack://chat-app/./src/vuechat/components/ImageViewer/ImageViewer.vue?f11c", "webpack://chat-app/./src/vuechat/components/ImageViewer/ImageViewer.vue?2ac6", "webpack://chat-app/./src/vuechat/lib/Room/Room.vue", "webpack://chat-app/./src/vuechat/components/Loader/Loader.vue", "webpack://chat-app/src/vuechat/components/Loader/Loader.vue", "webpack://chat-app/./src/vuechat/components/Loader/Loader.vue?1440", "webpack://chat-app/./src/vuechat/components/Loader/Loader.vue?b806", "webpack://chat-app/./src/vuechat/components/EmojiPickerContainer/EmojiPickerContainer.vue", "webpack://chat-app/src/vuechat/components/EmojiPickerContainer/EmojiPickerContainer.vue", "webpack://chat-app/./src/vuechat/components/EmojiPickerContainer/EmojiPickerContainer.vue?8cc3", "webpack://chat-app/./src/vuechat/components/EmojiPickerContainer/EmojiPickerContainer.vue?3064", "webpack://chat-app/./src/vuechat/components/UploadModal/UploadModal.vue", "webpack://chat-app/src/vuechat/components/UploadModal/UploadModal.vue", "webpack://chat-app/./src/vuechat/components/UploadModal/UploadModal.vue?9c83", "webpack://chat-app/./src/vuechat/components/UploadModal/UploadModal.vue?5ac7", "webpack://chat-app/./src/vuechat/components/InfoModal/Info.vue", "webpack://chat-app/src/vuechat/components/InfoModal/Info.vue", "webpack://chat-app/./src/vuechat/components/InfoModal/Info.vue?e0df", "webpack://chat-app/./src/vuechat/components/InfoModal/Info.vue?4c89", "webpack://chat-app/./src/vuechat/lib/Room/RoomHeader/RoomHeader.vue", "webpack://chat-app/./src/vuechat/utils/typing-text.js", "webpack://chat-app/./src/vuechat/components/TemplateModal/TemplateModal.vue", "webpack://chat-app/./src/utils/config.js", "webpack://chat-app/./src/utils/api.js", "webpack://chat-app/src/vuechat/components/TemplateModal/TemplateModal.vue", "webpack://chat-app/./src/vuechat/components/TemplateModal/TemplateModal.vue?d335", "webpack://chat-app/./src/vuechat/components/TemplateModal/TemplateModal.vue?d6cc", "webpack://chat-app/src/vuechat/lib/Room/RoomHeader/RoomHeader.vue", "webpack://chat-app/./src/vuechat/lib/Room/RoomHeader/RoomHeader.vue?7870", "webpack://chat-app/./src/vuechat/lib/Room/RoomHeader/RoomHeader.vue?add9", "webpack://chat-app/./src/vuechat/lib/Room/RoomFiles/RoomFiles.vue", "webpack://chat-app/./src/vuechat/lib/Room/RoomFile/RoomFile.vue", "webpack://chat-app/src/vuechat/lib/Room/RoomFile/RoomFile.vue", "webpack://chat-app/./src/vuechat/lib/Room/RoomFile/RoomFile.vue?16d8", "webpack://chat-app/./src/vuechat/lib/Room/RoomFile/RoomFile.vue?69da", "webpack://chat-app/src/vuechat/lib/Room/RoomFiles/RoomFiles.vue", "webpack://chat-app/./src/vuechat/lib/Room/RoomFiles/RoomFiles.vue?3b0d", "webpack://chat-app/./src/vuechat/lib/Room/RoomFiles/RoomFiles.vue?1dfe", "webpack://chat-app/./src/vuechat/lib/Room/RoomMessageReply/RoomMessageReply.vue", "webpack://chat-app/./src/vuechat/components/FormatMessage/FormatMessage.vue", "webpack://chat-app/./src/vuechat/utils/format-string.js", "webpack://chat-app/src/vuechat/components/FormatMessage/FormatMessage.vue", "webpack://chat-app/./src/vuechat/components/FormatMessage/FormatMessage.vue?6144", "webpack://chat-app/./src/vuechat/components/FormatMessage/FormatMessage.vue?e6b3", "webpack://chat-app/src/vuechat/lib/Room/RoomMessageReply/RoomMessageReply.vue", "webpack://chat-app/./src/vuechat/lib/Room/RoomMessageReply/RoomMessageReply.vue?75f7", "webpack://chat-app/./src/vuechat/lib/Room/RoomMessageReply/RoomMessageReply.vue?8bfa", "webpack://chat-app/./src/vuechat/lib/Room/RoomEmojis/RoomEmojis.vue", "webpack://chat-app/src/vuechat/lib/Room/RoomEmojis/RoomEmojis.vue", "webpack://chat-app/./src/vuechat/lib/Room/RoomEmojis/RoomEmojis.vue?c283", "webpack://chat-app/./src/vuechat/lib/Room/RoomEmojis/RoomEmojis.vue?2e50", "webpack://chat-app/./src/vuechat/lib/Message/Message.vue", "webpack://chat-app/./src/vuechat/lib/Message/MessageReply/MessageReply.vue", "webpack://chat-app/src/vuechat/lib/Message/MessageReply/MessageReply.vue", "webpack://chat-app/./src/vuechat/lib/Message/MessageReply/MessageReply.vue?d3f0", "webpack://chat-app/./src/vuechat/lib/Message/MessageReply/MessageReply.vue?dd9a", "webpack://chat-app/./src/vuechat/lib/Message/MessageFiles/MessageFiles.vue", "webpack://chat-app/./src/vuechat/lib/Message/MessageFile/MessageFile.vue", "webpack://chat-app/./src/vuechat/components/UploadState/UploadState.vue", "webpack://chat-app/src/vuechat/components/UploadState/UploadState.vue", "webpack://chat-app/./src/vuechat/components/UploadState/UploadState.vue?d162", "webpack://chat-app/./src/vuechat/components/UploadState/UploadState.vue?6c7a", "webpack://chat-app/src/vuechat/lib/Message/MessageFile/MessageFile.vue", "webpack://chat-app/./src/vuechat/lib/Message/MessageFile/MessageFile.vue?b257", "webpack://chat-app/./src/vuechat/lib/Message/MessageFile/MessageFile.vue?f323", "webpack://chat-app/src/vuechat/lib/Message/MessageFiles/MessageFiles.vue", "webpack://chat-app/./src/vuechat/lib/Message/MessageFiles/MessageFiles.vue?8d3a", "webpack://chat-app/./src/vuechat/lib/Message/MessageFiles/MessageFiles.vue?366c", "webpack://chat-app/./src/vuechat/lib/Message/MessageActions/MessageActions.vue", "webpack://chat-app/src/vuechat/lib/Message/MessageActions/MessageActions.vue", "webpack://chat-app/./src/vuechat/lib/Message/MessageActions/MessageActions.vue?70db", "webpack://chat-app/./src/vuechat/lib/Message/MessageActions/MessageActions.vue?65fa", "webpack://chat-app/./src/vuechat/lib/Message/MessageReactions/MessageReactions.vue", "webpack://chat-app/src/vuechat/lib/Message/MessageReactions/MessageReactions.vue", "webpack://chat-app/./src/vuechat/lib/Message/MessageReactions/MessageReactions.vue?02db", "webpack://chat-app/./src/vuechat/lib/Message/MessageReactions/MessageReactions.vue?3d55", "webpack://chat-app/./src/vuechat/lib/Message/AudioPlayer/AudioPlayer.vue", "webpack://chat-app/./src/vuechat/lib/Message/AudioControl/AudioControl.vue", "webpack://chat-app/src/vuechat/lib/Message/AudioControl/AudioControl.vue", "webpack://chat-app/./src/vuechat/lib/Message/AudioControl/AudioControl.vue?b06f", "webpack://chat-app/./src/vuechat/lib/Message/AudioControl/AudioControl.vue?20bd", "webpack://chat-app/src/vuechat/lib/Message/AudioPlayer/AudioPlayer.vue", "webpack://chat-app/./src/vuechat/lib/Message/AudioPlayer/AudioPlayer.vue?4dd0", "webpack://chat-app/./src/vuechat/lib/Message/AudioPlayer/AudioPlayer.vue?52bf", "webpack://chat-app/src/vuechat/lib/Message/Message.vue", "webpack://chat-app/./src/vuechat/lib/Message/Message.vue?28d2", "webpack://chat-app/./src/vuechat/lib/Message/Message.vue?64fa", "webpack://chat-app/./src/vuechat/utils/filter-items.js", "webpack://chat-app/./src/vuechat/utils/mp3-encoder.js", "webpack://chat-app/./src/vuechat/utils/recorder.js", "webpack://chat-app/src/vuechat/lib/Room/Room.vue", "webpack://chat-app/./src/vuechat/lib/Room/Room.vue?6b31", "webpack://chat-app/./src/vuechat/lib/Room/Room.vue?189b", "webpack://chat-app/./src/vuechat/locales/index.js", "webpack://chat-app/./src/vuechat/themes/index.js", "webpack://chat-app/src/vuechat/lib/ChatWindow.vue", "webpack://chat-app/./src/vuechat/lib/ChatWindow.vue?6288", "webpack://chat-app/./src/vuechat/lib/ChatWindow.vue?01ed", "webpack://chat-app/./src/components/Spinner.vue", "webpack://chat-app/src/components/Spinner.vue", "webpack://chat-app/./src/components/Spinner.vue?c71c", "webpack://chat-app/./src/components/Spinner.vue?b6e3", "webpack://chat-app/./src/utils/utils.js", "webpack://chat-app/./src/utils/dates.js", "webpack://chat-app/src/views/Activities.vue", "webpack://chat-app/./src/views/Activities.vue?d31a", "webpack://chat-app/./src/views/Activities.vue?0dad", "webpack://chat-app/./src/router/index.js", "webpack://chat-app/./src/store/index.js", "webpack://chat-app/./src/main.js", "webpack://chat-app/./src/vuechat/utils/constants.js", "webpack://chat-app/./src/vuechat/utils/data-validation.js", "webpack://chat-app/./src/vuechat/utils/media-file.js", "webpack://chat-app/./src/vuechat/utils/mobile-detection.js", "webpack://chat-app/webpack/bootstrap", "webpack://chat-app/webpack/runtime/chunk loaded", "webpack://chat-app/webpack/runtime/compat get default export", "webpack://chat-app/webpack/runtime/define property getters", "webpack://chat-app/webpack/runtime/global", "webpack://chat-app/webpack/runtime/hasOwnProperty shorthand", "webpack://chat-app/webpack/runtime/make namespace object", "webpack://chat-app/webpack/runtime/publicPath", "webpack://chat-app/webpack/runtime/jsonp chunk loading", "webpack://chat-app/webpack/startup"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[(!_vm.userData || _vm.loggedOut)?_c('app-logout'):_c('app-layout')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _vm._m(0)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"logout-view\"},[_c('h1',[_vm._v(\"You are not logged in.\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"logout-view\">\n    <h1>You are not logged in.</h1>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Logout'\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.logout-view {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100vh;\n\n  h1 {\n    font-size: 5rem;\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Logout.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Logout.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Logout.vue?vue&type=template&id=55bdeeee&scoped=true\"\nimport script from \"./Logout.vue?vue&type=script&lang=js\"\nexport * from \"./Logout.vue?vue&type=script&lang=js\"\nimport style0 from \"./Logout.vue?vue&type=style&index=0&id=55bdeeee&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"55bdeeee\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('div',{staticClass:\"app-layout\",class:{ mod: _vm.modify }},[_c('div',{staticClass:\"app-layout_main\",class:{ inactive: !_vm.active }},[_c('router-view')],1)])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div>\n    <div class=\"app-layout\" :class=\"{ mod: modify }\">\n      <!-- <app-header /> -->\n\n      <div class=\"app-layout_main\" :class=\"{ inactive: !active }\">\n        <router-view></router-view>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\n\nexport default {\n  name: 'Layout',\n  components: {  },\n  computed: {\n    active() {\n      return this.$store.state.menuBarShow\n    },\n    modify() {\n      const pathName = this.$route.name\n      if (pathName === 'Activities') {\n        return true\n      }\n      return false\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Layout.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Layout.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Layout.vue?vue&type=template&id=90f984b8\"\nimport script from \"./Layout.vue?vue&type=script&lang=js\"\nexport * from \"./Layout.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div>\n    <app-logout v-if=\"!userData || loggedOut\" />\n    <app-layout v-else />\n  </div>\n</template>\n\n<script>\nimport { mapMutations, mapState } from \"vuex\";\nimport AppLogout from \"@/views/Logout.vue\";\nimport AppLayout from \"@/Layout/Layout\";\nimport dummyData from \"@/utils/dummy_data.json\";\nimport \"floating-vue/dist/style.css\";\nimport \"@/assets/styles/main.scss\";\n\nexport default {\n  name: \"App\",\n  components: { AppLayout, AppLogout },\n\n  data() {\n    return {\n      testing: false,\n      interval: null,\n    };\n  },\n\n  computed: {\n    ...mapState([\"userData\", \"loggedOut\"]),\n  },\n\n  watch: {\n    loggedOut() {\n      if (this.loggedOut) {\n        clearInterval(this.interval);\n      }\n    },\n  },\n\n  methods: {\n    ...mapMutations([\n      \"setUserData\",\n      \"setErrorApp\",\n      \"setErrorMsgApp\",\n      \"setBanner\",\n      \"setConflict\",\n      \"setBannerContent\",\n    ]),\n\n    initApp() {\n      let user = {};\n      var urlString = window.location.href;\n      var url = new URL(urlString);\n      user.user_id = url.searchParams.get(\"user_id\");\n      user.portal_id = url.searchParams.get(\"portal_id\");\n      user.accountUser = url.searchParams.get(\"accountUser\");\n      user.accountPhone = url.searchParams.get(\"accountPhone\");\n\n      if (this.testing) {\n        this.setUserData(dummyData);\n        return;\n      }\n\n      this.setUserData(user);\n    },\n\n    updateOnlineStatus(e) {\n      const { type } = e;\n      if (type === \"online\") {\n        window.location.reload();\n      } else {\n        this.setErrorApp(true);\n        this.setErrorMsgApp(\"You are offline!\");\n      }\n    },\n  },\n\n  mounted() {\n    // init data\n    this.initApp();\n  },\n};\n</script>\n", "import mod from \"-!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=52a25caa\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"activities-main-div\"},[(!_vm.dataLoaded)?_c('spinner'):(_vm.noNeedToPermission || (_vm.isAdmin || _vm.isPermission))?_c('chat-window',{attrs:{\"user-data\":_vm.userData,\"current-user-id\":_vm.currentUserId,\"rooms\":_vm.rooms,\"engagement\":_vm.engagement,\"participants\":_vm.participants,\"loadingTab\":_vm.loadingTab,\"showAddToHubspot\":_vm.showAddToHubspot,\"showParticipants\":_vm.showParticipants,\"setContactObjects\":_vm.setContactObjects,\"rooms-loaded\":_vm.roomsLoaded,\"messages\":_vm.messages,\"messages-loaded\":_vm.messagesLoaded,\"selected-room\":_vm.selectedRoom,\"requests\":_vm.requests,\"request\":_vm.request,\"labels\":_vm.labels,\"show-labels\":_vm.showLabels,\"room-labels\":_vm.roomLabels,\"assigning-label\":_vm.assigningLabel,\"templates\":_vm.templates,\"error-message\":_vm.errorMessage,\"success-message\":_vm.successMessage,\"save-key\":_vm.saveKey,\"show-error-modal\":_vm.showErrorModal,\"show-create-modal\":_vm.showCreateModal,\"show-success-modal\":_vm.showSuccessModal,\"toggle-error-modal\":_vm.toggleErrorModal,\"toggle-success-modal\":_vm.toggleSuccessModal,\"loading-rooms\":_vm.loadingRooms,\"unread-counts\":_vm.unreadCounts,\"messageInTransit\":_vm.messageInTransit,\"sidebar-visible\":_vm.menuBarShow,\"isMsgFetched\":_vm.isMsgFetched},on:{\"room-action-handler\":_vm.roomActionHandler,\"send-message\":_vm.sendMessage,\"fetch-messages\":_vm.fetchMessages,\"open-file\":_vm.openFile,\"toggle-labels-modal\":_vm.toggleLabelsModal,\"close-sidebar\":_vm.closeSideBar,\"redirect-to-hubspot\":_vm.redirectToHubspot,\"add-template-msg\":_vm.addTemplateMsg}}):_c('Info',{attrs:{\"infoMsg\":\"You don't have chat access, Please ask admin for access.\"}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mw-100\"},[_c('div',{staticClass:\"vac-card-window\",style:([_vm.cssVars])},[_c('div',{staticClass:\"vac-chat-container\",class:{ 'profile-visible': _vm.showProfile }},[_c('room',{attrs:{\"current-user-id\":_vm.currentUserId,\"rooms\":_vm.rooms,\"room-id\":_vm.room.roomId || '',\"load-first-room\":_vm.loadFirstRoom,\"messages\":_vm.messages,\"room-message\":_vm.roomMessage,\"messages-loaded\":_vm.messagesLoaded,\"menu-actions\":_vm.menuActions,\"message-actions\":_vm.messageActions,\"show-send-icon\":_vm.showSendIcon,\"show-files\":_vm.showFiles,\"show-audio\":_vm.showAudio,\"audio-bit-rate\":_vm.audioBitRate,\"audio-sample-rate\":_vm.audioSampleRate,\"show-emojis\":_vm.showEmojis,\"show-reaction-emojis\":_vm.showReactionEmojis,\"show-new-messages-divider\":_vm.showNewMessagesDivider,\"show-footer\":_vm.showFooter,\"text-messages\":_vm.t,\"single-room\":_vm.singleRoom,\"show-rooms-list\":_vm.showRoomsList,\"text-formatting\":_vm.textFormatting,\"link-options\":_vm.linkOptions,\"is-mobile\":false,\"loading-rooms\":_vm.loadingRooms,\"textarea-action-enabled\":_vm.textareaActionEnabled,\"accepted-files\":_vm.acceptedFiles,\"templates-text\":_vm.templatesText,\"templates\":_vm.templates,\"isMsgFetched\":_vm.isMsgFetched,\"unread-counts\":_vm.unreadCounts,\"messageInTransit\":_vm.messageInTransit},on:{\"toggle-error-modal\":_vm.toggleErrorModal,\"fetch-messages\":_vm.fetchMessages,\"send-message\":_vm.sendMessage,\"edit-message\":_vm.editMessage,\"delete-message\":_vm.deleteMessage,\"open-file\":_vm.openFile,\"send-message-reaction\":_vm.sendMessageReaction,\"typing-message\":_vm.typingMessage,\"textarea-action-handler\":_vm.textareaActionHandler,\"carousel-handler\":_vm.carouselHandler,\"toggle-menu-bar\":function($event){return _vm.$emit('toggle-menu-bar')},\"add-template-msg\":_vm.forwardTemplateMsg,\"redirect-to-hubspot\":function($event){return _vm.$emit('redirect-to-hubspot', _vm.room)}},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)})],1)]),_c('app-carousel',{attrs:{\"show\":_vm.showCarousel,\"close\":_vm.closeCarousel,\"images\":_vm.carouselData}}),_c('error-modal',{attrs:{\"show\":_vm.showErrorModal,\"toggle\":_vm.toggleErrorModal,\"error-message\":this.errorMessage}}),_c('success-modal',{attrs:{\"show\":_vm.showSuccessModal,\"toggle\":_vm.toggleSuccessModal,\"success-message\":_vm.successMessage}}),_c('image-viewer',{attrs:{\"show\":_vm.previewImage,\"msg\":_vm.previewMessage,\"close\":_vm.closeImageViewer}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return (_vm.show)?_c('div',{staticClass:\"app-carousel\",attrs:{\"id\":\"carousel\"}},[_c('div',{staticClass:\"app-carousel-topbar\"},[_c('div',{staticClass:\"image-details\"},[_c('span',{staticClass:\"image-name\"},[_vm._v(_vm._s(_vm.senderName))]),_c('span',{staticClass:\"image-time\"},[_vm._v(_vm._s(_vm.imageTime))])]),_c('div',{staticClass:\"d-flex align-items-center\"},[_c('div',{on:{\"click\":function($event){return _vm.$emit('open-forward-modal', { _id: _vm.images[_vm.activeImage].msg_id })}}},[_c('svg',{staticClass:\"forward-icon\",attrs:{\"viewBox\":\"0 0 24 24\",\"width\":\"24\",\"height\":\"24\"}},[_c('path',{attrs:{\"fill\":\"currentColor\",\"d\":\"M14.278 4.813c0-.723.873-1.085 1.383-.574l6.045 6.051a.81.81 0 0 1 0 1.146l-6.045 6.051a.81.81 0 0 1-1.383-.574v-2.732c-5.096 0-8.829 1.455-11.604 4.611-.246.279-.702.042-.602-.316 1.43-5.173 4.925-10.004 12.206-11.045V4.813z\"}})])]),_c('div',{on:{\"click\":_vm.openFile}},[_c('svg-icon',{attrs:{\"name\":\"document\"}})],1),_c('button',{staticClass:\"app-carousel-topbar_close\",on:{\"click\":_vm.close}},[_vm._v(\"×\")])])]),_c('div',{staticClass:\"app-carousel-main\"},[_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"carousel-img\"},[_c('img',{attrs:{\"src\":_vm.currentImage,\"alt\":\"imageName\"}})])])]),_c('div',{staticClass:\"app-carousel-thumbnails\"},_vm._l((_vm.images),function(image,index){return _c('div',{key:image.id,class:['thumbnail-image', _vm.activeImage == index ? 'active' : ''],on:{\"click\":function($event){return _vm.activateImage(index)}}},[_c('img',{attrs:{\"src\":image.thumb}})])}),0),_c('div',{staticClass:\"app-carousel-actions\"},[_c('span',{staticClass:\"prev\",on:{\"click\":_vm.prevImage}},[_c('svg',{attrs:{\"viewBox\":\"0 0 30 30\",\"width\":\"30\",\"height\":\"30\"}},[_c('path',{attrs:{\"fill\":\"currentColor\",\"d\":\"M19.214 21.212L12.865 15l6.35-6.35-1.933-1.932L9 15l8.282 8.282 1.932-2.07z\"}})])]),_c('span',{staticClass:\"next\",on:{\"click\":_vm.nextImage}},[_c('svg',{attrs:{\"viewBox\":\"0 0 30 30\",\"width\":\"30\",\"height\":\"30\"}},[_c('path',{attrs:{\"fill\":\"currentColor\",\"d\":\"M11 21.212L17.35 15 11 8.65l1.932-1.932L21.215 15l-8.282 8.282L11 21.212z\"}})])])])]):_vm._e()\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('svg',{attrs:{\"title\":_vm.reason,\"xmlns\":\"http://www.w3.org/2000/svg\",\"xmlns:xlink\":\"http://www.w3.org/1999/xlink\",\"version\":\"1.1\",\"width\":\"24\",\"height\":\"24\",\"viewBox\":`0 0 ${_vm.size} ${_vm.size}`}},[_c('path',{attrs:{\"id\":_vm.svgId,\"d\":_vm.svgItem[_vm.name].path}}),(_vm.svgItem[_vm.name].path2)?_c('path',{attrs:{\"id\":_vm.svgId,\"d\":_vm.svgItem[_vm.name].path2}}):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <svg\n    :title=\"reason\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n    version=\"1.1\"\n    width=\"24\"\n    height=\"24\"\n    :viewBox=\"`0 0 ${size} ${size}`\"\n  >\n    <path :id=\"svgId\" :d=\"svgItem[name].path\" />\n    <path v-if=\"svgItem[name].path2\" :id=\"svgId\" :d=\"svgItem[name].path2\" />\n  </svg>\n</template>\n\n<script>\nexport default {\n  name: 'SvgIcon',\n\n  props: {\n    name: { type: String, default: null },\n    param: { type: String, default: null },\n    reason: { type: String, default: null }\n  },\n\n  data() {\n    return {\n      svgItem: {\n        search: {\n          path: 'M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z'\n        },\n        add: {\n          path: 'M17,13H13V17H11V13H7V11H11V7H13V11H17M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z'\n        },\n        toggle: {\n          path: 'M5,13L9,17L7.6,18.42L1.18,12L7.6,5.58L9,7L5,11H21V13H5M21,6V8H11V6H21M21,16V18H11V16H21Z'\n        },\n\n        menu: {\n          path: 'M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z'\n        },\n        close: {\n          path: 'M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z'\n        },\n        cancel: {\n          path: 'M443.6,387.1L312.4,255.4l131.5-130c5.4-5.4,5.4-14.2,0-19.6l-37.4-37.6c-2.6-2.6-6.1-4-9.8-4c-3.7,0-7.2,1.5-9.8,4  L256,197.8L124.9,68.3c-2.6-2.6-6.1-4-9.8-4c-3.7,0-7.2,1.5-9.8,4L68,105.9c-5.4,5.4-5.4,14.2,0,19.6l131.5,130L68.4,387.1  c-2.6,2.6-4.1,6.1-4.1,9.8c0,3.7,1.4,7.2,4.1,9.8l37.4,37.6c2.7,2.7,6.2,4.1,9.8,4.1c3.5,0,7.1-1.3,9.8-4.1L256,313.1l130.7,131.1  c2.7,2.7,6.2,4.1,9.8,4.1c3.5,0,7.1-1.3,9.8-4.1l37.4-37.6c2.6-2.6,4.1-6.1,4.1-9.8C447.7,393.2,446.2,389.7,443.6,387.1z'\n        },\n        file: {\n          path: 'M14,17H7V15H14M17,13H7V11H17M17,9H7V7H17M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3Z'\n        },\n        paperclip: {\n          path: 'M1.816 15.556v.002c0 1.502.584 2.912 1.646 3.972s2.472 1.647 3.974 1.647a5.58 5.58 0 0 0 3.972-1.645l9.547-9.548c.769-.768 1.147-1.767 1.058-2.817-.079-.968-.548-1.927-1.319-2.698-1.594-1.592-4.068-1.711-5.517-.262l-7.916 7.915c-.881.881-.792 2.25.214 3.261.959.958 2.423 1.053 3.263.215l5.511-5.512c.28-.28.267-.722.053-.936l-.244-.244c-.191-.191-.567-.349-.957.04l-5.506 5.506c-.18.18-.635.127-.976-.214-.098-.097-.576-.613-.213-.973l7.915-7.917c.818-.817 2.267-.699 3.23.262.5.501.802 1.1.849 1.685.051.573-.156 1.111-.589 1.543l-9.547 9.549a3.97 3.97 0 0 1-2.829 1.171 3.975 3.975 0 0 1-2.83-1.173 3.973 3.973 0 0 1-1.172-2.828c0-1.071.415-2.076 1.172-2.83l7.209-7.211c.157-.157.264-.579.028-.814L11.5 4.36a.572.572 0 0 0-.834.018l-7.205 7.207a5.577 5.577 0 0 0-1.645 3.971z'\n        },\n        'close-outline': {\n          path: 'M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z'\n        },\n        send: {\n          path: 'M2,21L23,12L2,3V10L17,12L2,14V21Z'\n        },\n        emoji: {\n          path: 'M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm3.5-9c.83 0 1.5-.67 1.5-1.5S16.33 8 15.5 8 14 8.67 14 9.5s.67 1.5 1.5 1.5zm-7 0c.83 0 1.5-.67 1.5-1.5S9.33 8 8.5 8 7 8.67 7 9.5 7.67 11 8.5 11zm3.5 6.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z'\n        },\n        document: {\n          path: 'M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z'\n        },\n        pencil: {\n          path: 'M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z'\n        },\n        checkmark: {\n          path: 'M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z'\n        },\n        'double-checkmark': {\n          path: 'M18 7l-1.41-1.41-12.59 11.41 2 2L18 7zm4.24-1.41L11.66 16.17 6 11l-1.41 1.41L11.66 19l12-12-1.42-1.41zM.41 13.41 6 19l1.41-1.41L1.83 12 .41 13.41z'\n        },\n        wait: {\n          path: 'M 5.832031 1.042969 C 3.1875 1.042969 1.042969 3.1875 1.042969 5.832031 L 1.042969 14.167969 C 1.042969 16.8125 3.1875 18.957031 5.832031 18.957031 L 14.167969 18.957031 C 16.8125 18.957031 18.957031 16.8125 18.957031 14.167969 L 18.957031 5.832031 C 18.957031 3.1875 16.8125 1.042969 14.167969 1.042969 Z M 5.832031 1.042969 M 10 3.542969 C 10.34375 3.542969 10.625 3.820312 10.625 4.167969 L 10.625 9.375 L 15.832031 9.375 C 16.179688 9.375 16.457031 9.65625 16.457031 10 C 16.457031 10.34375 16.179688 10.625 15.832031 10.625 L 10 10.625 C 9.65625 10.625 9.375 10.34375 9.375 10 L 9.375 4.167969 C 9.375 3.820312 9.65625 3.542969 10 3.542969 Z M 10 3.542969  '\n        },\n        eye: {\n          path: 'M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z'\n        },\n        error: {\n          path: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2zm0-4h-2V7h2z'\n          // path2: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2zm0-4h-2V7h2z'\n        },\n        dropdown: {\n          path: 'M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z'\n        },\n        deleted: {\n          path: 'M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12C4,13.85 4.63,15.55 5.68,16.91L16.91,5.68C15.55,4.63 13.85,4 12,4M12,20A8,8 0 0,0 20,12C20,10.15 19.37,8.45 18.32,7.09L7.09,18.32C8.45,19.37 10.15,20 12,20Z'\n        },\n        microphone: {\n          size: 'large',\n          path: 'M432.8,216.4v39.2c0,45.2-15.3,84.3-45.2,118.4c-29.8,33.2-67.3,52.8-111.6,57.9v40.9h78.4c5.1,0,10.2,1.7,13.6,6c4.3,4.3,6,8.5,6,13.6c0,5.1-1.7,10.2-6,13.6c-4.3,4.3-8.5,6-13.6,6H157.6c-5.1,0-10.2-1.7-13.6-6c-4.3-4.3-6-8.5-6-13.6c0-5.1,1.7-10.2,6-13.6c4.3-4.3,8.5-6,13.6-6H236v-40.9c-44.3-5.1-81.8-23.9-111.6-57.9s-45.2-73.3-45.2-118.4v-39.2c0-5.1,1.7-10.2,6-13.6c4.3-4.3,8.5-6,13.6-6s10.2,1.7,13.6,6c4.3,4.3,6,8.5,6,13.6v39.2c0,37.5,13.6,70.7,40,97.1s59.6,40,97.1,40s70.7-13.6,97.1-40c26.4-26.4,40-59.6,40-97.1v-39.2c0-5.1,1.7-10.2,6-13.6c4.3-4.3,8.5-6,13.6-6c5.1,0,10.2,1.7,13.6,6C430.2,206.2,432.8,211.3,432.8,216.4z M353.5,98v157.6c0,27.3-9.4,50.3-29,69c-19.6,19.6-42.6,29-69,29s-50.3-9.4-69-29c-19.6-19.6-29-42.6-29-69V98c0-27.3,9.4-50.3,29-69c19.6-19.6,42.6-29,69-29s50.3,9.4,69,29C344.2,47.7,353.5,71.6,353.5,98z'\n        },\n        'audio-play': {\n          size: 'medium',\n          path: 'M43.331,21.237L7.233,0.397c-0.917-0.529-2.044-0.529-2.96,0c-0.916,0.528-1.48,1.505-1.48,2.563v41.684   c0,1.058,0.564,2.035,1.48,2.563c0.458,0.268,0.969,0.397,1.48,0.397c0.511,0,1.022-0.133,1.48-0.397l36.098-20.84   c0.918-0.529,1.479-1.506,1.479-2.564S44.247,21.767,43.331,21.237z'\n        },\n        'audio-pause': {\n          size: 'medium',\n          path: 'M17.991,40.976c0,3.662-2.969,6.631-6.631,6.631l0,0c-3.662,0-6.631-2.969-6.631-6.631V6.631C4.729,2.969,7.698,0,11.36,0l0,0c3.662,0,6.631,2.969,6.631,6.631V40.976z',\n          path2:\n            'M42.877,40.976c0,3.662-2.969,6.631-6.631,6.631l0,0c-3.662,0-6.631-2.969-6.631-6.631V6.631C29.616,2.969,32.585,0,36.246,0l0,0c3.662,0,6.631,2.969,6.631,6.631V40.976z'\n        }\n      }\n    }\n  },\n\n  computed: {\n    svgId() {\n      const param = this.param ? '-' + this.param : ''\n      return `vac-icon-${this.name}${param}`\n    },\n    size() {\n      const item = this.svgItem[this.name]\n\n      if (item.size === 'large') return 512\n      else if (item.size === 'medium') return 48\n      else return 24\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./SvgIcon.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./SvgIcon.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./SvgIcon.vue?vue&type=template&id=3968ce5e\"\nimport script from \"./SvgIcon.vue?vue&type=script&lang=js\"\nexport * from \"./SvgIcon.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div v-if=\"show\" id=\"carousel\" class=\"app-carousel\">\n    <!-- topbar -->\n    <div class=\"app-carousel-topbar\">\n      <div class=\"image-details\">\n        <span class=\"image-name\">{{ senderName }}</span>\n        <span class=\"image-time\">{{ imageTime }}</span>\n      </div>\n      <div class=\"d-flex align-items-center\">\n        <div @click=\"$emit('open-forward-modal', { _id: images[activeImage].msg_id })\">\n          <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" class=\"forward-icon\">\n            <path\n              fill=\"currentColor\"\n              d=\"M14.278 4.813c0-.723.873-1.085 1.383-.574l6.045 6.051a.81.81 0 0 1 0 1.146l-6.045 6.051a.81.81 0 0 1-1.383-.574v-2.732c-5.096 0-8.829 1.455-11.604 4.611-.246.279-.702.042-.602-.316 1.43-5.173 4.925-10.004 12.206-11.045V4.813z\"\n            />\n          </svg>\n        </div>\n        <div @click=\"openFile\">\n          <svg-icon name=\"document\" />\n        </div>\n        <button class=\"app-carousel-topbar_close\" @click=\"close\">&times;</button>\n      </div>\n    </div>\n\n    <div class=\"app-carousel-main\">\n      <div class=\"content\">\n        <div class=\"carousel-img\">\n          <img :src=\"currentImage\" alt=\"imageName\" />\n        </div>\n      </div>\n    </div>\n\n    <div class=\"app-carousel-thumbnails\">\n      <div\n        v-for=\"(image, index) in images\"\n        :key=\"image.id\"\n        :class=\"['thumbnail-image', activeImage == index ? 'active' : '']\"\n        @click=\"activateImage(index)\"\n      >\n        <img :src=\"image.thumb\" />\n      </div>\n    </div>\n\n    <div class=\"app-carousel-actions\">\n      <span class=\"prev\" @click=\"prevImage\">\n        <svg viewBox=\"0 0 30 30\" width=\"30\" height=\"30\" class=\"\">\n          <path fill=\"currentColor\" d=\"M19.214 21.212L12.865 15l6.35-6.35-1.933-1.932L9 15l8.282 8.282 1.932-2.07z\" />\n        </svg>\n      </span>\n      <span class=\"next\" @click=\"nextImage\">\n        <svg viewBox=\"0 0 30 30\" width=\"30\" height=\"30\" class=\"\">\n          <path fill=\"currentColor\" d=\"M11 21.212L17.35 15 11 8.65l1.932-1.932L21.215 15l-8.282 8.282L11 21.212z\" />\n        </svg>\n      </span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport SvgIcon from '../../components/SvgIcon/SvgIcon'\nexport default {\n  name: 'Carousel',\n  components: { SvgIcon },\n  props: {\n    show: { type: Boolean },\n    close: { type: Function, default: () => ({}) },\n    images: { type: Array, default: () => [] }\n  },\n  emits: ['open-forward-modal'],\n  data() {\n    return {\n      activeImage: 0\n    }\n  },\n  computed: {\n    currentImage() {\n      return this.images[this.activeImage]?.big\n    },\n    senderName() {\n      return this.images[this.activeImage]?.username\n    },\n    imageName() {\n      return this.images[this.activeImage]?.name\n    },\n    imageTime() {\n      return this.images[this.activeImage]?.date + ' at ' + this.images[this.activeImage]?.timestamp\n    }\n  },\n  methods: {\n    nextImage() {\n      let active = this.activeImage + 1\n      if (active >= this.images.length) {\n        active = 0\n      }\n      this.activateImage(active)\n    },\n    prevImage() {\n      let active = this.activeImage - 1\n      if (active < 0) {\n        active = this.images.length - 1\n      }\n      this.activateImage(active)\n    },\n    activateImage(imageIndex) {\n      this.activeImage = imageIndex\n    },\n    async openFile() {\n      const image = await fetch(this.currentImage)\n      const blob = await image.blob()\n      const link = document.createElement('a')\n      link.href = URL.createObjectURL(blob)\n      link.download = this.imageName\n      link.click()\n      URL.revokeObjectURL(link.href)\n    }\n  }\n}\n</script>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Carousel.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Carousel.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Carousel.vue?vue&type=template&id=3d6dbaaa\"\nimport script from \"./Carousel.vue?vue&type=script&lang=js\"\nexport * from \"./Carousel.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.show),expression:\"show\"}],staticClass:\"error-modal\"},[_c('transition',{attrs:{\"name\":\"vac-bounce\"}},[(_vm.show)?_c('div',{staticClass:\"error-modal-div\"},[_c('img',{attrs:{\"src\":_vm.errorIcon,\"alt\":\"Error Icon\"}}),_c('p',[_vm._v(_vm._s(_vm.errorMessage))])]):_vm._e()]),_c('div',{staticClass:\"error-modal_overlay\",on:{\"click\":function($event){$event.preventDefault();return _vm.toggle.apply(null, arguments)}}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div v-show=\"show\" class=\"error-modal\">\n    <transition name=\"vac-bounce\">\n      <div v-if=\"show\" class=\"error-modal-div\">\n        <img :src=\"errorIcon\" alt=\"Error Icon\" />\n        <p>{{errorMessage}}</p>\n      </div>\n    </transition>\n    <div class=\"error-modal_overlay\" @click.prevent=\"toggle\" />\n  </div>\n</template>\n\n<script>\n\nimport ErrorIcon from '../../../assets/icons/error_icon.svg'\n\nexport default {\n  name: 'ErrorModal',\n  props: {\n    show: { type: Boolean },\n    toggle: { type: Function, default: () => ({}) },\n    errorMessage: { type: String, default: 'Something went wrong!' },\n  },\n  data(){\n    return {\n      errorIcon: ErrorIcon\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./ErrorModal.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./ErrorModal.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ErrorModal.vue?vue&type=template&id=6249ef3c\"\nimport script from \"./ErrorModal.vue?vue&type=script&lang=js\"\nexport * from \"./ErrorModal.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.show),expression:\"show\"}],staticClass:\"success-modal\"},[_c('transition',{attrs:{\"name\":\"vac-bounce\"}},[(_vm.show)?_c('div',{staticClass:\"success-modal_content\"},[_c('div',{staticClass:\"success-header\"},[_vm._v(\" \"+_vm._s(_vm.successMessage.heading)+\" \"),_c('button',{staticClass:\"close-button\",on:{\"click\":function($event){$event.preventDefault();return _vm.toggle.apply(null, arguments)}}},[_vm._v(\"×\")])]),_c('div',{staticClass:\"success-message\"},[_c('p',[_vm._v(_vm._s(_vm.successMessage.content))])])]):_vm._e()]),_c('div',{staticClass:\"success-modal_overlay\",on:{\"click\":function($event){$event.preventDefault();return _vm.toggle.apply(null, arguments)}}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div v-show=\"show\" class=\"success-modal\">\n    <transition name=\"vac-bounce\">\n      <div v-if=\"show\" class=\"success-modal_content\">\n        <div class=\"success-header\">\n          {{ successMessage.heading }}\n          <button class=\"close-button\" @click.prevent=\"toggle\">&times;</button>\n        </div>\n\n        <div class=\"success-message\">\n          <p>{{ successMessage.content }}</p>\n        </div>\n      </div>\n    </transition>\n    <div class=\"success-modal_overlay\" @click.prevent=\"toggle\" />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'SuccessModal',\n  props: {\n    show: { type: Boolean },\n    toggle: { type: Function, default: () => ({}) },\n    successMessage: {\n      type: Object,\n      required: true\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./SuccessModal.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./SuccessModal.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./SuccessModal.vue?vue&type=template&id=8840c9e4\"\nimport script from \"./SuccessModal.vue?vue&type=script&lang=js\"\nexport * from \"./SuccessModal.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return (_vm.show)?_c('div',{staticClass:\"image-preview\"},[_c('div',{staticClass:\"image-preview-topbar\"},[_c('div',{staticClass:\"image-details\"},[_c('span',{staticClass:\"image-name\"},[_vm._v(_vm._s(_vm.msg.username))]),_c('span',{staticClass:\"image-time\"},[_vm._v(_vm._s(_vm.imageTime))])]),_c('div',{staticClass:\"d-flex align-items-center\"},[_c('div',{staticClass:\"download-icon\",on:{\"click\":_vm.openFile}},[_c('svg-icon',{attrs:{\"name\":\"document\"}}),_vm._m(0)],1),_c('div',{staticClass:\"image-preview-topbar_close\",on:{\"click\":_vm.close}},[_c('svg-icon',{attrs:{\"name\":\"close\"}}),_vm._m(1)],1)])]),_c('div',{staticClass:\"image-preview-main\"},[_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"carousel-img\"},[_c('img',{attrs:{\"src\":_vm.msg.url,\"alt\":_vm.msg.name}})])])])]):_vm._e()\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"popover__content\"},[_c('p',{staticClass:\"popover__message\"},[_vm._v(\"Download\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"popover__content\"},[_c('p',{staticClass:\"popover__message\"},[_vm._v(\"Close\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div v-if=\"show\" class=\"image-preview\">\n    <!-- topbar -->\n    <div class=\"image-preview-topbar\">\n      <div class=\"image-details\">\n        <span class=\"image-name\">{{ msg.username }}</span>\n        <span class=\"image-time\">{{ imageTime }}</span>\n      </div>\n      <div class=\"d-flex align-items-center\">\n        <div class=\"download-icon\" @click=\"openFile\">\n          <svg-icon name=\"document\" />\n          <div class=\"popover__content\">\n            <p class=\"popover__message\">Download</p>\n          </div>\n        </div>\n        <div class=\"image-preview-topbar_close\" @click=\"close\">\n          <svg-icon name=\"close\" />\n          <div class=\"popover__content\">\n            <p class=\"popover__message\">Close</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"image-preview-main\">\n      <div class=\"content\">\n        <div class=\"carousel-img\">\n          <img :src=\"msg.url\" :alt=\"msg.name\" />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport SvgIcon from '../../components/SvgIcon/SvgIcon'\nexport default {\n  name: 'ImageViewer',\n  components: { SvgIcon },\n  props: {\n    show: { type: Boolean },\n    close: { type: Function, default: () => ({}) },\n    msg: { type: Object, required: true }\n  },\n  emits: ['open-forward-modal'],\n  computed: {\n    imageTime() {\n      return this.msg.date + ' at ' + this.msg.timestamp\n    }\n  },\n\n  methods: {\n    async openFile() {\n      const a = document.createElement('a')\n      a.href = this.msg.url\n      a.target = '_blank'\n      a.download = this.msg.url.split('/').pop()\n      console.log(a.download)\n      document.body.appendChild(a)\n      a.click()\n      document.body.removeChild(a)\n\n      // let msg = this.msg;\n      // const image = await fetch(this.msg.url, { mode: \"no-cors\" })\n      //   .then((response) => {\n      //     response.blob().then(function (myBlob) {\n      //       var objectURL = URL.createObjectURL(myBlob);\n      //       const link = document.createElement(\"a\");\n      //       link.href = URL.createObjectURL(myBlob);\n      //       link.download = msg.url.split(\"/\").pop();\n      //       link.click();\n      //       URL.revokeObjectURL(link.href);\n      //     });\n      //   })\n      //   .catch((error) => console.log(error));\n\n      // const blob = await image.blob();\n      // const link = document.createElement(\"a\");\n      // link.href = URL.createObjectURL(blob);\n      // link.download = this.msg.name + \".\" + this.msg.extension;\n      // link.click();\n      // URL.revokeObjectURL(link.href);\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import './ImageViewer.scss';\n</style>\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./ImageViewer.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./ImageViewer.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ImageViewer.vue?vue&type=template&id=d17b0594&scoped=true\"\nimport script from \"./ImageViewer.vue?vue&type=script&lang=js\"\nexport * from \"./ImageViewer.vue?vue&type=script&lang=js\"\nimport style0 from \"./ImageViewer.vue?vue&type=style&index=0&id=d17b0594&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d17b0594\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"overflow-hidden w-100 room-container\"},[(_vm.disableFooter)?_c('info'):_vm._e(),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:((_vm.isMobile && !_vm.showRoomsList) || !_vm.isMobile || _vm.singleRoom),expression:\"(isMobile && !showRoomsList) || !isMobile || singleRoom\"}],staticClass:\"vac-col-messages\",on:{\"touchstart\":_vm.touchStart}},[(_vm.showNoRoom)?_vm._t(\"no-room-selected\",function(){return [_c('div',{staticClass:\"vac-container-center vac-room-empty\"},[_c('div',[_vm._v(_vm._s(_vm.textMessages.ROOM_EMPTY))])])]}):_c('room-header',{attrs:{\"current-user-id\":_vm.currentUserId,\"text-messages\":_vm.textMessages,\"single-room\":_vm.singleRoom,\"show-rooms-list\":_vm.showRoomsList,\"is-mobile\":_vm.isMobile,\"menu-actions\":_vm.menuActions,\"isMsgFetched\":_vm.isMsgFetched,\"room\":_vm.room,\"templates\":_vm.templates},on:{\"handle-message-search\":_vm.handleMessageSearch,\"add-template\":_vm.addTemplate,\"add-template-msg\":_vm.forwardTemplateMsg,\"toggle-rooms-list\":function($event){return _vm.$emit('toggle-rooms-list')},\"toggle-menu-bar\":function($event){return _vm.$emit('toggle-menu-bar')},\"redirect-to-hubspot\":function($event){return _vm.$emit('redirect-to-hubspot', _vm.room)}},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)}),_c('div',{ref:\"scrollContainer\",staticClass:\"vac-container-scroll\",on:{\"scroll\":_vm.onContainerScroll}},[_c('div',{staticClass:\"vac-messages-container\"},[_c('div',{class:{ 'vac-messages-hidden': _vm.loadingMessages }},[_c('transition',{attrs:{\"name\":\"vac-fade-message\"}},[_c('div',[(_vm.showNoMessages)?_c('div',{staticClass:\"vac-text-started\"},[_vm._t(\"messages-empty\",function(){return [_vm._v(\" \"+_vm._s(_vm.textMessages.MESSAGES_EMPTY)+\" \")]})],2):_vm._e(),(_vm.showMessagesStarted)?_c('div',{staticClass:\"vac-text-started\"},[_vm._v(\" \"+_vm._s(_vm.textMessages.CONVERSATION_STARTED)+\" \"+_vm._s(_vm.messages[0].date)+\" \")]):_vm._e()])]),_c('transition',{attrs:{\"name\":\"vac-fade-message\"}},[(_vm.messages.length)?_c('infinite-loading',{class:{ 'vac-infinite-loading': !_vm.messagesLoaded },attrs:{\"force-use-infinite-wrapper\":\".vac-container-scroll\",\"web-component-name\":\"vue-advanced-chat\",\"spinner\":\"spiral\",\"direction\":\"top\",\"distance\":40},on:{\"infinite\":_vm.loadMoreMessages},scopedSlots:_vm._u([{key:\"spinner\",fn:function(){return [_c('loader',{attrs:{\"show\":true,\"infinite\":true}})]},proxy:true},{key:\"no-results\",fn:function(){return [_c('div')]},proxy:true},{key:\"no-more\",fn:function(){return [_c('div')]},proxy:true}],null,false,3407458732)}):_vm._e()],1),_c('transition-group',{key:_vm.roomId,attrs:{\"name\":\"vac-fade-message\",\"tag\":\"span\"}},_vm._l((_vm.messages),function(m,i){return _c('div',{key:m.indexId || m._id},[_c('message',{attrs:{\"current-user-id\":_vm.currentUserId,\"message\":m,\"index\":i,\"messages\":_vm.messages,\"edited-message\":_vm.editedMessage,\"message-actions\":_vm.messageActions,\"room-users\":_vm.room.users,\"text-messages\":_vm.textMessages,\"room-footer-ref\":_vm.$refs.roomFooter,\"new-messages\":_vm.newMessages,\"show-reaction-emojis\":_vm.showReactionEmojis,\"show-new-messages-divider\":_vm.showNewMessagesDivider,\"text-formatting\":_vm.textFormatting,\"link-options\":_vm.linkOptions,\"hide-options\":_vm.hideOptions,\"toggle-labels-modal\":_vm.toggleLabelsModal,\"msg-search-query\":_vm.msgSearchQuery,\"is-group\":_vm.room.isGroup,\"unread-counts\":_vm.unreadCounts,\"room-id\":_vm.roomId},on:{\"toggle-error-modal\":_vm.toggleErrorModal,\"message-added\":_vm.onMessageAdded,\"message-action-handler\":_vm.messageActionHandler,\"open-file\":_vm.openFile,\"send-message-reaction\":_vm.sendMessageReaction,\"hide-options\":function($event){_vm.hideOptions = $event},\"carousel-handler\":_vm.carouselHandler,\"reply-msg-handler\":_vm.replyMsgHandler},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(idx,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)})],1)}),0)],1)])]),(!_vm.loadingMessages)?_c('div',[_c('transition',{attrs:{\"name\":\"vac-bounce\"}},[(_vm.scrollIcon)?_c('div',{staticClass:\"vac-icon-scroll\",on:{\"click\":_vm.scrollToBottom}},[_c('transition',{attrs:{\"name\":\"vac-bounce\"}},[(_vm.scrollMessagesCount)?_c('div',{staticClass:\"vac-badge-counter vac-messages-count\"},[_vm._v(\" \"+_vm._s(_vm.scrollMessagesCount)+\" \")]):_vm._e()]),_vm._t(\"scroll-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"dropdown\",\"param\":\"scroll\"}})]})],2):_vm._e()])],1):_vm._e(),_c('div',{ref:\"roomFooter\",staticClass:\"vac-room-footer\",class:{\n      'vac-app-box-shadow': _vm.shadowFooter,\n      'footer-disabled': _vm.disableFooter,\n    }},[_c('room-emojis',{attrs:{\"filtered-emojis\":_vm.filteredEmojis,\"select-item\":_vm.selectEmojiItem,\"active-up-or-down\":_vm.activeUpOrDownEmojis},on:{\"select-emoji\":function($event){return _vm.selectEmoji($event)},\"activate-item\":function($event){_vm.activeUpOrDownEmojis = 0}}}),_c('room-message-reply',{attrs:{\"room\":_vm.room,\"message-reply\":_vm.messageReply,\"text-formatting\":_vm.textFormatting,\"link-options\":_vm.linkOptions},on:{\"reset-message\":_vm.resetMessage},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)}),_c('div',{staticClass:\"vac-box-footer\"},[(!_vm.showAudio && !_vm.files.length)?_c('div',{staticClass:\"vac-icon-textarea-left\"},[(_vm.isRecording)?[_c('div',{staticClass:\"vac-svg-button vac-icon-audio-stop\",on:{\"click\":_vm.stopRecorder}},[_vm._t(\"audio-stop-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"close-outline\"}})]})],2),_c('div',{staticClass:\"vac-dot-audio-record\"}),_c('div',{staticClass:\"vac-dot-audio-record-time\"},[_vm._v(\" \"+_vm._s(_vm.recordedTime)+\" \")]),_c('div',{staticClass:\"vac-svg-button vac-icon-audio-confirm\",on:{\"click\":function($event){return _vm.toggleRecorder(false)}}},[_vm._t(\"audio-stop-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"checkmark\"}})]})],2)]:_c('div',{staticClass:\"vac-svg-button\",on:{\"click\":function($event){return _vm.toggleRecorder(true)}}},[_vm._t(\"microphone-icon\",function(){return [_c('svg-icon',{staticClass:\"vac-icon-microphone\",attrs:{\"name\":\"microphone\"}})]})],2)],2):_vm._e(),(!_vm.showUploadModal)?_c('room-files',{attrs:{\"files\":_vm.files},on:{\"remove-file\":_vm.removeFile,\"reset-message\":_vm.resetMessage},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)}):_vm._e(),_c('div',{staticClass:\"textarea-wrapper\"},[_c('div',{staticClass:\"vac-icon-textarea\"},[(_vm.editedMessage._id)?_c('div',{staticClass:\"vac-svg-button\",on:{\"click\":_vm.resetMessage}},[_vm._t(\"edit-close-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"close-outline\"}})]})],2):_vm._e(),(_vm.showEmojis)?_c('emoji-picker-container',{directives:[{name:\"click-outside\",rawName:\"v-click-outside\",value:(() => (_vm.emojiOpened = false)),expression:\"() => (emojiOpened = false)\"}],attrs:{\"emoji-opened\":_vm.emojiOpened,\"position-top\":true},on:{\"add-emoji\":_vm.addEmoji,\"open-emoji\":function($event){_vm.emojiOpened = $event}},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)}):_vm._e(),(_vm.showFiles)?_c('div',{staticClass:\"vac-svg-button paperclip-icon\",on:{\"click\":_vm.launchFilePicker}},[_vm._t(\"paperclip-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"paperclip\",\"width\":\"19\",\"height\":\"40\"}})]})],2):_vm._e(),(_vm.textareaActionEnabled)?_c('div',{staticClass:\"vac-svg-button\",on:{\"click\":_vm.textareaActionHandler}},[_vm._t(\"custom-action-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"deleted\"}})]})],2):_vm._e(),(_vm.showFiles)?_c('input',{ref:\"file\",staticStyle:{\"display\":\"none\"},attrs:{\"type\":\"file\",\"multiple\":\"\",\"accept\":_vm.acceptedFiles},on:{\"change\":function($event){return _vm.onFileChange($event.target.files)}}}):_vm._e()],1),_c('div',{staticClass:\"textarea-box\"},[_c('textarea',{ref:\"roomTextarea\",staticClass:\"vac-textarea\",class:{\n                'vac-textarea-outline': _vm.editedMessage._id,\n              },style:({\n                position: `relative`,\n                maxHeight: `100px`,\n              }),attrs:{\"disabled\":_vm.disableFooter || _vm.messageInTransit,\"placeholder\":\"Type Something...\"},on:{\"input\":_vm.onChangeInput,\"keydown\":[function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"esc\",27,$event.key,[\"Esc\",\"Escape\"]))return null;return _vm.escapeTextarea.apply(null, arguments)},function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;if($event.ctrlKey||$event.shiftKey||$event.altKey||$event.metaKey)return null;$event.preventDefault();return _vm.selectItem.apply(null, arguments)},function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"tab\",9,$event.key,\"Tab\"))return null;if($event.ctrlKey||$event.shiftKey||$event.altKey||$event.metaKey)return null;$event.preventDefault();},function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"tab\",9,$event.key,\"Tab\"))return null;return _vm.selectItem.apply(null, arguments)},function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"up\",38,$event.key,[\"Up\",\"ArrowUp\"]))return null;return _vm.updateActiveUpOrDown($event, -1)},function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"down\",40,$event.key,[\"Down\",\"ArrowDown\"]))return null;return _vm.updateActiveUpOrDown($event, 1)}],\"paste\":_vm.onPasteImage}}),(_vm.showSendIcon)?_c('div',{staticClass:\"vac-svg-button send-icon\",class:{ 'vac-send-disabled': _vm.isMessageEmpty },on:{\"click\":_vm.sendMessage}},[(!_vm.messageInTransit)?_c('img',{attrs:{\"src\":_vm.isMessageEmpty ? _vm.sendIconDisabled : _vm.sendIcon,\"alt\":\"Send Icon\",\"param\":_vm.isMessageEmpty ? 'disabled' : ''}}):_vm._e(),(_vm.messageInTransit)?_c('div',{staticClass:\"spinner-border text-primary\",attrs:{\"role\":\"status\"}},[_c('span',{staticClass:\"visually-hidden\"},[_vm._v(\"Loading...\")])]):_vm._e()]):_vm._e()])])],1)],1)],2),_c('upload-modal',{attrs:{\"show\":_vm.showUploadModal,\"files\":_vm.files,\"toggle\":_vm.toggleUploadModal,\"handle-upload\":_vm.handleUpload}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"loader\"})\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"loader\"></div>\n</template>\n\n<script>\nexport default {\n  name: 'Loader',\n\n  // props: {\n  //   show: { type: Boolean, default: false },\n  //   infinite: { type: Boolean, default: false }\n  // }\n}\n</script>\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Loader.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Loader.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Loader.vue?vue&type=template&id=6c20f3c7\"\nimport script from \"./Loader.vue?vue&type=script&lang=js\"\nexport * from \"./Loader.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"vac-emoji-wrapper\"},[_c('div',{staticClass:\"vac-svg-button emoji-icon\",class:{ 'vac-emoji-reaction': _vm.emojiReaction },on:{\"click\":_vm.openEmoji}},[_vm._t(\"emoji-picker-icon\",function(){return [_c('svg',{attrs:{\"width\":\"35\",\"height\":\"36\",\"viewBox\":\"0 0 35 36\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"xmlns:xlink\":\"http://www.w3.org/1999/xlink\",\"param\":_vm.emojiReaction ? 'reaction' : ''}},[_c('rect',{attrs:{\"y\":\"0.729492\",\"width\":\"35\",\"height\":\"34.9353\",\"fill\":\"url(#pattern0)\"}}),_c('defs',[_c('pattern',{attrs:{\"id\":\"pattern0\",\"patternContentUnits\":\"objectBoundingBox\",\"width\":\"1\",\"height\":\"1\"}},[_c('use',{attrs:{\"xlink:href\":\"#image0\",\"transform\":\"translate(0 -0.000925904) scale(0.015625)\"}})]),_c('image',{attrs:{\"id\":\"image0\",\"width\":\"64\",\"height\":\"64\",\"xlink:href\":\"data:image/png;base64,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\"}})])])]})],2),(_vm.emojiOpened)?[_c('transition',{attrs:{\"name\":\"vac-slide-up\",\"appear\":\"\"}},[_c('div',{staticClass:\"vac-emoji-picker\",class:{ 'vac-picker-reaction': _vm.emojiReaction },style:({\n          height: `${_vm.emojiPickerHeight}px`,\n          top: _vm.positionTop ? _vm.emojiPickerHeight : `${_vm.emojiPickerTop}px`,\n          right: _vm.emojiPickerRight,\n          display: _vm.emojiPickerTop || !_vm.emojiReaction ? 'initial' : 'none'\n        })},[(_vm.emojiOpened)?_c('emoji-picker',{ref:\"emojiPicker\"}):_vm._e()],1)])]:_vm._e()],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"vac-emoji-wrapper\">\n    <div class=\"vac-svg-button emoji-icon\" :class=\"{ 'vac-emoji-reaction': emojiReaction }\" @click=\"openEmoji\">\n      <slot name=\"emoji-picker-icon\">\n        <svg\n          width=\"35\"\n          height=\"36\"\n          viewBox=\"0 0 35 36\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n          :param=\"emojiReaction ? 'reaction' : ''\"\n        >\n          <rect y=\"0.729492\" width=\"35\" height=\"34.9353\" fill=\"url(#pattern0)\" />\n          <defs>\n            <pattern id=\"pattern0\" patternContentUnits=\"objectBoundingBox\" width=\"1\" height=\"1\">\n              <use xlink:href=\"#image0\" transform=\"translate(0 -0.000925904) scale(0.015625)\" />\n            </pattern>\n            <image\n              id=\"image0\"\n              width=\"64\"\n              height=\"64\"\n              xlink:href=\"data:image/png;base64,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\"\n            />\n          </defs>\n        </svg>\n      </slot>\n    </div>\n\n    <template v-if=\"emojiOpened\">\n      <transition name=\"vac-slide-up\" appear>\n        <div\n          class=\"vac-emoji-picker\"\n          :class=\"{ 'vac-picker-reaction': emojiReaction }\"\n          :style=\"{\n            height: `${emojiPickerHeight}px`,\n            top: positionTop ? emojiPickerHeight : `${emojiPickerTop}px`,\n            right: emojiPickerRight,\n            display: emojiPickerTop || !emojiReaction ? 'initial' : 'none'\n          }\"\n        >\n          <emoji-picker v-if=\"emojiOpened\" ref=\"emojiPicker\" />\n        </div>\n      </transition>\n    </template>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'EmojiPickerContainer',\n\n  props: {\n    emojiOpened: { type: Boolean, default: false },\n    emojiReaction: { type: Boolean, default: false },\n    roomFooterRef: { type: HTMLDivElement, default: null },\n    positionTop: { type: Boolean, default: false },\n    positionRight: { type: Boolean, default: false }\n  },\n\n  emits: ['add-emoji', 'open-emoji'],\n\n  data() {\n    return {\n      emojiPickerHeight: 320,\n      emojiPickerTop: 0,\n      emojiPickerRight: ''\n    }\n  },\n\n  watch: {\n    emojiOpened(val) {\n      if (val) {\n        setTimeout(() => {\n          this.addCustomStyling()\n\n          document.querySelector('emoji-picker').addEventListener('emoji-click', ({ detail }) => {\n            this.$emit('add-emoji', {\n              unicode: detail.unicode\n            })\n          })\n        }, 0)\n      }\n    }\n  },\n\n  methods: {\n    addCustomStyling() {\n      const picker = `.picker {\n\t\t\t\tborder: none;\n\t\t\t}`\n\n      const nav = `.nav {\n\t\t\t\toverflow-x: auto;\n\t\t\t}`\n\n      const searchBox = `.search-wrapper {\n\t\t\t\tpadding-right: 2px;\n\t\t\t\tpadding-left: 2px;\n\t\t\t}`\n\n      const search = `input.search {\n\t\t\t\theight: 32px;\n\t\t\t\tfont-size: 14px;\n\t\t\t\tborder-radius: 10rem;\n\t\t\t\tborder: var(--chat-border-style);\n\t\t\t\tpadding: 5px 10px;\n\t\t\t\toutline: none;\n\t\t\t\tbackground: var(--chat-bg-color-input);\n\t\t\t\tcolor: var(--chat-color);\n\t\t\t}`\n\n      const style = document.createElement('style')\n      style.textContent = picker + nav + searchBox + search\n      this.$refs.emojiPicker.shadowRoot.appendChild(style)\n    },\n    openEmoji(ev) {\n      this.$emit('open-emoji', !this.emojiOpened)\n      this.setEmojiPickerPosition(ev.clientY, ev.view.innerWidth, ev.view.innerHeight)\n    },\n    setEmojiPickerPosition(clientY, innerWidth, innerHeight) {\n      setTimeout(() => {\n        const mobileSize = innerWidth < 500 || innerHeight < 700\n\n        if (!this.roomFooterRef) {\n          if (mobileSize) this.emojiPickerRight = '-50px'\n          return\n        }\n\n        if (mobileSize) {\n          this.emojiPickerRight = innerWidth / 2 - 150 + 'px'\n          this.emojiPickerTop = 100\n          this.emojiPickerHeight = innerHeight - 200\n        } else {\n          const roomFooterTop = this.roomFooterRef.getBoundingClientRect().top\n          const pickerTopPosition = roomFooterTop - clientY > this.emojiPickerHeight - 50\n\n          if (pickerTopPosition) this.emojiPickerTop = clientY + 10\n          else this.emojiPickerTop = clientY - this.emojiPickerHeight - 10\n\n          this.emojiPickerRight = this.positionTop ? '-50px' : this.positionRight ? '60px' : ''\n        }\n      })\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./EmojiPickerContainer.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./EmojiPickerContainer.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./EmojiPickerContainer.vue?vue&type=template&id=0fd1a711\"\nimport script from \"./EmojiPickerContainer.vue?vue&type=script&lang=js\"\nexport * from \"./EmojiPickerContainer.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return (_vm.show)?_c('div',[_c('div',{staticClass:\"upload-modal\"},[_c('button',{staticClass:\"upload-modal_close\",on:{\"click\":function($event){$event.preventDefault();return _vm.toggle.apply(null, arguments)}}},[_vm._v(\"×\")]),_c('div',{staticClass:\"upload-modal-content\"},[_vm._m(0),_c('div',{staticClass:\"dragdrop-box\"},[_c('div',{staticClass:\"drag-drop\",class:{\n            'bg-secondary': _vm.is_dragover\n          },on:{\"drag\":function($event){$event.preventDefault();$event.stopPropagation();},\"dragstart\":function($event){$event.preventDefault();$event.stopPropagation();},\"dragend\":function($event){$event.preventDefault();$event.stopPropagation();_vm.is_dragover = false},\"dragover\":function($event){$event.preventDefault();$event.stopPropagation();_vm.is_dragover = true},\"dragenter\":function($event){$event.preventDefault();$event.stopPropagation();_vm.is_dragover = true},\"dragleave\":function($event){$event.preventDefault();$event.stopPropagation();_vm.is_dragover = false},\"drop\":function($event){$event.preventDefault();$event.stopPropagation();return _vm.handleDragDrop.apply(null, arguments)}}},[_c('img',{attrs:{\"src\":_vm.dropIcon,\"alt\":\"Drag Drop Icon\"}}),_c('div',{staticClass:\"drop-text\"},[_vm._v(\"Drag and drop files here\")])]),_c('div',{staticClass:\"diversion-text\"},[_vm._v(\"OR\")]),_c('div',{staticClass:\"upload-button\"},[_c('button',{on:{\"click\":function($event){$event.preventDefault();return _vm.handleUpload.apply(null, arguments)}}},[_vm._v(\"Browse Files\")])])]),(_vm.files.length)?_c('div',{staticClass:\"uploaded-files\"},[_vm._m(1),_c('div',{staticClass:\"upload-file-wrapper\"},_vm._l((_vm.files),function(file){return _c('div',{key:file.name,staticClass:\"upload-file-item\"},[_c('img',{staticClass:\"extension-icon\",attrs:{\"src\":file.extension === 'pdf' ? _vm.pdfIcon : _vm.docIcon}}),_c('div',{staticClass:\"file-progress-wrapper\"},[_c('div',{staticClass:\"file-name\"},[_vm._v(\" \"+_vm._s(file.name)+\" \")]),_vm._m(2,true)]),_c('div',{staticClass:\"status-icon\"},[_c('img',{attrs:{\"src\":_vm.checkIcon}})])])}),0)]):_vm._e()])]),_c('div',{staticClass:\"upload-modal_overlay\",on:{\"click\":function($event){$event.preventDefault();return _vm.toggle.apply(null, arguments)}}})]):_vm._e()\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"content-heading\"},[_c('div',{staticClass:\"content-heading_main\"},[_vm._v(\"UPLOAD FILES\")]),_c('div',{staticClass:\"content-heading_sub\"},[_vm._v(\"Upload documents you want to share\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"file-heading\"},[_c('span',[_vm._v(\"Uploaded files\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"hwa-progress-bar\"},[_c('div',{staticClass:\"progress\"},[_c('div',{staticClass:\"progress-bar bg-darkblue\",staticStyle:{\"width\":\"100%\"},attrs:{\"role\":\"progressbar\",\"aria-valuenow\":\"100\",\"aria-valuemin\":\"0\",\"aria-valuemax\":\"100\"}})])])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div v-if=\"show\">\n    <div class=\"upload-modal\">\n      <button class=\"upload-modal_close\" @click.prevent=\"toggle\">&times;</button>\n\n      <div class=\"upload-modal-content\">\n        <div class=\"content-heading\">\n          <div class=\"content-heading_main\">UPLOAD FILES</div>\n          <div class=\"content-heading_sub\">Upload documents you want to share</div>\n        </div>\n        <div class=\"dragdrop-box\">\n          <div\n            class=\"drag-drop\"\n            :class=\"{\n              'bg-secondary': is_dragover\n            }\"\n            @drag.prevent.stop=\"\"\n            @dragstart.prevent.stop=\"\"\n            @dragend.prevent.stop=\"is_dragover = false\"\n            @dragover.prevent.stop=\"is_dragover = true\"\n            @dragenter.prevent.stop=\"is_dragover = true\"\n            @dragleave.prevent.stop=\"is_dragover = false\"\n            @drop.prevent.stop=\"handleDragDrop\"\n          >\n            <img :src=\"dropIcon\" alt=\"Drag Drop Icon\" />\n            <div class=\"drop-text\">Drag and drop files here</div>\n          </div>\n          <div class=\"diversion-text\">OR</div>\n          <div class=\"upload-button\">\n            <button @click.prevent=\"handleUpload\">Browse Files</button>\n          </div>\n        </div>\n\n        <div v-if=\"files.length\" class=\"uploaded-files\">\n          <div class=\"file-heading\">\n            <span>Uploaded files</span>\n          </div>\n          <!-- files -->\n          <div class=\"upload-file-wrapper\">\n            <div v-for=\"file in files\" :key=\"file.name\" class=\"upload-file-item\">\n              <img class=\"extension-icon\" :src=\"file.extension === 'pdf' ? pdfIcon : docIcon\" />\n              <div class=\"file-progress-wrapper\">\n                <div class=\"file-name\">\n                  {{ file.name }}\n                </div>\n                <div class=\"hwa-progress-bar\">\n                  <div class=\"progress\">\n                    <div\n                      class=\"progress-bar bg-darkblue\"\n                      role=\"progressbar\"\n                      style=\"width: 100%\"\n                      aria-valuenow=\"100\"\n                      aria-valuemin=\"0\"\n                      aria-valuemax=\"100\"\n                    />\n                  </div>\n                </div>\n              </div>\n              <div class=\"status-icon\">\n                <img :src=\"checkIcon\" />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"upload-modal_overlay\" @click.prevent=\"toggle\" />\n  </div>\n</template>\n\n<script>\nimport DropIcon from '../../../assets/icons/drop_icon.png'\nimport PdfIcon from '../../../assets/icons/pdf_icon.png'\nimport DocIcon from '../../../assets/icons/doc_icon.png'\nimport CheckIcon from '../../../assets/icons/check_icon.png'\nexport default {\n  name: 'UploadModal',\n  props: {\n    show: { type: Boolean },\n    // eslint-disable-next-line vue/require-valid-default-prop\n    files: { type: Array, default: [] },\n    toggle: { type: Function, default: () => ({}) },\n    handleUpload: { type: Function, default: () => ({}) }\n  },\n  data() {\n    return {\n      dropIcon: DropIcon,\n      pdfIcon: PdfIcon,\n      docIcon: DocIcon,\n      checkIcon: CheckIcon,\n      is_dragover: false\n    }\n  },\n\n  methods: {\n    handleDragDrop($event) {\n      this.is_dragover = false\n      this.handleUpload($event)\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./UploadModal.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./UploadModal.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./UploadModal.vue?vue&type=template&id=5389d1ea\"\nimport script from \"./UploadModal.vue?vue&type=script&lang=js\"\nexport * from \"./UploadModal.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"info-modal\"},[_c('transition',{attrs:{\"name\":\"vac-bounce\"}},[_c('div',[_c('img',{attrs:{\"src\":_vm.infoIcon,\"alt\":\"\"}}),_c('span',[_vm._v(_vm._s(_vm.infoMsg))])])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n    <div class=\"info-modal\">\n        <transition name=\"vac-bounce\">\n            <div>\n                <img :src=\"infoIcon\" alt=\"\">\n                <span>{{ infoMsg }}</span>\n            </div>\n        </transition>\n\n    </div>\n</template>\n\n<script>\nimport infoIcon from \"../../../assets/icons/info-Icon.svg\"\nexport default {\n    data() {\n        return { \n\n            infoIcon: infoIcon\n        }\n    },\n  props: {\n    infoMsg: {\n      type: String,\n      default: \"Outside 24-hours window. Please send a template to initiate conversation.\"\n    }\n  },\n}\n\n</script>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Info.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Info.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Info.vue?vue&type=template&id=88f02626\"\nimport script from \"./Info.vue?vue&type=script&lang=js\"\nexport * from \"./Info.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"vac-room-header\"},[_vm._t(\"room-header\",function(){return [_c('div',{staticClass:\"vac-room-wrapper\"},[_c('div',{staticClass:\"vac-info-wrapper\"},[_vm._t(\"room-header-avatar\",function(){return [_c('div',{staticClass:\"vac-avatar\",style:({\n              'background-image': `url('${_vm.room.avatar || _vm.dummyAvatar}')`\n            })})]},null,{ room: _vm.room }),_vm._t(\"room-header-info\",function(){return [_c('div',{staticClass:\"vac-text-ellipsis\"},[_c('div',{staticClass:\"vac-room-name vac-text-ellipsis\"},[_vm._v(\" \"+_vm._s(_vm.userName)+\" \")])])]},null,{ room: _vm.room, typingUsers: _vm.typingUsers })],2),_c('div',{staticClass:\"d-flex align-items-center\"},[_c('div',{staticClass:\"hwa-template\"},[_c('div',{staticClass:\"external-link\"},[_c('a',{attrs:{\"href\":`${_vm.config.baseURL}dashboard/?user_id=${_vm.userId}&accountUser=${_vm.accountUser}&portal_id=${_vm.portalId}&accountPhone=${_vm.accountPhone}`,\"target\":\"_blank\"}},[_c('img',{attrs:{\"src\":_vm.ExternalIcon,\"alt\":\"\"}})]),_c('span',{staticClass:\"tooltip-span\"},[_vm._v(\"Go To Main DashBoard\")])]),_c('div',{staticClass:\"template-box\",on:{\"click\":function($event){$event.preventDefault();return _vm.templateHandler.apply(null, arguments)}}},[_c('p',{staticClass:\"hwa-template_text\"},[_vm._v(\"Templates\")]),_c('img',{class:{ rotateSvg: _vm.templateOpened },attrs:{\"src\":_vm.DropDownIcon,\"alt\":\"\",\"srcset\":\"\"}})]),_c('transition',{attrs:{\"name\":\"vac-slide-left\"}},[(_vm.templateOpened)?_c('div',{directives:[{name:\"click-outside\",rawName:\"v-click-outside\",value:(_vm.closeTemplates),expression:\"closeTemplates\"}],staticClass:\"vac-menu-options\"},[(!_vm.localTemplates.length)?_c('div',{staticClass:\"vac-menu-item no-template-center\"},[_c('a',{staticClass:\"nav-link\",attrs:{\"target\":\"_blank\",\"href\":\"https://business.facebook.com/wa/manage/message-templates\"}},[_vm._v(\" Create Template \")])]):_vm._e(),(_vm.localTemplates.length)?_c('div',[_c('div',{staticClass:\"template-search-box\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.searchTemplate),expression:\"searchTemplate\"}],attrs:{\"type\":\"text\",\"placeholder\":\"Search Template\"},domProps:{\"value\":(_vm.searchTemplate)},on:{\"input\":[function($event){if($event.target.composing)return;_vm.searchTemplate=$event.target.value},function($event){return _vm.debouncedHandleKeypressWhatsApp(_vm.searchTemplate)}]}}),_c('img',{staticClass:\"template-search-image_icon\",attrs:{\"src\":_vm.searchIcon,\"alt\":\"search icon\"}})]),_c('ul',{staticClass:\"hwa-menu-list\"},[(_vm.loading)?_c('div',{staticClass:\"vac-menu-item loading-item\"},[_vm._v(\"Searching templates...\")]):_vm._e(),_vm._l((_vm.filteredTemplates),function(template){return _c('li',{key:template.id,staticClass:\"vac-menu-item\",attrs:{\"title\":template.name},on:{\"click\":function($event){$event.preventDefault();return _vm.openModal(template)}}},[_vm._v(\" \"+_vm._s(template.name)+\" \")])}),(_vm.showNoDataMessage && !_vm.loading)?_c('li',{staticClass:\"no-data-found\"},[_vm._v(\"Template not found\")]):_vm._e()],2)]):_vm._e()]):_vm._e()])],1)])])]},null,{ room: _vm.room, typingUsers: _vm.typingUsers }),(_vm.isModalOpen)?_c('template-modal',{attrs:{\"selectedTemplate\":_vm.selectedTemplate,\"roomPhone\":_vm.room.roomId,\"objectId\":_vm.room.object_id,\"defaultHeaderTemplateUrl\":_vm.defaultHeaderTemplateUrl},on:{\"close-modal\":_vm.closeModal,\"add-template-msg\":_vm.forwardTemplateMsg}}):_vm._e()],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "export default (room, currentUserId, textMessages) => {\n  if (room.typingUsers && room.typingUsers.length) {\n    const typingUsers = room.users.filter(user => {\n      if (user._id === currentUserId) return\n      if (room.typingUsers.indexOf(user._id) === -1) return\n      if (user.status && user.status.state === 'offline') return\n      return true\n    })\n\n    if (!typingUsers.length) return\n\n    if (room.users.length === 2) {\n      return textMessages.IS_TYPING\n    } else {\n      return typingUsers.map(user => user.username).join(', ') + ' ' + textMessages.IS_TYPING\n    }\n  }\n}\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"template-modal\"},[_c('div',{staticClass:\"modal-overlay\",on:{\"click\":_vm.closeModal}}),_c('div',{staticClass:\"modal-dialog modal-md\"},[_c('div',{staticClass:\"modal-content\"},[_c('div',{staticClass:\"modal-header justify-content-between\"},[_c('h5',{staticClass:\"modal-title\"},[_vm._v(\"Send template message\")]),_c('button',{staticClass:\"btn\",attrs:{\"type\":\"button\"},on:{\"click\":_vm.closeModal}},[_c('img',{attrs:{\"src\":_vm.closeIcon,\"alt\":\"\",\"srcset\":\"\"}})])]),_c('hr'),_c('form',{on:{\"submit\":function($event){$event.preventDefault();return _vm.submitHandler.apply(null, arguments)}}},[_c('div',{staticClass:\"modal-body\"},[_c('p',[_c('span',{staticClass:\"fw-bolder\"},[_vm._v(\"Template Name: \")]),_vm._v(_vm._s(_vm.selectedTemplate.name))]),_c('p',[_c('span',{staticClass:\"fw-bolder\"},[_vm._v(\" Language Code: \")]),_vm._v(_vm._s(_vm.selectedTemplate.language))]),_c('p',{staticStyle:{\"white-space\":\"pre-wrap\"}},[_c('span',{staticClass:\"fw-bolder\"},[_vm._v(\"Template Content: \")]),_vm._v(_vm._s(_vm.selectedTemplate.body)+\" \")]),(_vm.selectedTemplate)?_c('div',{staticClass:\"mt-5\"},[(_vm.selectedTemplate.hasHeaderParam)?[_c('div',{key:_vm.selectedTemplate.id,staticClass:\"form-group\"},[_c('div',{staticClass:\"d-flex justify-content-between align-items-center\"},[_c('label',{staticClass:\"form-label\"},[_vm._v(\"Header Param Text\")]),_c('div',{staticClass:\"dropdown\"},[_c('button',{staticClass:\"btn tokenButton dropdown-toggle\",attrs:{\"type\":\"button\",\"data-bs-toggle\":\"dropdown\"},on:{\"click\":function($event){$event.preventDefault();return _vm.tokenModalHandler(`header_text_1`)}}},[_vm._v(\" Contact Tokens \")]),(_vm.activeTokenModal === `header_text_1`)?_c('ul',{staticClass:\"token-dropdown\"},[_c('li',[_c('input',{staticClass:\"form-control search-input\",attrs:{\"type\":\"text\",\"placeholder\":\"Search property name\"},on:{\"keyup\":function($event){return _vm.searchProperties($event)},\"keydown\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;$event.preventDefault();}}})]),_c('div',{staticClass:\"dropdown-data\"},[_vm._l((_vm.properties),function(property){return _c('li',{key:property.name,staticClass:\"tokenItem\",attrs:{\"data-name\":property.label}},[_c('button',{staticClass:\"dropdown-item\",attrs:{\"title\":property.name,\"type\":\"button\"},on:{\"click\":function($event){return _vm.addToken(property.name, 'header_text_1')}}},[_vm._v(\" \"+_vm._s(property.label)+\" \")])])}),_c('p',{staticClass:\"no-data-found\"},[_vm._v(\"Contact token not found\")])],2)]):_vm._e()])]),_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.templateFields['header_text_1']),expression:\"templateFields['header_text_1']\"}],staticClass:\"form-control\",attrs:{\"name\":\"header_text_1\",\"type\":\"text\",\"placeholder\":\"Enter value\",\"required\":\"\"},domProps:{\"value\":(_vm.templateFields['header_text_1'])},on:{\"keydown\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;$event.preventDefault();},\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.templateFields, 'header_text_1', $event.target.value)}}})])]:_vm._e(),(_vm.selectedTemplate.params !== 0)?_vm._l((_vm.selectedTemplate.params),function(index){return _c('div',{key:_vm.selectedTemplate.id + index,staticClass:\"form-group\"},[_c('div',{staticClass:\"d-flex flex-column\"},[_c('div',{staticClass:\"dropdown d-flex justify-content-between\"},[_c('label',{attrs:{\"for\":'placeholder_id_' + index}},[_vm._v(\"Placeholder \"+_vm._s(index))]),_c('button',{staticClass:\"btn tokenButton dropdown-toggle\",attrs:{\"type\":\"button\",\"aria-expanded\":\"true\"},on:{\"click\":function($event){$event.preventDefault();return _vm.tokenModalHandler(`placeholder${index}`)}}},[_vm._v(\" Contact Tokens \")]),(_vm.activeTokenModal === `placeholder${index}`)?_c('ul',{staticClass:\"token-dropdown\"},[_c('li',[_c('input',{staticClass:\"form-control search-input\",attrs:{\"type\":\"text\",\"placeholder\":\"Search property name\"},on:{\"keyup\":function($event){return _vm.searchProperties($event)},\"keydown\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;$event.preventDefault();}}})]),_c('div',{staticClass:\"dropdown-data\"},[_vm._l((_vm.properties),function(property){return _c('li',{key:property.name,staticClass:\"tokenItem\",attrs:{\"data-name\":property.label}},[_c('button',{staticClass:\"dropdown-item\",attrs:{\"title\":property.name,\"type\":\"button\"},on:{\"click\":function($event){return _vm.addToken(property.name, 'placeholder_' + index)}}},[_vm._v(\" \"+_vm._s(property.label)+\" \")])])}),_c('p',{staticClass:\"no-data-found\"},[_vm._v(\"Contact token not found\")])],2)]):_vm._e()]),_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.templateFields[`placeholder_${index}`]),expression:\"templateFields[`placeholder_${index}`]\"}],staticClass:\"form-control\",attrs:{\"id\":'placeholder_id_' + index,\"name\":'placeholder_' + index,\"type\":\"text\",\"placeholder\":\"Enter placeholder value \",\"required\":\"\"},domProps:{\"value\":(_vm.templateFields[`placeholder_${index}`])},on:{\"keydown\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;$event.preventDefault();},\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.templateFields, `placeholder_${index}`, $event.target.value)}}})])])}):_vm._e(),(_vm.matchedDependency)?[_vm._l((_vm.matchedDependency.dependentFieldNames),function(field,index){return [_c('div',{key:index,staticClass:\"form-group\"},[_c('div',{staticClass:\"d-flex justify-content-between align-items-center\"},[_c('div',{staticClass:\"info-icon-div\"},[_c('label',{staticClass:\"form-label\"},[_vm._v(_vm._s(Object.values(field)[0]))]),(Object.keys(field)[0].endsWith('_url'))?_c('div',{staticClass:\"info-hint\"},[_c('img',{attrs:{\"src\":_vm.infoIcon,\"alt\":\"info Icon\"}}),_vm._m(0,true)]):_vm._e()]),_c('div',{staticClass:\"dropdown\"},[_c('button',{staticClass:\"btn tokenButton dropdown-toggle\",attrs:{\"type\":\"button\",\"data-bs-toggle\":\"dropdown\",\"aria-expanded\":\"false\"},on:{\"click\":function($event){$event.preventDefault();_vm.tokenModalHandler(Object.values(field)[0])}}},[_vm._v(\" Contact Tokens \")]),(_vm.activeTokenModal === Object.values(field)[0])?_c('ul',{staticClass:\"token-dropdown\"},[_c('li',[_c('input',{staticClass:\"form-control search-input\",attrs:{\"type\":\"text\",\"placeholder\":\"Search property name\"},on:{\"keyup\":function($event){return _vm.searchProperties($event)},\"keydown\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;$event.preventDefault();}}})]),_c('div',{staticClass:\"dropdown-data\"},[_vm._l((_vm.properties),function(property){return _c('li',{key:property.name,staticClass:\"tokenItem\",attrs:{\"data-name\":property.label}},[_c('button',{staticClass:\"dropdown-item\",attrs:{\"title\":property.label,\"type\":\"button\"},on:{\"click\":function($event){_vm.addToken(property.name, Object.keys(field)[0])}}},[_vm._v(\" \"+_vm._s(property.label)+\" \")])])}),_c('p',{staticClass:\"no-data-found\"},[_vm._v(\"Contact token not found\")])],2)]):_vm._e()])]),_c('input',{staticClass:\"form-control\",attrs:{\"name\":Object.keys(field)[0],\"type\":\"text\",\"placeholder\":\"Enter value\",\"required\":\"\"},domProps:{\"value\":_vm.tokenValue || _vm.defaultUrl || ''},on:{\"input\":function($event){_vm.updateField($event, Object.keys(field)[0])},\"keydown\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;$event.preventDefault();}}})])]})]:_vm._e()],2):_vm._e()]),_c('div',{staticClass:\"modal-footer footer\"},[_c('button',{staticClass:\"btn btn-primary\",attrs:{\"disabled\":this.apiLoader,\"type\":\"submit\"}},[_vm._v(\"Send\")]),(this.apiLoader)?_c('Loader'):_vm._e()],1)])])]),_c('error-modal',{attrs:{\"show\":this.sendtemplateError,\"toggle\":_vm.hideErrorModal,\"error-message\":this.errorMessage}})],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('p',[_vm._v(\"Please refer to the Meta guidelines \"),_c('a',{attrs:{\"href\":\"https://developers.facebook.com/docs/whatsapp/cloud-api/reference/media/#supported-media-types\",\"target\":\"_blank\"}},[_vm._v(\"here\")]),_vm._v(\" in respect of supported media type and maximum file size\")])\n}]\n\nexport { render, staticRenderFns }", "const config = {\n  baseURL: process.env.VUE_APP_API_URL || 'http://localhost:8000/'\n  // baseURL: 'https://niswey.net/'\n}\n\nexport default config\n", "import axios from 'axios'\nimport config from './config'\n\nexport default axios.create({\n  baseURL: config.baseURL\n})\n", "<template>\n  <div class=\"template-modal\">\n    <div @click=\"closeModal\" class=\"modal-overlay\"></div>\n    <div class=\"modal-dialog modal-md\">\n      <div class=\"modal-content\">\n        <div class=\"modal-header justify-content-between\">\n          <h5 class=\"modal-title\">Send template message</h5>\n          <button type=\"button\" class=\"btn\" @click=\"closeModal\">\n            <img :src=\"closeIcon\" alt=\"\" srcset=\"\" />\n          </button>\n        </div>\n        <hr />\n        <form @submit.prevent=\"submitHandler\">\n          <div class=\"modal-body\">\n            <p><span class=\"fw-bolder\">Template Name: </span>{{ selectedTemplate.name }}</p>\n            <p><span class=\"fw-bolder\"> Language Code: </span>{{ selectedTemplate.language }}</p>\n            <p style=\"white-space: pre-wrap\">\n              <span class=\"fw-bolder\">Template Content: </span>{{ selectedTemplate.body }}\n            </p>\n\n            <div class=\"mt-5\" v-if=\"selectedTemplate\">\n              <template v-if=\"selectedTemplate.hasHeaderParam\">\n                <div class=\"form-group\" :key=\"selectedTemplate.id\">\n                  <div class=\"d-flex justify-content-between align-items-center\">\n                    <label class=\"form-label\">Header Param Text</label>\n                    <div class=\"dropdown\">\n                      <button\n                        class=\"btn tokenButton dropdown-toggle\"\n                        type=\"button\"\n                        data-bs-toggle=\"dropdown\"\n                        @click.prevent=\"tokenModalHandler(`header_text_1`)\"\n                      >\n                        Contact Tokens\n                      </button>\n                      <ul class=\"token-dropdown\" v-if=\"activeTokenModal === `header_text_1`\">\n                        <li>\n                          <input\n                            @keyup=\"searchProperties($event)\"\n                            @keydown.enter.prevent\n                            type=\"text\"\n                            class=\"form-control search-input\"\n                            placeholder=\"Search property name\"\n                          />\n                        </li>\n                        <div class=\"dropdown-data\">\n                          <li\n                            v-for=\"property in properties\"\n                            :key=\"property.name\"\n                            :data-name=\"property.label\"\n                            class=\"tokenItem\"\n                          >\n                            <button\n                              @click=\"addToken(property.name, 'header_text_1')\"\n                              :title=\"property.name\"\n                              class=\"dropdown-item\"\n                              type=\"button\"\n                            >\n                              {{ property.label }}\n                            </button>\n                          </li>\n                          <p class=\"no-data-found\">Contact token not found</p>\n                        </div>\n                      </ul>\n                    </div>\n                  </div>\n\n                  <input\n                    name=\"header_text_1\"\n                    v-model=\"templateFields['header_text_1']\"\n                    @keydown.enter.prevent\n                    type=\"text\"\n                    class=\"form-control\"\n                    placeholder=\"Enter value\"\n                    required\n                  />\n                </div>\n              </template>\n\n              <!-- Placeholder template -->\n              <template v-if=\"selectedTemplate.params !== 0\">\n                <div class=\"form-group\" v-for=\"index in selectedTemplate.params\" :key=\"selectedTemplate.id + index\">\n                  <div class=\"d-flex flex-column\">\n                    <div class=\"dropdown d-flex justify-content-between\">\n                      <label :for=\"'placeholder_id_' + index\">Placeholder {{ index }}</label>\n                      <button\n                        @click.prevent=\"tokenModalHandler(`placeholder${index}`)\"\n                        class=\"btn tokenButton dropdown-toggle\"\n                        type=\"button\"\n                        aria-expanded=\"true\"\n                      >\n                        Contact Tokens\n                      </button>\n                      <ul class=\"token-dropdown\" v-if=\"activeTokenModal === `placeholder${index}`\">\n                        <li>\n                          <input\n                            @keyup=\"searchProperties($event)\"\n                            @keydown.enter.prevent\n                            type=\"text\"\n                            class=\"form-control search-input\"\n                            placeholder=\"Search property name\"\n                          />\n                        </li>\n                        <div class=\"dropdown-data\">\n                          <li\n                            v-for=\"property in properties\"\n                            :key=\"property.name\"\n                            :data-name=\"property.label\"\n                            class=\"tokenItem\"\n                          >\n                            <button\n                              @click=\"addToken(property.name, 'placeholder_' + index)\"\n                              :title=\"property.name\"\n                              class=\"dropdown-item\"\n                              type=\"button\"\n                            >\n                              {{ property.label }}\n                            </button>\n                          </li>\n                          <p class=\"no-data-found\">Contact token not found</p>\n                        </div>\n                      </ul>\n                    </div>\n                    <input\n                      :id=\"'placeholder_id_' + index\"\n                      :name=\"'placeholder_' + index\"\n                      @keydown.enter.prevent\n                      v-model=\"templateFields[`placeholder_${index}`]\"\n                      type=\"text\"\n                      class=\"form-control\"\n                      placeholder=\"Enter placeholder value \"\n                      required\n                    />\n                  </div>\n                </div>\n              </template>\n\n              <!-- Dynamic data template -->\n              <template v-if=\"matchedDependency\">\n                <template v-for=\"(field, index) in matchedDependency.dependentFieldNames\">\n                  <div class=\"form-group\" :key=\"index\">\n                    <div class=\"d-flex justify-content-between align-items-center\">\n                      <div class=\"info-icon-div\">\n                        <label class=\"form-label\">{{ Object.values(field)[0] }}</label>\n                        <div v-if=\"Object.keys(field)[0].endsWith('_url')\" class=\"info-hint\">\n                          <img :src=\"infoIcon\" alt=\"info Icon\">\n                          <p>Please refer to the Meta guidelines <a href=\"https://developers.facebook.com/docs/whatsapp/cloud-api/reference/media/#supported-media-types\" target=\"_blank\">here</a> in respect of supported media type and  maximum file size</p>\n                        </div>\n                      </div>\n                      <div class=\"dropdown\">\n                        <button\n                          @click.prevent=\"tokenModalHandler(Object.values(field)[0])\"\n                          class=\"btn tokenButton dropdown-toggle\"\n                          type=\"button\"\n                          data-bs-toggle=\"dropdown\"\n                          aria-expanded=\"false\"\n                        >\n                          Contact Tokens\n                        </button>\n                        <ul class=\"token-dropdown\" v-if=\"activeTokenModal === Object.values(field)[0]\">\n                          <li>\n                            <input\n                              @keyup=\"searchProperties($event)\"\n                              @keydown.enter.prevent\n                              type=\"text\"\n                              class=\"form-control search-input\"\n                              placeholder=\"Search property name\"\n                            />\n                          </li>\n                          <div class=\"dropdown-data\">\n                            <li\n                              v-for=\"property in properties\"\n                              :key=\"property.name\"\n                              :data-name=\"property.label\"\n                              class=\"tokenItem\"\n                            >\n                              <button\n                                @click=\"addToken(property.name, Object.keys(field)[0])\"\n                                :title=\"property.label\"\n                                class=\"dropdown-item\"\n                                type=\"button\"\n                              >\n                                {{ property.label }}\n                              </button>\n                            </li>\n                            <p class=\"no-data-found\">Contact token not found</p>\n                          </div>\n                        </ul>\n                      </div>\n                    </div>\n\n                    <input :name=\"Object.keys(field)[0]\" :value=\"tokenValue || defaultUrl || ''\"\n                      @input=\"updateField($event, Object.keys(field)[0])\" @keydown.enter.prevent type=\"text\"\n                      class=\"form-control\" placeholder=\"Enter value\" required />\n                  </div>\n                </template>\n              </template>\n            </div>\n          </div>\n          <div class=\"modal-footer footer\">\n            <button :disabled=\"this.apiLoader\" type=\"submit\" class=\"btn btn-primary\">Send</button>\n            <Loader v-if=\"this.apiLoader\" />\n          </div>\n        </form>\n      </div>\n    </div>\n    <error-modal :show=\"this.sendtemplateError\" :toggle=\"hideErrorModal\" :error-message=\"this.errorMessage\" />\n  </div>\n</template>\n\n<script>\nimport axios from '@/utils/api.js'\nimport Loader from '../Loader/Loader.vue'\nimport templateData from './template.json'\nimport closeIcon from '../../../assets/icons/close-icon.svg'\nimport ErrorModal from '../ErrorModal/ErrorModal'\nimport infoIcon from '../../../assets/icons/info.svg'\n\nexport default {\n  name: 'TemplateModal',\n  emits: ['create-handler'],\n  components: {\n    Loader,\n    ErrorModal\n  },\n  props: {\n    selectedTemplate: Object,\n    roomPhone: String,\n    tokens: String,\n    objectId: Number,\n    defaultHeaderTemplateUrl: String,\n  },\n\n  data() {\n    return {\n      properties: [], // Holds the list of hubspot properties,\n      templateFields: {},\n      activeTokenModal: null,\n      apiLoader: false,\n      closeIcon: closeIcon,\n      infoIcon: infoIcon,\n      sendtemplateError: false,\n      errorMessage: 'Failed to send template, Please try again',\n      tokenValue: '',\n      defaultUrl: null,\n    }\n  },\n\n  computed: {\n    matchedDependency() {\n      return templateData.find(dep => dep.controllingFieldValue === this.selectedTemplate?.type)\n    },\n  },\n\n  created() {\n    const userData = this.$store.state.userData\n    this.user_id = userData.user_id\n    this.getHubspotProperties()\n  },\n\n  watch: {\n    defaultHeaderTemplateUrl: {\n      handler(newVal) {\n        if (newVal) {\n          let dependancyKey = templateData.find(dep => dep.controllingFieldValue === this.selectedTemplate?.type)\n          if (!dependancyKey || !dependancyKey.dependentFieldNames || dependancyKey.dependentFieldNames.length === 0) {\n            return;\n          }\n          let objectKey = Object.keys(dependancyKey?.dependentFieldNames[0])[0]\n          this.$set(this.templateFields, objectKey, newVal);\n          this.defaultUrl = newVal;\n        }\n      },\n      immediate: true,\n      deep: true,\n    }\n  },\n\n  methods: {\n    hideErrorModal() {\n      this.sendtemplateError = false\n    },\n\n    updateField(event, key) {\n      const decodedValue = decodeURIComponent(event?.target?.value || '');\n      this.$set(this.templateFields, key, decodedValue);\n      this.tokenValue = decodedValue;\n      this.defaultUrl = null;\n    },\n\n    // Token modal handler\n    tokenModalHandler(selectedData) {\n      if (this.activeTokenModal === selectedData) {\n        this.activeTokenModal = null\n      } else {\n        this.activeTokenModal = selectedData\n      }\n    },\n\n    closeModal() {\n      this.tokenValue = null\n      this.$emit('close-modal')\n      this.templateFields = {}\n      this.tokenModalHandler(null)\n    },\n\n    searchProperties(event) {\n      let searchValue = event.target.value.trim().toLowerCase()\n      let tokenItems = event.target.parentElement.parentElement.parentElement.querySelectorAll('.tokenItem')\n      let notfound = document.querySelector('.no-data-found')\n      let found = false // Track if any tokens are found\n\n      if (tokenItems) {\n        for (let i = 0; i < tokenItems.length; i++) {\n          let token = tokenItems[i]\n          let tokenName = token.getAttribute('data-name').trim().toLowerCase()\n\n          if (tokenName.includes(searchValue)) {\n            token.style.display = 'block'\n            found = true // A match is found\n          } else {\n            token.style.display = 'none'\n          }\n        }\n      }\n\n      // Display \"no data found\" message if no tokens are found\n      if (!found) {\n        notfound.style.display = 'flex'\n      } else {\n        notfound.style.display = 'none'\n      }\n    },\n\n    // Select token from dropdown list\n    addToken(token, target) {\n      if (!this.templateFields[target]) {\n        this.$set(this.templateFields, target, \"\"); // Initialize empty if not set\n      }\n      this.tokenValue = `[${token}]`\n      this.templateFields[target] = `[${token}]`;\n\n      this.defaultUrl = null;\n      this.activeTokenModal = null;\n    },\n\n    submitHandler(event) {\n      var urlString = window.location.href\n      var url = new URL(urlString)\n      const userId = url.searchParams.get('user_id')\n      const objectId = url.searchParams.get('objectId')\n      const phone = url.searchParams.get('phone')\n\n      let data = {\n        phone: phone,\n        objectId: objectId,\n        message: this.selectedTemplate.body,\n        templateId: this.selectedTemplate.id,\n        ...(this.templateFields && { fields: this.templateFields })\n      }\n\n      const reqData = {\n        user_id: userId,\n        ...data\n      }\n      this.apiLoader = true\n      try {\n        axios\n          .post(`api/v1/send-template`, reqData)\n          .then(response => {\n            this.apiLoader = false\n            if (response.data.ok) {\n              this.closeModal()\n              this.$emit('add-template-msg', response.data)\n            } else {\n              this.errorMessage = response.data.message\n              this.sendtemplateError = true\n            }\n          })\n          .catch(error => {\n            this.apiLoader = false\n            this.sendtemplateError = true\n            console.error(error)\n          })\n      } catch (err) {\n        this.apiLoader = false\n        console.log(err)\n      }\n    },\n\n    // Fetch hubspot properties\n    async getHubspotProperties() {\n      try {\n        const response = await axios.get(`api/hubspot/properties?user_id=${this.user_id}`)\n        this.properties = response.data.data.results\n      } catch (error) {\n        console.error(error)\n      }\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./TemplateModal.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./TemplateModal.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./TemplateModal.vue?vue&type=template&id=5179e753\"\nimport script from \"./TemplateModal.vue?vue&type=script&lang=js\"\nexport * from \"./TemplateModal.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div class=\"vac-room-header\">\n    <slot name=\"room-header\" v-bind=\"{ room, typingUsers }\">\n      <div class=\"vac-room-wrapper\">\n        <div class=\"vac-info-wrapper\">\n          <slot name=\"room-header-avatar\" v-bind=\"{ room }\">\n            <div\n              class=\"vac-avatar\"\n              :style=\"{\n                'background-image': `url('${room.avatar || dummyAvatar}')`\n              }\"\n            />\n          </slot>\n          <slot name=\"room-header-info\" v-bind=\"{ room, typingUsers }\">\n            <div class=\"vac-text-ellipsis\">\n              <div class=\"vac-room-name vac-text-ellipsis\">\n                {{ userName }}\n              </div>\n            </div>\n          </slot>\n        </div>\n        <div class=\"d-flex align-items-center\">\n          <!-- Template -->\n          <div class=\"hwa-template\">\n            <div class=\"external-link\">\n              <a\n                :href=\"`${config.baseURL}dashboard/?user_id=${userId}&accountUser=${accountUser}&portal_id=${portalId}&accountPhone=${accountPhone}`\"\n                target=\"_blank\"\n              >\n                <img :src=\"ExternalIcon\" alt=\"\" />\n              </a>\n              <span class=\"tooltip-span\">Go To Main DashBoard</span>\n            </div>\n            <div class=\"template-box\" @click.prevent=\"templateHandler\">\n              <p class=\"hwa-template_text\">Templates</p>\n              <img :src=\"DropDownIcon\" alt=\"\" srcset=\"\" :class=\"{ rotateSvg: templateOpened }\" />\n            </div>\n            <!-- templates list -->\n            <transition name=\"vac-slide-left\">\n              <div v-if=\"templateOpened\" v-click-outside=\"closeTemplates\" class=\"vac-menu-options\">\n                <div v-if=\"!localTemplates.length\" class=\"vac-menu-item no-template-center\">\n                  <a class=\"nav-link\" target=\"_blank\" href=\"https://business.facebook.com/wa/manage/message-templates\">\n                    Create Template\n                  </a>\n                </div>\n                <div v-if=\"localTemplates.length\">\n                  <div class=\"template-search-box\">\n                    <input\n                      v-model=\"searchTemplate\"\n                      @input=\"debouncedHandleKeypressWhatsApp(searchTemplate)\"\n                      type=\"text\"\n                      placeholder=\"Search Template\"\n                    />\n                    <img class=\"template-search-image_icon\" :src=\"searchIcon\" alt=\"search icon\" />\n                  </div>\n                  <ul class=\"hwa-menu-list\">\n                    <div v-if=\"loading\" class=\"vac-menu-item loading-item\">Searching templates...</div>\n\n                    <li\n                      :title=\"template.name\"\n                      v-for=\"template in filteredTemplates\"\n                      :key=\"template.id\"\n                      class=\"vac-menu-item\"\n                      @click.prevent=\"openModal(template)\"\n                    >\n                      {{ template.name }}\n                    </li>\n                    <li v-if=\"showNoDataMessage && !loading\" class=\"no-data-found\">Template not found</li>\n                  </ul>\n                </div>\n              </div>\n            </transition>\n          </div>\n        </div>\n      </div>\n    </slot>\n    <template-modal\n      v-if=\"isModalOpen\"\n      :selectedTemplate=\"selectedTemplate\"\n      :roomPhone=\"room.roomId\"\n      :objectId=\"room.object_id\"\n      :defaultHeaderTemplateUrl=\"defaultHeaderTemplateUrl\"\n      @close-modal=\"closeModal\"\n      @add-template-msg=\"forwardTemplateMsg\"\n    />\n  </div>\n</template>\n\n<script>\nimport vClickOutside from 'v-click-outside'\nimport RefreshMsg from '../../../components/SvgIcon/refresh-msg.svg'\nimport ExternalIcon from '../../../../assets/icons/external-link.svg'\nimport DummyAvatar from '../../../../assets/avatar.png'\nimport DropDownIcon from '../../../../assets/icons/dropdown.svg'\nimport SearchIcon from '../../../../assets/icons/searchicon.svg'\nimport typingText from '../../../utils/typing-text'\nimport TemplateModal from '../../../components/TemplateModal/TemplateModal.vue'\nimport config from '@/utils/config.js'\nimport axios from '@/utils/api.js'\n\nexport default {\n  name: 'RoomHeader',\n  components: {\n    TemplateModal\n  },\n\n  directives: {\n    clickOutside: vClickOutside.directive\n  },\n\n  props: {\n    currentUserId: { type: [String, Number], required: true },\n    textMessages: { type: Object, required: true },\n    singleRoom: { type: Boolean, required: true },\n    isMsgFetched: { type: Boolean, required: true },\n    menuActions: { type: Array, required: true },\n    room: { type: Object, required: true },\n    templates: { type: Array, required: true }\n  },\n\n  emits: ['add-template', 'handle-message-search', 'toggle-menu-bar', 'redirect-to-hubspot'],\n\n  data() {\n    return {\n      refreshIcon: RefreshMsg,\n      dummyAvatar: DummyAvatar,\n      DropDownIcon: DropDownIcon,\n      menuOpened: false,\n      searchIcon: SearchIcon,\n      templateOpened: false,\n      currentIdx: -1,\n      searchTemplate: '',\n      showNoDataMessage: false,\n      isModalOpen: false,\n      selectedTemplate: null,\n      roomPhone: String,\n      userName: '',\n      ExternalIcon: ExternalIcon,\n      localTemplates: [],\n      loading: false,\n      userId: '',\n      config: config,\n      defaultHeaderTemplateUrl: null\n    }\n  },\n\n  computed: {\n    typingUsers() {\n      return typingText(this.room, this.currentUserId, this.textMessages)\n    },\n\n    filteredTemplates() {\n      const filtered = this.localTemplates.filter(t => t.name.toLowerCase().includes(this.searchTemplate.toLowerCase()))\n\n      return filtered\n    }\n  },\n\n  created() {\n    var urlString = window.location.href\n    var url = new URL(urlString)\n\n    let firstName = url.searchParams.get('firstname')\n    let lastName = url.searchParams.get('lastname')\n    let phone = url.searchParams.get('phone')\n    this.userName =\n      firstName && firstName !== 'null' && firstName !== 'undefined'\n        ? lastName && lastName !== 'null' && lastName !== 'undefined'\n          ? `${firstName} ${lastName}`\n          : firstName\n        : lastName && lastName !== 'null' && lastName !== 'undefined'\n        ? lastName\n        : phone\n    this.userId = url.searchParams.get('user_id')\n    this.accountUser = url.searchParams.get('accountUser')\n    this.portalId = url.searchParams.get('portal_id')\n    this.accountPhone = url.searchParams.get('accountPhone')\n    this.localTemplates = this.templates\n    this.debouncedHandleKeypressWhatsApp = this.debounce(this.handleKeypressWhatsApp, 300)\n  },\n\n  watch: {\n    filteredTemplates(newFilteredTemplates) {\n      this.showNoDataMessage = newFilteredTemplates.length === 0\n    },\n    templates: {\n      immediate: true,\n      handler(newTemplates) {\n        if (newTemplates && newTemplates.length) {\n          this.localTemplates = [...newTemplates]\n        }\n      }\n    }\n  },\n\n  methods: {\n    debounce(func, wait) {\n      let timeout\n      return function (...args) {\n        const context = this\n        clearTimeout(timeout)\n        timeout = setTimeout(() => func.apply(context, args), wait)\n      }\n    },\n    async handleKeypressWhatsApp(searchText) {\n      let searchValue = searchText?.toLowerCase()\n\n      // Reset match flag\n      this.isMatch = false\n\n      // Handle empty search\n      if (searchValue.length === 0) {\n        return\n      }\n\n      // First check if we have local matches\n      const localMatches = this.localTemplates.filter(template => template.name.toLowerCase().includes(searchValue))\n\n      // Set isMatch if we have local matches\n      if (localMatches.length > 0) {\n        this.isMatch = true\n      }\n\n      // Only query API if we have 3+ characters AND no local matches\n      if (searchValue.length >= 3 && localMatches.length === 0) {\n        this.loading = true\n        try {\n          const response = await axios.post(`api/whatsapp/template`, {\n            user_id: this.userId,\n            filters: {\n              name: searchValue,\n              status: 'APPROVED',\n              parse: true\n            }\n          })\n          let matchTemplates = response?.data?.templates?.data || []\n\n          if (matchTemplates.length > 0) {\n            // Add unique templates to localTemplates\n            const uniqueNewTemplates = matchTemplates.filter(\n              newTemplate => !this.localTemplates.some(existingTemplate => existingTemplate.id === newTemplate.id)\n            )\n\n            if (uniqueNewTemplates.length > 0) {\n              this.localTemplates = this.localTemplates.concat(uniqueNewTemplates)\n              this.isMatch = true\n            }\n          }\n        } catch (error) {\n          console.error('Error fetching WhatsApp templates:', error)\n        } finally {\n          this.loading = false\n        }\n      }\n    },\n    forwardTemplateMsg(newItem) {\n      this.$emit('add-template-msg', newItem) // Forward template  msg to the parent\n    },\n\n    closeMenu() {\n      this.menuOpened = false\n    },\n\n    closeTemplates() {\n      this.templateOpened = false\n      this.searchTemplate = ''\n    },\n\n    templateHandler() {\n      this.templateOpened = !this.templateOpened\n    },\n\n    openModal(template) {\n      this.checkTemplateHeaderUrl(template?.id)\n      this.isModalOpen = true\n      this.selectedTemplate = template\n      this.templateOpened = !this.templateOpened\n      this.searchTemplate = ''\n    },\n\n    closeModal() {\n      this.isModalOpen = false\n      this.selectedTemplate = null\n    },\n\n    async checkTemplateHeaderUrl(templateId) {\n      this.defaultHeaderTemplateUrl = null\n      try {\n        const { data } = await axios.get(`api/template/media/${templateId}?user_id=${this.userId}`)\n        this.defaultHeaderTemplateUrl = data?.file_url\n      } catch (err) {\n        this.errorMessage = 'Unable to update'\n        setTimeout(() => (this.errorMessage = ''), 1000)\n        console.log(err)\n      }\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./RoomHeader.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./RoomHeader.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RoomHeader.vue?vue&type=template&id=b7b0b3ec\"\nimport script from \"./RoomHeader.vue?vue&type=script&lang=js\"\nexport * from \"./RoomHeader.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('transition',{attrs:{\"name\":\"vac-slide-up\"}},[(_vm.files.length)?_c('div',{staticClass:\"vac-room-files-container\",style:({ bottom: `${_vm.$parent.$refs.roomFooter.clientHeight}px` })},[_c('div',{staticClass:\"vac-files-box\"},_vm._l((_vm.files),function(file,i){return _c('div',{key:i},[_c('room-file',{attrs:{\"file\":file,\"index\":i},on:{\"remove-file\":function($event){return _vm.$emit('remove-file', $event)}}})],1)}),0),_c('div',{staticClass:\"vac-icon-close\"},[_c('div',{staticClass:\"vac-svg-button\",on:{\"click\":function($event){return _vm.$emit('reset-message')}}},[_vm._t(\"reply-close-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"close-outline\"}})]})],2)])]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"vac-room-file-container\"},[_c('div',{staticClass:\"vac-svg-button vac-icon-remove\",on:{\"click\":function($event){return _vm.$emit('remove-file', _vm.index)}}},[_vm._t(\"reply-close-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"close-outline\"}})]})],2),(_vm.isPdf)?_c('div',{staticClass:\"vac-file-container\"},[_c('div',{staticClass:\"file-icon\"},[_vm._t(\"file-icon\",function(){return [_c('img',{attrs:{\"src\":_vm.pdfIcon,\"alt\":\"PDF Icon\"}})]})],2),_c('div',{staticClass:\"vac-text-ellipsis\"},[_vm._v(\" \"+_vm._s(_vm.file.name)+\" \")])]):_c('div',{staticClass:\"vac-file-container\"},[_c('div',{staticClass:\"file-icon\"},[_vm._t(\"file-icon\",function(){return [_c('img',{attrs:{\"src\":_vm.docIcon,\"alt\":\"PDF Icon\"}})]})],2),_c('div',{staticClass:\"vac-text-ellipsis\"},[_vm._v(\" \"+_vm._s(_vm.file.name)+\" \")])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"vac-room-file-container\">\n    <div class=\"vac-svg-button vac-icon-remove\" @click=\"$emit('remove-file', index)\">\n      <slot name=\"reply-close-icon\">\n        <svg-icon name=\"close-outline\" />\n      </slot>\n    </div>\n\n    <div v-if=\"isPdf\" class=\"vac-file-container\">\n      <div class=\"file-icon\">\n        <slot name=\"file-icon\">\n          <img :src=\"pdfIcon\" alt=\"PDF Icon\" />\n        </slot>\n      </div>\n      <div class=\"vac-text-ellipsis\">\n        {{ file.name }}\n      </div>\n    </div>\n\n    <div v-else class=\"vac-file-container\">\n      <div class=\"file-icon\">\n        <slot name=\"file-icon\">\n          <img :src=\"docIcon\" alt=\"PDF Icon\" />\n        </slot>\n      </div>\n      <div class=\"vac-text-ellipsis\">\n        {{ file.name }}\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport SvgIcon from '../../../components/SvgIcon/SvgIcon'\nimport PdfIcon from '../../../../assets/icons/pdf_icon.png'\nimport DocIcon from '../../../../assets/icons/doc_icon.png'\n\nconst { isPdfFile } = require('../../../utils/media-file')\n\nexport default {\n  name: 'RoomFiles',\n  components: {\n    SvgIcon\n  },\n\n  props: {\n    file: { type: Object, required: true },\n    index: { type: Number, required: true }\n  },\n\n  emits: ['remove-file'],\n\n  data() {\n    return {\n      pdfIcon: PdfIcon,\n      docIcon: DocIcon\n    }\n  },\n\n  computed: {\n    isPdf() {\n      return isPdfFile(this.file)\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./RoomFile.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./RoomFile.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RoomFile.vue?vue&type=template&id=acc43c36\"\nimport script from \"./RoomFile.vue?vue&type=script&lang=js\"\nexport * from \"./RoomFile.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <transition name=\"vac-slide-up\">\n    <div\n      v-if=\"files.length\"\n      class=\"vac-room-files-container\"\n      :style=\"{ bottom: `${$parent.$refs.roomFooter.clientHeight}px` }\"\n    >\n      <div class=\"vac-files-box\">\n        <div v-for=\"(file, i) in files\" :key=\"i\">\n          <room-file :file=\"file\" :index=\"i\" @remove-file=\"$emit('remove-file', $event)\" />\n        </div>\n      </div>\n\n      <div class=\"vac-icon-close\">\n        <div class=\"vac-svg-button\" @click=\"$emit('reset-message')\">\n          <slot name=\"reply-close-icon\">\n            <svg-icon name=\"close-outline\" />\n          </slot>\n        </div>\n      </div>\n    </div>\n  </transition>\n</template>\n\n<script>\nimport SvgIcon from '../../../components/SvgIcon/SvgIcon'\n\nimport RoomFile from '../RoomFile/RoomFile'\n\nexport default {\n  name: 'RoomFiles',\n  components: {\n    SvgIcon,\n    RoomFile\n  },\n\n  props: {\n    files: { type: Array, required: true }\n  },\n\n  emits: ['remove-file', 'reset-message']\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./RoomFiles.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./RoomFiles.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RoomFiles.vue?vue&type=template&id=6d03ec3c\"\nimport script from \"./RoomFiles.vue?vue&type=script&lang=js\"\nexport * from \"./RoomFiles.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('transition',{attrs:{\"name\":\"vac-slide-up\"}},[(_vm.messageReply)?_c('div',{staticClass:\"vac-reply-container\",style:({ bottom: `${_vm.$parent.$refs.roomFooter.clientHeight}px` })},[_c('div',{staticClass:\"vac-reply-box\"},[_c('span',{staticClass:\"left-border\"}),_c('div',{staticClass:\"vac-reply-wrapper\"},[_c('div',{staticClass:\"vac-reply-info\"},[_c('div',{staticClass:\"vac-reply-content\"},[(_vm.messageReply.username)?_c('div',{staticClass:\"vac-reply-username\"},[_vm._v(\" \"+_vm._s(_vm.messageReply.username)+\" \")]):_vm._e(),_c('format-message',{attrs:{\"content\":_vm.messageReply.content,\"users\":_vm.room.users,\"text-formatting\":_vm.textFormatting,\"link-options\":_vm.linkOptions,\"reply\":true},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)})],1)]),(_vm.isImage)?_c('img',{staticClass:\"vac-image-reply\",attrs:{\"src\":_vm.firstFile.url}}):(_vm.isVideo)?_c('video',{staticClass:\"vac-image-reply\",attrs:{\"controls\":\"\"}},[_c('source',{attrs:{\"src\":_vm.firstFile.url}})]):(_vm.isOtherFile)?_c('div',{staticClass:\"vac-image-reply vac-file-container\"},[_c('div',{staticClass:\"reply-icon\"},[_vm._t(\"file-icon\",function(){return [_c('img',{attrs:{\"src\":_vm.docIcon}})]})],2),_c('div',{staticClass:\"vac-text-ellipsis\"},[_vm._v(\" \"+_vm._s(_vm.firstFile.name)+\" \")]),(_vm.firstFile.extension)?_c('div',{staticClass:\"vac-text-ellipsis vac-text-extension\"},[_vm._v(\" \"+_vm._s(_vm.firstFile.extension)+\" \")]):_vm._e()]):_vm._e()])]),_c('div',{staticClass:\"vac-icon-reply\"},[_c('div',{staticClass:\"vac-svg-button\",on:{\"click\":function($event){return _vm.$emit('reset-message')}}},[_vm._t(\"reply-close-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"close-outline\"}})]})],2)])]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"vac-format-message-wrapper\",class:{ 'vac-text-ellipsis': _vm.singleLine }},[(_vm.textFormatting)?_c('div',{class:{ 'vac-text-ellipsis': _vm.singleLine }},[(_vm.textFormatting)?_vm._l((_vm.filteredLinkifiedMessage),function(message,i){return _c('div',{key:i,staticClass:\"vac-format-container\"},[(message.type === 'group')?_c('span',{staticClass:\"msg msg_box\"},_vm._l((message.parts),function(part,index){return _c(part.url ? 'a' : 'span',{key:index,tag:\"component\",class:{\n            'vac-text-ellipsis': _vm.singleLine,\n            'vac-text-bold': part.bold,\n            'vac-text-italic': _vm.deleted || part.italic,\n            'vac-text-strike': part.strike,\n            'vac-text-underline': part.underline,\n            'vac-text-inline-code': !_vm.singleLine && part.inline,\n            'vac-text-multiline-code': !_vm.singleLine && part.multiline,\n            'vac-text-tag': !_vm.singleLine && !_vm.reply && part.tag\n          },attrs:{\"href\":part.href,\"target\":part.href ? _vm.linkOptions.target : null,\"rel\":part.href ? _vm.linkOptions.rel : null}},[_c('text-highlight',{attrs:{\"queries\":_vm.queries}},[_vm._v(_vm._s(part.value))])],1)}),1):_c(message.url ? 'a' : 'span',{tag:\"component\",class:{\n          'vac-text-ellipsis': _vm.singleLine,\n          'vac-text-bold': message.bold,\n          'vac-text-italic': _vm.deleted || message.italic,\n          'vac-text-strike': message.strike,\n          'vac-text-underline': message.underline,\n          'vac-text-inline-code': !_vm.singleLine && message.inline,\n          'vac-text-multiline-code': !_vm.singleLine && message.multiline,\n          'vac-text-tag': !_vm.singleLine && !_vm.reply && message.tag\n        },attrs:{\"href\":message.href,\"target\":message.href ? _vm.linkOptions.target : null,\"rel\":message.href ? _vm.linkOptions.rel : null}},[_c('span',{staticClass:\"msg msg_box\"},[_c('text-highlight',{attrs:{\"queries\":_vm.queries}},[_vm._v(_vm._s(message.value))])],1)])],1)}):_vm._e()],2):_c('div',[_vm._v(\" \"+_vm._s(_vm.formattedContent)+\" \")])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "const linkify = require('linkifyjs')\n// require('linkifyjs/plugins/hashtag')(linkify);\n\nexport default (text, doLinkify) => {\n  // Preprocess the text to convert double asterisks to single asterisks\n  text = preprocessText(text)\n\n  const json = compileToJSON(text)\n\n  const html = compileToHTML(json)\n\n  const result = [].concat.apply([], html)\n\n  if (doLinkify) linkifyResult(result)\n\n  return result\n}\n\nconst preprocessText = text => {\n  // return text.replace(/\\*\\*/g, '*')\n  text = text.replace(/\\*\\*/g, '*')\n\n  const validBoldMatches = [...text.matchAll(/\\*[^*\\n]+\\*/g)].map(m => m.index);\n\n  let cleaned = '';\n  let skipNext = false;\n\n  for (let i = 0; i < text.length; i++) {\n    if (text[i] === '*' && !skipNext) {\n      // Check if this * is part of a valid pair\n      const isPartOfBold = validBoldMatches.some(start => i === start || i === start + text.slice(start).indexOf('*', 1));\n\n      if (!isPartOfBold) {\n        continue; // Skip this lone asterisk\n      }\n    }\n\n    cleaned += text[i];\n  }\n\n  return cleaned;\n}\n\nconst typeMarkdown = {\n  bold: '*',\n  italic: '_',\n  strike: '~',\n  underline: '°'\n}\n\nconst pseudoMarkdown = {\n  [typeMarkdown.bold]: {\n    end: '\\\\' + [typeMarkdown.bold],\n    allowed_chars: '.',\n    type: 'bold'\n  },\n  [typeMarkdown.italic]: {\n    end: [typeMarkdown.italic],\n    allowed_chars: '[^_]',\n    type: 'italic'\n  },\n  [typeMarkdown.strike]: {\n    end: [typeMarkdown.strike],\n    allowed_chars: '.',\n    type: 'strike'\n  },\n  [typeMarkdown.underline]: {\n    end: [typeMarkdown.underline],\n    allowed_chars: '.',\n    type: 'underline'\n  },\n  '```': {\n    end: '```',\n    allowed_chars: '(.|\\n)',\n    type: 'multiline-code'\n  },\n  '`': {\n    end: '`',\n    allowed_chars: '.',\n    type: 'inline-code'\n  },\n  '<usertag>': {\n    allowed_chars: '.',\n    end: '</usertag>',\n    type: 'tag'\n  }\n}\n\nfunction compileToJSON(str) {\n  let result = []\n  let minIndexOf = -1\n  let minIndexOfKey = null\n\n  const links = linkify.find(str)\n  let minIndexFromLink = false\n\n  if (links.length > 0) {\n    minIndexOf = str.indexOf(links[0].value)\n    minIndexFromLink = true\n  }\n\n  Object.keys(pseudoMarkdown).forEach(startingValue => {\n    const io = str.indexOf(startingValue)\n    if (io >= 0 && (minIndexOf < 0 || io < minIndexOf)) {\n      minIndexOf = io\n      minIndexOfKey = startingValue\n      minIndexFromLink = false\n    }\n  })\n\n  if (minIndexFromLink && minIndexOfKey !== -1) {\n    const strLeft = str.substr(0, minIndexOf)\n    const strLink = str.substr(minIndexOf, links[0].value.length)\n    const strRight = str.substr(minIndexOf + links[0].value.length)\n    result.push(strLeft)\n    result.push(strLink)\n    result = result.concat(compileToJSON(strRight))\n    return result\n  }\n\n  if (minIndexOfKey) {\n    let strLeft = str.substr(0, minIndexOf)\n    const char = minIndexOfKey\n    let strRight = str.substr(minIndexOf + char.length)\n\n    if (str.replace(/\\s/g, '').length === char.length * 2) {\n      return [str]\n    }\n\n    const match = strRight.match(\n      new RegExp(\n        '^(' +\n          (pseudoMarkdown[char].allowed_chars || '.') +\n          '*' +\n          (pseudoMarkdown[char].end ? '?' : '') +\n          ')' +\n          (pseudoMarkdown[char].end ? '(' + pseudoMarkdown[char].end + ')' : ''),\n        'm'\n      )\n    )\n    if (!match || !match[1]) {\n      strLeft = strLeft + char\n      result.push(strLeft)\n    } else {\n      if (strLeft) {\n        result.push(strLeft)\n      }\n      const object = {\n        start: char,\n        content: compileToJSON(match[1]),\n        end: match[2],\n        type: pseudoMarkdown[char].type\n      }\n      result.push(object)\n      strRight = strRight.substr(match[0].length)\n    }\n    result = result.concat(compileToJSON(strRight))\n    return result\n  } else {\n    if (str) {\n      return [str]\n    } else {\n      return []\n    }\n  }\n}\n\nfunction compileToHTML(json) {\n  const result = []\n\n  json.forEach(item => {\n    if (typeof item === 'string') {\n      result.push({ types: [], value: item })\n    } else {\n      if (pseudoMarkdown[item.start]) {\n        result.push(parseContent(item))\n      }\n    }\n  })\n\n  return result\n}\n\nfunction parseContent(item) {\n  const result = []\n\n  item.content.forEach(it => {\n    if (typeof it === 'string') {\n      result.push({\n        types: [item.type],\n        value: it\n      })\n    } else {\n      it.content.forEach(i => {\n        if (typeof i === 'string') {\n          result.push({\n            types: [it.type].concat([item.type]),\n            value: i\n          })\n        } else {\n          result.push({\n            types: [i.type].concat([it.type]).concat([item.type]),\n            value: parseContent(i)\n          })\n        }\n      })\n    }\n  })\n\n  return result\n}\n\nfunction linkifyResult(array) {\n  const result = []\n\n  array.forEach(arr => {\n    const links = linkify.find(arr.value)\n\n    if (links.length) {\n      const spaces = arr.value.replace(links[0].value, '')\n      result.push({ types: arr.types, value: spaces })\n\n      arr.types = ['url'].concat(arr.types)\n      arr.href = links[0].href\n      arr.value = links[0].value\n    }\n\n    result.push(arr)\n  })\n\n  return result\n}\n", "<template>\n  <div class=\"vac-format-message-wrapper\" :class=\"{ 'vac-text-ellipsis': singleLine }\">\n    <div v-if=\"textFormatting\" :class=\"{ 'vac-text-ellipsis': singleLine }\">\n      <template v-if=\"textFormatting\">\n        <div v-for=\"(message, i) in filteredLinkifiedMessage\" :key=\"i\" class=\"vac-format-container\">\n          <!-- Group message -->\n          <span v-if=\"message.type === 'group'\" class=\"msg msg_box\">\n            <component v-for=\"(part, index) in message.parts\" :key=\"index\" :is=\"part.url ? 'a' : 'span'\" :class=\"{\n              'vac-text-ellipsis': singleLine,\n              'vac-text-bold': part.bold,\n              'vac-text-italic': deleted || part.italic,\n              'vac-text-strike': part.strike,\n              'vac-text-underline': part.underline,\n              'vac-text-inline-code': !singleLine && part.inline,\n              'vac-text-multiline-code': !singleLine && part.multiline,\n              'vac-text-tag': !singleLine && !reply && part.tag\n            }\" :href=\"part.href\" :target=\"part.href ? linkOptions.target : null\"\n              :rel=\"part.href ? linkOptions.rel : null\">\n              <text-highlight :queries=\"queries\">{{ part.value }}</text-highlight>\n            </component>\n          </span>\n\n          <!-- Single message -->\n          <component v-else :is=\"message.url ? 'a' : 'span'\" :class=\"{\n            'vac-text-ellipsis': singleLine,\n            'vac-text-bold': message.bold,\n            'vac-text-italic': deleted || message.italic,\n            'vac-text-strike': message.strike,\n            'vac-text-underline': message.underline,\n            'vac-text-inline-code': !singleLine && message.inline,\n            'vac-text-multiline-code': !singleLine && message.multiline,\n            'vac-text-tag': !singleLine && !reply && message.tag\n          }\" :href=\"message.href\" :target=\"message.href ? linkOptions.target : null\"\n            :rel=\"message.href ? linkOptions.rel : null\">\n            <span class=\"msg msg_box\">\n              <text-highlight :queries=\"queries\">{{ message.value }}</text-highlight>\n            </span>\n          </component>\n        </div>\n      </template>\n\n    </div>\n    <div v-else>\n      {{ formattedContent }}\n    </div>\n  </div>\n</template>\n<script>\nimport SvgIcon from '../SvgIcon/SvgIcon'\nimport TextHighlight from 'vue-text-highlight'\nimport formatString from '../../utils/format-string'\nimport { IMAGE_TYPES } from '../../utils/constants'\nexport default {\n  name: 'FormatMessage',\n  components: { SvgIcon, TextHighlight },\n  props: {\n    content: { type: [String, Number], required: false },\n    deleted: { type: Boolean, default: false },\n    users: { type: Array, default: () => [] },\n    linkify: { type: Boolean, default: true },\n    singleLine: { type: Boolean, default: false },\n    reply: { type: Boolean, default: false },\n    textFormatting: { type: Boolean, required: true },\n    linkOptions: { type: Object, required: true },\n    msgSearchQuery: { type: String, default: () => '' },\n    isGroup: { type: Boolean, default: true }\n  },\n  emits: ['open-user-tag'],\n  computed: {\n    queries() {\n      return [this.msgSearchQuery]\n    },\n    linkifiedMessage() {\n      if (!this.content) {\n        return null\n      }\n      let message = formatString(\n        this.formatTags(this.content),\n        this.linkify && !this.linkOptions.disabled,\n        this.linkOptions\n      )\n\n      message.forEach(m => {\n        m.value = this.cleanText(m.value);\n        m.url = this.checkType(m, 'url');\n        m.bold = this.checkType(m, 'bold');\n        m.italic = this.checkType(m, 'italic');\n        m.strike = this.checkType(m, 'strike');\n        m.underline = this.checkType(m, 'underline');\n        m.inline = this.checkType(m, 'inline-code');\n        m.multiline = this.checkType(m, 'multiline-code');\n        m.tag = this.checkType(m, 'tag');\n        m.image = this.checkImageType(m);\n      });\n\n      return this.groupInlineMessages(message);\n    },\n    filteredLinkifiedMessage() {\n      if (!Array.isArray(this.linkifiedMessage)) return [];\n\n      return this.linkifiedMessage.filter(m =>\n        (m.type === 'group' && m.parts?.length) ||\n        (m.value && m.value.trim())\n      );\n    }\n  },\n  methods: {\n    groupInlineMessages(messages) {\n      const grouped = [];\n      let buffer = [];\n      const flushBuffer = () => {\n        if (buffer.length > 0) {\n          grouped.push({\n            type: 'group',\n            parts: buffer.map(m => ({ ...m })), // keep individual formatting\n          });\n          buffer = [];\n        }\n      };\n      for (const msg of messages) {\n        if (msg.value === '\\n' || msg.value === '\\n\\n' || /^\\s*$/.test(msg.value)) {\n          flushBuffer();\n          grouped.push(msg); // preserve line break\n        } else {\n          buffer.push(msg);\n        }\n      }\n      flushBuffer();\n      return grouped;\n    },\n    cleanText(text) {\n      // Removes zero-width spaces, non-breaking spaces, etc.\n      return text.replace(/[\\u200B-\\u200D\\uFEFF\\u00A0]/g, '');\n    },\n    checkType(message, type) {\n      return message.types.indexOf(type) !== -1\n    },\n    checkImageType(message) {\n      let index = message.value.lastIndexOf('.')\n      const slashIndex = message.value.lastIndexOf('/')\n      if (slashIndex > index) index = -1\n      const type = message.value.substring(index + 1, message.value.length)\n      const isMedia = index > 0 && IMAGE_TYPES.some(t => type.toLowerCase().includes(t))\n      if (isMedia) this.setImageSize(message)\n      return isMedia\n    },\n    setImageSize(message) {\n      const image = new Image()\n      image.src = message.value\n      image.addEventListener('load', onLoad)\n      function onLoad(img) {\n        if (!img?.path) {\n          image.removeEventListener('load', onLoad)\n          return\n        }\n        const ratio = img?.path[0].width / 150\n        message.height = Math.round(img?.path[0]?.height / ratio) + 'px'\n        image.removeEventListener('load', onLoad)\n      }\n    },\n    formatTags(content) {\n      if (!content) {\n        return content\n      }\n      const firstTag = '<usertag>'\n      const secondTag = '</usertag>'\n      const usertags = [...content.matchAll(new RegExp(firstTag, 'gi'))].map(a => a.index)\n      const initialContent = content\n      usertags.forEach(index => {\n        const userId = initialContent.substring(index + firstTag.length, initialContent.indexOf(secondTag, index))\n        const user = this.users.find(user => user._id === userId)\n        content = content.replaceAll(userId, `@${user?.username || 'unknown'}`)\n      })\n      return content\n    },\n    openTag(message) {\n      if (!this.singleLine && this.checkType(message, 'tag')) {\n        const user = this.users.find(u => message.value.indexOf(u.username) !== -1)\n        this.$emit('open-user-tag', user)\n      }\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./FormatMessage.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./FormatMessage.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./FormatMessage.vue?vue&type=template&id=7456541c\"\nimport script from \"./FormatMessage.vue?vue&type=script&lang=js\"\nexport * from \"./FormatMessage.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <transition name=\"vac-slide-up\">\n    <div\n      v-if=\"messageReply\"\n      class=\"vac-reply-container\"\n      :style=\"{ bottom: `${$parent.$refs.roomFooter.clientHeight}px` }\"\n    >\n      <div class=\"vac-reply-box\">\n        <span class=\"left-border\" />\n        <div class=\"vac-reply-wrapper\">\n          <div class=\"vac-reply-info\">\n            <div class=\"vac-reply-content\">\n              <div v-if=\"messageReply.username\" class=\"vac-reply-username\">\n                {{ messageReply.username }}\n              </div>\n              <format-message\n                :content=\"messageReply.content\"\n                :users=\"room.users\"\n                :text-formatting=\"textFormatting\"\n                :link-options=\"linkOptions\"\n                :reply=\"true\"\n              >\n                <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n                  <slot :name=\"name\" v-bind=\"data\" />\n                </template>\n              </format-message>\n            </div>\n          </div>\n\n          <img v-if=\"isImage\" :src=\"firstFile.url\" class=\"vac-image-reply\" />\n\n          <video v-else-if=\"isVideo\" controls class=\"vac-image-reply\">\n            <source :src=\"firstFile.url\" />\n          </video>\n\n          <div v-else-if=\"isOtherFile\" class=\"vac-image-reply vac-file-container\">\n            <div class=\"reply-icon\">\n              <slot name=\"file-icon\">\n                <img :src=\"docIcon\" />\n              </slot>\n            </div>\n            <div class=\"vac-text-ellipsis\">\n              {{ firstFile.name }}\n            </div>\n            <div v-if=\"firstFile.extension\" class=\"vac-text-ellipsis vac-text-extension\">\n              {{ firstFile.extension }}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"vac-icon-reply\">\n        <div class=\"vac-svg-button\" @click=\"$emit('reset-message')\">\n          <slot name=\"reply-close-icon\">\n            <svg-icon name=\"close-outline\" />\n          </slot>\n        </div>\n      </div>\n    </div>\n  </transition>\n</template>\n\n<script>\nimport SvgIcon from '../../../components/SvgIcon/SvgIcon'\nimport FormatMessage from '../../../components/FormatMessage/FormatMessage'\nimport DocIcon from '../../../../assets/icons/doc_icon.png'\n\nconst { isImageFile, isVideoFile } = require('../../../utils/media-file')\n\nexport default {\n  name: 'RoomMessageReply',\n  components: {\n    SvgIcon,\n    FormatMessage\n  },\n\n  props: {\n    room: { type: Object, required: true },\n    messageReply: { type: Object, default: null },\n    textFormatting: { type: Boolean, required: true },\n    linkOptions: { type: Object, required: true }\n  },\n\n  emits: ['reset-message'],\n\n  data() {\n    return {\n      docIcon: DocIcon\n    }\n  },\n\n  computed: {\n    firstFile() {\n      return this.messageReply.files ? this.messageReply.files[0] : {}\n    },\n    isImage() {\n      return isImageFile(this.firstFile)\n    },\n    isVideo() {\n      return isVideoFile(this.firstFile)\n    },\n\n    isOtherFile() {\n      return this.messageReply.files && !this.isVideo && !this.isImage\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./RoomMessageReply.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./RoomMessageReply.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RoomMessageReply.vue?vue&type=template&id=10b2fdba\"\nimport script from \"./RoomMessageReply.vue?vue&type=script&lang=js\"\nexport * from \"./RoomMessageReply.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('transition',{attrs:{\"name\":\"vac-slide-up\"}},[(_vm.filteredEmojis.length)?_c('div',{staticClass:\"vac-emojis-container\",style:({ bottom: `${_vm.$parent.$refs.roomFooter.clientHeight}px` })},_vm._l((_vm.filteredEmojis),function(emoji,index){return _c('div',{key:emoji,staticClass:\"vac-emoji-element\",class:{ 'vac-emoji-element-active': index === _vm.activeItem },on:{\"mouseover\":function($event){_vm.activeItem = index},\"click\":function($event){return _vm.$emit('select-emoji', emoji)}}},[_vm._v(\" \"+_vm._s(emoji)+\" \")])}),0):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <transition name=\"vac-slide-up\">\n    <div\n      v-if=\"filteredEmojis.length\"\n      class=\"vac-emojis-container\"\n      :style=\"{ bottom: `${$parent.$refs.roomFooter.clientHeight}px` }\"\n    >\n      <div\n        v-for=\"(emoji, index) in filteredEmojis\"\n        :key=\"emoji\"\n        class=\"vac-emoji-element\"\n        :class=\"{ 'vac-emoji-element-active': index === activeItem }\"\n        @mouseover=\"activeItem = index\"\n        @click=\"$emit('select-emoji', emoji)\"\n      >\n        {{ emoji }}\n      </div>\n    </div>\n  </transition>\n</template>\n\n<script>\nexport default {\n  name: 'RoomEmojis',\n\n  props: {\n    filteredEmojis: { type: Array, required: true },\n    selectItem: { type: Boolean, default: null },\n    activeUpOrDown: { type: Number, default: null }\n  },\n\n  emits: ['select-emoji', 'active-item'],\n\n  data() {\n    return {\n      activeItem: null\n    }\n  },\n\n  watch: {\n    filteredEmojis() {\n      this.activeItem = 0\n    },\n    selectItem(val) {\n      if (val) {\n        this.$emit('select-emoji', this.filteredEmojis[this.activeItem])\n      }\n    },\n    activeUpOrDown() {\n      if (this.activeUpOrDown > 0 && this.activeItem < this.filteredEmojis.length - 1) {\n        this.activeItem++\n      } else if (this.activeUpOrDown < 0 && this.activeItem > 0) {\n        this.activeItem--\n      }\n      this.$emit('activate-item')\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./RoomEmojis.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./RoomEmojis.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RoomEmojis.vue?vue&type=template&id=2bc087ac\"\nimport script from \"./RoomEmojis.vue?vue&type=script&lang=js\"\nexport * from \"./RoomEmojis.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{ref:_vm.message._id,staticClass:\"vac-message-wrapper\",attrs:{\"id\":_vm.message._id}},[(_vm.showDate)?_c('div',{staticClass:\"vac-card-info vac-card-date\"},[_vm._v(\" \"+_vm._s(_vm.message.date)+\" \")]):_vm._e(),(_vm.unreadCounts[_vm.roomId]?.msg_id === _vm.message._id)?_c('div',{staticClass:\"vac-line-new\"},[_vm._v(\" \"+_vm._s(_vm.textMessages.NEW_MESSAGES)+\" \")]):_vm._e(),(_vm.message.system)?_c('div',{staticClass:\"vac-card-info vac-card-system\"},[_c('format-message',{attrs:{\"content\":_vm.message.content,\"users\":_vm.roomUsers,\"text-formatting\":_vm.textFormatting,\"link-options\":_vm.linkOptions,\"msg-search-query\":_vm.msgSearchQuery}},[_vm._l((_vm.$scopedSlots),function(i,name){return [_vm._t(name,null,null,_vm.data)]})],2)],1):_c('div',{staticClass:\"vac-message-box\",class:{ 'vac-offset-current': _vm.message.fromMe === 1 }},[_vm._t(\"message\",function(){return [_c('div',{staticClass:\"vac-message-container\",class:{\n          'vac-message-container-offset': _vm.messageOffset\n        }},[_c('div',{staticClass:\"vac-message-card\",class:{\n            'vac-message-highlight': _vm.isMessageHover,\n            'vac-message-wait': !_vm.message.saved && _vm.message.fromMe === 1,\n            'vac-message-current': _vm.message.fromMe === 1,\n            'vac-message-deleted': _vm.message.deleted,\n            'no-event': _vm.message.files ? _vm.message.files[0].loading : null\n          },attrs:{\"id\":`${_vm.message._id}-child`},on:{\"mouseover\":_vm.onHoverMessage,\"mouseleave\":_vm.onLeaveMessage}},[(_vm.isGroup && _vm.messageOffset)?_c('div',{staticClass:\"vac-text-username\",class:{\n              'vac-username-reply': !_vm.message.deleted && _vm.message.replyMessage\n            }},[_c('span',[_vm._v(_vm._s(_vm.message.username))])]):_vm._e(),(!_vm.message.deleted && _vm.message.replyMessage)?_c('message-reply',{attrs:{\"message\":_vm.message,\"room-users\":_vm.roomUsers,\"text-formatting\":_vm.textFormatting,\"link-options\":_vm.linkOptions,\"is-group\":_vm.isGroup},on:{\"reply-msg-handler\":function($event){return _vm.$emit('reply-msg-handler', _vm.message)}}},[_vm._l((_vm.$scopedSlots),function(i,name){return [_vm._t(name,null,null,_vm.data)]})],2):_vm._e(),(_vm.message.deleted)?_c('div',[_vm._t(\"deleted-icon\",function(){return [_c('svg-icon',{staticClass:\"vac-icon-deleted\",attrs:{\"name\":\"deleted\"}})]}),_c('span',[_vm._v(_vm._s(_vm.textMessages.MESSAGE_DELETED))])],2):(!_vm.message.files || !_vm.message.files.length)?_c('format-message',{attrs:{\"content\":_vm.message.content,\"users\":_vm.roomUsers,\"text-formatting\":_vm.textFormatting,\"link-options\":_vm.linkOptions,\"msg-search-query\":_vm.msgSearchQuery}},[_vm._l((_vm.$scopedSlots),function(i,name){return [_vm._t(name,null,null,_vm.data)]})],2):(!_vm.isAudio || _vm.message.files.length > 1)?_c('message-files',{attrs:{\"current-user-id\":_vm.currentUserId,\"message\":_vm.message,\"room-users\":_vm.roomUsers,\"text-formatting\":_vm.textFormatting,\"link-options\":_vm.linkOptions,\"msg-search-query\":_vm.msgSearchQuery},on:{\"open-file\":_vm.openFile,\"carousel-handler\":_vm.carouselHandler}},[_vm._l((_vm.$scopedSlots),function(i,name){return [_vm._t(name,null,null,_vm.data)]})],2):[_c('div',{class:{ 'vac-loading': _vm.message.files[0].loading }},[_c('audio-player',{attrs:{\"src\":_vm.message.files[0].url},on:{\"update-progress-time\":function($event){_vm.progressTime = $event},\"hover-audio-progress\":function($event){_vm.hoverAudioProgress = $event}}},[_vm._l((_vm.$scopedSlots),function(i,name){return [_vm._t(name,null,null,_vm.data)]})],2),(!_vm.message.deleted)?_c('div',{staticClass:\"vac-progress-time\"},[_vm._v(\" \"+_vm._s(_vm.progressTime)+\" \")]):_vm._e(),_c('div',{staticClass:\"vac-text-timestamp\"},[(_vm.message.edited && !_vm.message.deleted)?_c('div',{staticClass:\"vac-icon-edited\"},[_vm._t(\"pencil-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"pencil\"}})]})],2):_vm._e(),_c('span',[_vm._v(_vm._s(_vm.message.timestamp))]),(_vm.isCheckmarkVisible)?_c('span',[_vm._t(\"checkmark-icon\",function(){return [_c('svg-icon',{staticClass:\"vac-icon-check\",attrs:{\"name\":_vm.message.distributed === 'wait' && !_vm.message.saved\n                          ? 'wait'\n                          : _vm.message.distributed\n                          ? 'double-checkmark'\n                          : 'checkmark',\"param\":_vm.message.seen ? 'seen' : ''}})]},null,{ message: _vm.message })],2):_vm._e()])],1)],(_vm.showTimeStamp)?_c('div',{staticClass:\"vac-text-timestamp\"},[(_vm.message.edited && !_vm.message.deleted)?_c('div',{staticClass:\"vac-icon-edited\"},[_vm._t(\"pencil-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"pencil\"}})]})],2):_vm._e(),_c('span',[_vm._v(_vm._s(_vm.message.timestamp))]),(_vm.isCheckmarkVisible)?_c('span',{directives:[{name:\"tooltip\",rawName:\"v-tooltip\",value:(_vm.message.reason),expression:\"message.reason\"}]},[_vm._t(\"checkmark-icon\",function(){return [_c('svg-icon',{staticClass:\"vac-icon-check\",attrs:{\"reason\":_vm.message.reason,\"name\":_vm.message.failed\n                      ? 'error'\n                      : _vm.message.distributed === 'wait' && !_vm.message.saved\n                      ? 'wait'\n                      : _vm.message.distributed\n                      ? 'double-checkmark'\n                      : 'checkmark',\"param\":_vm.message.seen ? 'seen' : ''}})]},null,{ message: _vm.message })],2):_vm._e()]):_vm._e(),_c('message-actions',{attrs:{\"current-user-id\":_vm.currentUserId,\"message\":_vm.message,\"message-actions\":_vm.messageActions,\"room-footer-ref\":_vm.roomFooterRef,\"show-reaction-emojis\":_vm.showReactionEmojis,\"hide-options\":_vm.hideOptions,\"message-hover\":_vm.messageHover,\"hover-message-id\":_vm.hoverMessageId,\"hover-audio-progress\":_vm.hoverAudioProgress,\"toggle-labels-modal\":_vm.toggleLabelsModal},on:{\"hide-options\":function($event){return _vm.$emit('hide-options', false)},\"update-message-hover\":function($event){_vm.messageHover = $event},\"update-options-opened\":function($event){_vm.optionsOpened = $event},\"update-emoji-opened\":function($event){_vm.emojiOpened = $event},\"message-action-handler\":_vm.messageActionHandler,\"send-message-reaction\":_vm.sendMessageReaction,\"open-forward-modal\":_vm.openForwardModal,\"toggle-error-modal\":_vm.toggleErrorModal}},[_vm._l((_vm.$scopedSlots),function(i,name){return [_vm._t(name,null,null,_vm.data)]})],2)],2),_c('message-reactions',{attrs:{\"current-user-id\":_vm.currentUserId,\"message\":_vm.message},on:{\"send-message-reaction\":_vm.sendMessageReaction}})],1)]},null,{ message: _vm.message })],2)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"vac-reply-message\",on:{\"click\":function($event){return _vm.$emit('reply-msg-handler', _vm.message)}}},[(_vm.isGroup)?_c('div',{staticClass:\"vac-reply-username\"},[_vm._v(\" \"+_vm._s(_vm.replyUsername)+\" \")]):_vm._e(),(_vm.isImage)?_c('div',{staticClass:\"vac-image-reply-container\"},[_c('div',{staticClass:\"vac-message-image vac-message-image-reply\",style:({\n        'background-image': `url('${_vm.firstFile.url}')`\n      })})]):(_vm.isVideo)?_c('div',{staticClass:\"vac-video-reply-container\"},[_c('video',{attrs:{\"width\":\"100%\",\"height\":\"100%\",\"controls\":\"\"}},[_c('source',{attrs:{\"src\":_vm.firstFile.url}})])]):(_vm.containsFile)?_c('div',{staticClass:\"vac-reply-username hwa-filename\"},[_c('img',{staticClass:\"d-block\",attrs:{\"src\":_vm.fileIcon,\"alt\":\"File Icon\",\"height\":\"30\"}}),_c('span',{staticClass:\"name\"},[_vm._v(_vm._s(`${_vm.firstFile.name}.`))]),_c('span',{staticClass:\"ext\"},[_vm._v(_vm._s(`${_vm.firstFile.extension}`))])]):_vm._e(),_c('div',{staticClass:\"vac-reply-content\"},[_c('format-message',{attrs:{\"content\":_vm.message.replyMessage.content,\"users\":_vm.roomUsers,\"text-formatting\":_vm.textFormatting,\"link-options\":_vm.linkOptions,\"reply\":true},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)})],1)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"vac-reply-message\" @click=\"$emit('reply-msg-handler', message)\">\n    <div v-if=\"isGroup\" class=\"vac-reply-username\">\n      {{ replyUsername }}\n    </div>\n\n    <div v-if=\"isImage\" class=\"vac-image-reply-container\">\n      <div\n        class=\"vac-message-image vac-message-image-reply\"\n        :style=\"{\n          'background-image': `url('${firstFile.url}')`\n        }\"\n      />\n    </div>\n\n    <div v-else-if=\"isVideo\" class=\"vac-video-reply-container\">\n      <video width=\"100%\" height=\"100%\" controls>\n        <source :src=\"firstFile.url\" />\n      </video>\n    </div>\n\n    <div v-else-if=\"containsFile\" class=\"vac-reply-username hwa-filename\">\n      <img class=\"d-block\" :src=\"fileIcon\" alt=\"File Icon\" height=\"30\" />\n\n      <span class=\"name\">{{ `${firstFile.name}.` }}</span>\n      <span class=\"ext\">{{ `${firstFile.extension}` }}</span>\n    </div>\n\n    <div class=\"vac-reply-content\">\n      <format-message\n        :content=\"message.replyMessage.content\"\n        :users=\"roomUsers\"\n        :text-formatting=\"textFormatting\"\n        :link-options=\"linkOptions\"\n        :reply=\"true\"\n      >\n        <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n          <slot :name=\"name\" v-bind=\"data\" />\n        </template>\n      </format-message>\n    </div>\n  </div>\n</template>\n\n<script>\nimport FormatMessage from '../../../components/FormatMessage/FormatMessage'\nimport FileIcon from '../../../../assets/icons/file_icon.png'\n\nconst { isImageFile, isVideoFile } = require('../../../utils/media-file')\n\nexport default {\n  name: 'MessageReply',\n  components: {\n    FormatMessage\n  },\n\n  props: {\n    message: { type: Object, required: true },\n    textFormatting: { type: Boolean, required: true },\n    linkOptions: { type: Object, required: true },\n    roomUsers: { type: Array, required: true },\n    isGroup: { type: Boolean, default: false }\n  },\n\n  emits: ['reply-msg-handler'],\n\n  data() {\n    return {\n      fileIcon: FileIcon\n    }\n  },\n\n  computed: {\n    replyUsername() {\n      const { username } = this.message.replyMessage\n      return username || ''\n    },\n    firstFile() {\n      return this.message.replyMessage.files ? this.message.replyMessage.files[0] : {}\n    },\n    containsFile() {\n      return this.message.replyMessage.files\n    },\n\n    isImage() {\n      return isImageFile(this.firstFile)\n    },\n    isVideo() {\n      return isVideoFile(this.firstFile)\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./MessageReply.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./MessageReply.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./MessageReply.vue?vue&type=template&id=23aa7a7a\"\nimport script from \"./MessageReply.vue?vue&type=script&lang=js\"\nexport * from \"./MessageReply.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"vac-message-files-container\",class:{ 'with-reply': _vm.message.replyMessage }},[_c('div',{staticClass:\"clearfix\"},[(_vm.imageFiles.length === 1)?_c('div',{staticClass:\"hwa-single-image\"},[_c('message-file',{attrs:{\"file\":_vm.imageFiles[0],\"current-user-id\":_vm.currentUserId,\"message\":_vm.message,\"index\":0},on:{\"open-file\":function($event){return _vm.$emit('open-file', $event)}},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)})],1):(_vm.imageFiles.length <= 3)?_c('div',_vm._l((_vm.imageFiles),function(file,idx){return _c('div',{key:idx + 'iv',staticClass:\"hwa-single-image\"},[_c('message-file',{attrs:{\"file\":file,\"current-user-id\":_vm.currentUserId,\"message\":_vm.message,\"index\":idx},on:{\"open-file\":function($event){return _vm.$emit('open-file', $event)}},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)})],1)}),0):(_vm.imageFiles.length === 4)?_c('div',_vm._l((_vm.imageFiles),function(file,idx){return _c('div',{key:idx + 'iv',staticClass:\"hwa-multiple-images\"},[_c('message-file',{attrs:{\"file\":file,\"current-user-id\":_vm.currentUserId,\"message\":_vm.message,\"index\":idx},on:{\"open-file\":function($event){return _vm.$emit('open-file', $event)}},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)})],1)}),0):_c('div',[_vm._l((_vm.imageFiles.slice(0, 3)),function(file,idx){return _c('div',{key:idx + 'iv',staticClass:\"hwa-multiple-images\"},[_c('message-file',{attrs:{\"file\":file,\"current-user-id\":_vm.currentUserId,\"message\":_vm.message,\"index\":idx},on:{\"open-file\":function($event){return _vm.$emit('open-file', $event)}},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)})],1)}),_c('div',{staticClass:\"hwa-multiple-images more-images\",class:{ 'more-images-loading': _vm.imageFiles[3].loading },on:{\"click\":function($event){$event.preventDefault();return _vm.handleCarousel(_vm.imageFiles, _vm.imageFiles[3])}}},[_c('message-file',{attrs:{\"file\":_vm.imageFiles[3],\"current-user-id\":_vm.currentUserId,\"message\":_vm.message,\"index\":3},on:{\"open-file\":function($event){return _vm.$emit('open-file', $event)}},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)}),_c('span',{staticClass:\"images-num\"},[_vm._v(\" +\"+_vm._s(_vm.imageFiles.length - 4)+\" \")])],1)],2)]),(_vm.videoFiles)?_c('div',_vm._l((_vm.videoFiles),function(file,idx){return _c('div',{key:idx + 'iv',staticClass:\"hwa-video-container\"},[_c('message-file',{attrs:{\"file\":file,\"current-user-id\":_vm.currentUserId,\"message\":_vm.message,\"index\":idx},on:{\"open-file\":function($event){return _vm.$emit('open-file', $event)}},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)}),_c('div',{staticClass:\"video-timestamp\"},[_c('span',[_vm._v(_vm._s(_vm.message.timestamp))]),(_vm.isCheckmarkVisible)?_c('span',[_vm._t(\"checkmark-icon\",function(){return [_c('svg-icon',{staticClass:\"vac-icon-check\",attrs:{\"name\":_vm.message.distributed ? 'double-checkmark' : 'checkmark',\"param\":_vm.message.seen ? 'seen' : ''}})]},null,{ message: _vm.message })],2):_vm._e()])],1)}),0):_vm._e(),_vm._l((_vm.otherFiles),function(file,idx){return _c('div',{key:idx + 'a',staticClass:\"position-relative\"},[_c('upload-state',{attrs:{\"show\":file.loading || file.error,\"loading\":false,\"error\":file.error}}),_c('div',{staticClass:\"vac-file-container\",on:{\"click\":function($event){$event.stopPropagation();return _vm.openFile(file, 'download')}}},[_c('div',{staticClass:\"hwa-file-display\"},[(file.extension === 'pdf')?_c('embed',{class:{ 'vac-blur': file.loading || file.error },attrs:{\"src\":`${file.loading ? file.localUrl : file.url}`,\"type\":\"application/pdf\"}}):_c('img',{staticClass:\"file-icon\",class:{ 'vac-blur': file.loading || file.error },attrs:{\"src\":file.extension === 'pdf' ? _vm.pdfIcon : _vm.docIcon,\"alt\":\"Pdf Icon\"}})]),_c('div',{staticClass:\"hwa-file-info\"},[_c('img',{staticClass:\"file-icon\",attrs:{\"src\":file.extension === 'pdf' ? _vm.pdfIcon : _vm.docIcon,\"alt\":\"Pdf Icon\"}}),_c('div',{staticClass:\"vac-text-ellipsis\"},[_vm._v(\" \"+_vm._s(decodeURIComponent(file.extension ? `${file.name}.${file.extension}` : file.name))+\" \")]),_c('img',{staticClass:\"download-icon\",attrs:{\"src\":_vm.message.fromMe === 1 ? _vm.downloadIcon : _vm.downloadIconDark,\"alt\":\"Download Icon\"}})]),_c('div',{staticClass:\"hwa-file-meta\"},[_c('div',{staticClass:\"file-info\"}),_c('div',{staticClass:\"mr-4\"},[_c('span',[_vm._v(_vm._s(_vm.message.timestamp))]),(_vm.isCheckmarkVisible)?_c('span',[_vm._t(\"checkmark-icon\",function(){return [_c('svg-icon',{staticClass:\"vac-icon-check\",attrs:{\"name\":_vm.message.distributed ? 'double-checkmark' : 'checkmark',\"param\":_vm.message.seen ? 'seen' : ''}})]},null,{ message: _vm.message })],2):_vm._e()])])])],1)}),_c('format-message',{attrs:{\"content\":_vm.message.content,\"users\":_vm.roomUsers,\"text-formatting\":_vm.textFormatting,\"link-options\":_vm.linkOptions,\"msg-search-query\":_vm.msgSearchQuery},scopedSlots:_vm._u([_vm._l((_vm.$scopedSlots),function(i,name){return {key:name,fn:function(data){return [_vm._t(name,null,null,data)]}}})],null,true)})],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"vac-message-file-container\"},[(_vm.isImage)?_c('div',{ref:'imageRef' + _vm.index,staticClass:\"vac-message-image-container\",on:{\"mouseover\":function($event){_vm.imageHover = true},\"mouseleave\":function($event){_vm.imageHover = false},\"click\":function($event){$event.stopPropagation();return _vm.openFile('preview')}}},[_c('upload-state',{attrs:{\"show\":_vm.inUploadState,\"loading\":false,\"error\":_vm.file.error}}),_c('div',{staticClass:\"vac-message-image\",class:{\n        'vac-loading': _vm.inUploadState && _vm.message.fromMe === 1,\n        'mb-8': _vm.message.content\n      },style:({\n        'background-image': `url('${_vm.inUploadState ? _vm.file.localUrl || _vm.file.url : _vm.file.url}')`,\n        'max-height': `${_vm.imageResponsive.maxHeight}px`\n      })},[_c('transition',{attrs:{\"name\":\"vac-fade-image\"}},[(!_vm.inUploadState)?_c('div',{staticClass:\"vac-image-buttons\"},[_c('div',{staticClass:\"hwa-text-timestamp\"},[_c('span',{staticClass:\"d-inline-block\"},[_vm._v(_vm._s(_vm.file.timestamp))]),(_vm.isCheckmarkVisible)?_c('span',[_vm._t(\"checkmark-icon\",function(){return [_c('svg-icon',{staticClass:\"vac-icon-check\",attrs:{\"name\":_vm.file.distributed ? 'double-checkmark' : 'checkmark',\"param\":_vm.file.seen ? 'seen' : ''}})]},null,{ file: _vm.file })],2):_vm._e()])]):_vm._e()])],1)],1):(_vm.isVideo)?_c('div',{staticClass:\"vac-video-container\",class:{\n      'mb-2': _vm.isAudio\n    }},[_c('upload-state',{attrs:{\"show\":_vm.inUploadState,\"loading\":false,\"error\":_vm.file.error}}),_c('div',{class:{\n        'vac-loading': _vm.inUploadState && _vm.message.fromMe === 1\n      }},[_c('video',{attrs:{\"width\":\"100%\",\"height\":\"100%\",\"controls\":\"\"}},[_c('source',{attrs:{\"src\":_vm.inUploadState ? _vm.file.localUrl : _vm.file.url}})])])],1):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return (_vm.show)?_c('div',{staticClass:\"upload-state\"},[(_vm.loading)?_c('div',{staticClass:\"upload-state-inner ball-pulse\"},[_c('div'),_c('div'),_c('div')]):_vm._e(),(_vm.error)?_c('div',[_c('img',{attrs:{\"src\":_vm.errorIcon,\"alt\":\"Error Icon\"}})]):_vm._e()]):_vm._e()\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div v-if=\"show\" class=\"upload-state\">\n    <div v-if=\"loading\" class=\"upload-state-inner ball-pulse\">\n      <div />\n      <div />\n      <div />\n    </div>\n    <div v-if=\"error\">\n      <img :src=\"errorIcon\" alt=\"Error Icon\" />\n    </div>\n  </div>\n</template>\n\n<script>\nimport ErrorIcon from '../../../assets/icons/error_icon.svg';\nexport default {\n  name: 'UploadState',\n\n  props: {\n    show: { type: Boolean, default: false },\n    loading: { type: Boolean, default: false },\n    error: { type: Boolean, default: true }\n  },\n\n  data() {\n    return {\n      errorIcon: ErrorIcon\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./UploadState.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./UploadState.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./UploadState.vue?vue&type=template&id=39e37a68\"\nimport script from \"./UploadState.vue?vue&type=script&lang=js\"\nexport * from \"./UploadState.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div class=\"vac-message-file-container\">\n    <div\n      v-if=\"isImage\"\n      :ref=\"'imageRef' + index\"\n      class=\"vac-message-image-container\"\n      @mouseover=\"imageHover = true\"\n      @mouseleave=\"imageHover = false\"\n      @click.stop=\"openFile('preview')\"\n    >\n      <upload-state :show=\"inUploadState\" :loading=\"false\" :error=\"file.error\" />\n      <div\n        class=\"vac-message-image\"\n        :class=\"{\n          'vac-loading': inUploadState && message.fromMe === 1,\n          'mb-8': message.content\n        }\"\n        :style=\"{\n          'background-image': `url('${inUploadState ? file.localUrl || file.url : file.url}')`,\n          'max-height': `${imageResponsive.maxHeight}px`\n        }\"\n      >\n        <transition name=\"vac-fade-image\">\n          <div v-if=\"!inUploadState\" class=\"vac-image-buttons\">\n            <div class=\"hwa-text-timestamp\">\n              <span class=\"d-inline-block\">{{ file.timestamp }}</span>\n              <span v-if=\"isCheckmarkVisible\">\n                <slot name=\"checkmark-icon\" v-bind=\"{ file }\">\n                  <svg-icon\n                    :name=\"file.distributed ? 'double-checkmark' : 'checkmark'\"\n                    :param=\"file.seen ? 'seen' : ''\"\n                    class=\"vac-icon-check\"\n                  />\n                </slot>\n              </span>\n            </div>\n          </div>\n        </transition>\n      </div>\n    </div>\n\n    <div\n      v-else-if=\"isVideo\"\n      class=\"vac-video-container\"\n      :class=\"{\n        'mb-2': isAudio\n      }\"\n    >\n      <upload-state :show=\"inUploadState\" :loading=\"false\" :error=\"file.error\" />\n      <div\n        :class=\"{\n          'vac-loading': inUploadState && message.fromMe === 1\n        }\"\n      >\n        <video width=\"100%\" height=\"100%\" controls>\n          <source :src=\"inUploadState ? file.localUrl : file.url\" />\n        </video>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport UploadState from '../../../components/UploadState/UploadState'\nimport SvgIcon from '../../../components/SvgIcon/SvgIcon'\n\nconst { isImageFile, isVideoFile, isAudioFile } = require('../../../utils/media-file')\n\nexport default {\n  name: 'MessageFile',\n  components: { SvgIcon, UploadState },\n\n  props: {\n    message: { type: Object, required: true },\n    file: { type: Object, required: true },\n    index: { type: Number, required: true }\n  },\n\n  emits: ['open-file'],\n\n  data() {\n    return {\n      imageResponsive: '',\n      imageLoading: false,\n      imageHover: false\n    }\n  },\n\n  computed: {\n    isAudio() {\n      return isAudioFile(this.file)\n    },\n\n    inUploadState() {\n      return this.file.loading || this.file.error\n    },\n    isImage() {\n      return isImageFile(this.file)\n    },\n    isVideo() {\n      return isVideoFile(this.file)\n    },\n    isCheckmarkVisible() {\n      return (\n        this.message.fromMe === 1 &&\n        !this.message.deleted &&\n        (this.message.saved || this.message.distributed || this.message.seen)\n      )\n    }\n  },\n\n  watch: {\n    file: {\n      immediate: true,\n      handler() {\n        this.checkImgLoad()\n      }\n    }\n  },\n\n  mounted() {\n    const ref = this.$refs['imageRef' + this.index]\n\n    if (ref) {\n      this.imageResponsive = {\n        maxHeight: ref.clientWidth - 18,\n        loaderTop: ref.clientHeight / 2 - 9\n      }\n    }\n  },\n\n  methods: {\n    checkImgLoad() {\n      if (!isImageFile(this.file)) return\n      this.imageLoading = true\n      const image = new Image()\n      image.src = this.file.url\n      image.addEventListener('load', () => (this.imageLoading = false))\n    },\n    openFile(action) {\n      if (!this.inUploadState) {\n        this.$emit('open-file', { file: this.file, action })\n      }\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./MessageFile.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./MessageFile.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./MessageFile.vue?vue&type=template&id=57842db7\"\nimport script from \"./MessageFile.vue?vue&type=script&lang=js\"\nexport * from \"./MessageFile.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div class=\"vac-message-files-container\" :class=\"{ 'with-reply': message.replyMessage }\">\n    <div class=\"clearfix\">\n      <!-- Image file start -->\n      <div v-if=\"imageFiles.length === 1\" class=\"hwa-single-image\">\n        <message-file\n          :file=\"imageFiles[0]\"\n          :current-user-id=\"currentUserId\"\n          :message=\"message\"\n          :index=\"0\"\n          @open-file=\"$emit('open-file', $event)\"\n        >\n          <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n            <slot :name=\"name\" v-bind=\"data\" />\n          </template>\n        </message-file>\n      </div>\n\n      <div v-else-if=\"imageFiles.length <= 3\">\n        <div v-for=\"(file, idx) in imageFiles\" :key=\"idx + 'iv'\" class=\"hwa-single-image\">\n          <message-file\n            :file=\"file\"\n            :current-user-id=\"currentUserId\"\n            :message=\"message\"\n            :index=\"idx\"\n            @open-file=\"$emit('open-file', $event)\"\n          >\n            <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n              <slot :name=\"name\" v-bind=\"data\" />\n            </template>\n          </message-file>\n        </div>\n      </div>\n\n      <div v-else-if=\"imageFiles.length === 4\">\n        <div v-for=\"(file, idx) in imageFiles\" :key=\"idx + 'iv'\" class=\"hwa-multiple-images\">\n          <message-file\n            :file=\"file\"\n            :current-user-id=\"currentUserId\"\n            :message=\"message\"\n            :index=\"idx\"\n            @open-file=\"$emit('open-file', $event)\"\n          >\n            <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n              <slot :name=\"name\" v-bind=\"data\" />\n            </template>\n          </message-file>\n        </div>\n      </div>\n\n      <div v-else>\n        <div v-for=\"(file, idx) in imageFiles.slice(0, 3)\" :key=\"idx + 'iv'\" class=\"hwa-multiple-images\">\n          <message-file\n            :file=\"file\"\n            :current-user-id=\"currentUserId\"\n            :message=\"message\"\n            :index=\"idx\"\n            @open-file=\"$emit('open-file', $event)\"\n          >\n            <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n              <slot :name=\"name\" v-bind=\"data\" />\n            </template>\n          </message-file>\n        </div>\n        <div\n          class=\"hwa-multiple-images more-images\"\n          :class=\"{ 'more-images-loading': imageFiles[3].loading }\"\n          @click.prevent=\"handleCarousel(imageFiles, imageFiles[3])\"\n        >\n          <message-file\n            :file=\"imageFiles[3]\"\n            :current-user-id=\"currentUserId\"\n            :message=\"message\"\n            :index=\"3\"\n            @open-file=\"$emit('open-file', $event)\"\n          >\n            <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n              <slot :name=\"name\" v-bind=\"data\" />\n            </template>\n          </message-file>\n          <span class=\"images-num\"> +{{ imageFiles.length - 4 }} </span>\n        </div>\n      </div>\n    </div>\n    <!-- Image file end -->\n\n    <!-- Video File start -->\n    <div v-if=\"videoFiles\">\n      <div v-for=\"(file, idx) in videoFiles\" :key=\"idx + 'iv'\" class=\"hwa-video-container\">\n        <message-file\n          :file=\"file\"\n          :current-user-id=\"currentUserId\"\n          :message=\"message\"\n          :index=\"idx\"\n          @open-file=\"$emit('open-file', $event)\"\n        >\n          <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n            <slot :name=\"name\" v-bind=\"data\" />\n          </template>\n        </message-file>\n        <div class=\"video-timestamp\">\n          <span>{{ message.timestamp }}</span>\n          <span v-if=\"isCheckmarkVisible\">\n            <slot name=\"checkmark-icon\" v-bind=\"{ message }\">\n              <svg-icon\n                :name=\"message.distributed ? 'double-checkmark' : 'checkmark'\"\n                :param=\"message.seen ? 'seen' : ''\"\n                class=\"vac-icon-check\"\n              />\n            </slot>\n          </span>\n        </div>\n      </div>\n    </div>\n    <!-- Video File End -->\n\n    <div v-for=\"(file, idx) in otherFiles\" :key=\"idx + 'a'\" class=\"position-relative\">\n      <upload-state :show=\"file.loading || file.error\" :loading=\"false\" :error=\"file.error\" />\n      <div class=\"vac-file-container\" @click.stop=\"openFile(file, 'download')\">\n        <div class=\"hwa-file-display\">\n          <embed\n            v-if=\"file.extension === 'pdf'\"\n            :class=\"{ 'vac-blur': file.loading || file.error }\"\n            :src=\"`${file.loading ? file.localUrl : file.url}`\"\n            type=\"application/pdf\"\n          />\n          <img\n            v-else\n            :src=\"file.extension === 'pdf' ? pdfIcon : docIcon\"\n            alt=\"Pdf Icon\"\n            class=\"file-icon\"\n            :class=\"{ 'vac-blur': file.loading || file.error }\"\n          />\n        </div>\n        <div class=\"hwa-file-info\">\n          <img :src=\"file.extension === 'pdf' ? pdfIcon : docIcon\" alt=\"Pdf Icon\" class=\"file-icon\" />\n          <div class=\"vac-text-ellipsis\">\n           {{ decodeURIComponent(file.extension ? `${file.name}.${file.extension}` : file.name) }}\n          </div>\n          <img\n            :src=\"message.fromMe === 1 ? downloadIcon : downloadIconDark\"\n            alt=\"Download Icon\"\n            class=\"download-icon\"\n          />\n        </div>\n\n        <div class=\"hwa-file-meta\">\n          <div class=\"file-info\" />\n          <div class=\"mr-4\">\n            <span>{{ message.timestamp }}</span>\n            <span v-if=\"isCheckmarkVisible\">\n              <slot name=\"checkmark-icon\" v-bind=\"{ message }\">\n                <svg-icon\n                  :name=\"message.distributed ? 'double-checkmark' : 'checkmark'\"\n                  :param=\"message.seen ? 'seen' : ''\"\n                  class=\"vac-icon-check\"\n                />\n              </slot>\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <format-message\n      :content=\"message.content\"\n      :users=\"roomUsers\"\n      :text-formatting=\"textFormatting\"\n      :link-options=\"linkOptions\"\n      :msg-search-query=\"msgSearchQuery\"\n    >\n      <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n        <slot :name=\"name\" v-bind=\"data\" />\n      </template>\n    </format-message>\n  </div>\n</template>\n\n<script>\nimport SvgIcon from '../../../components/SvgIcon/SvgIcon'\nimport PdfIcon from '../../../../assets/icons/pdf_icon.png'\nimport DocIcon from '../../../../assets/icons/doc_icon.png'\nimport DownloadIcon from '../../../../assets/icons/download_icon.png'\nimport DownloadIconDark from '../../../../assets/icons/download_icon-dark.png'\nimport FormatMessage from '../../../components/FormatMessage/FormatMessage'\nimport MessageFile from '../MessageFile/MessageFile'\nimport UploadState from '../../../components/UploadState/UploadState'\n\nconst { isImageVideoFile, isVideoFile, isImageFile } = require('../../../utils/media-file')\n\nexport default {\n  name: 'MessageFiles',\n  components: { SvgIcon, FormatMessage, MessageFile, UploadState },\n\n  props: {\n    currentUserId: { type: [String, Number], required: true },\n    message: { type: Object, required: true },\n    roomUsers: { type: Array, required: true },\n    textFormatting: { type: Boolean, required: true },\n    linkOptions: { type: Object, required: true },\n    msgSearchQuery: { type: String, required: true }\n  },\n\n  emits: ['open-file', 'carousel-handler'],\n\n  data() {\n    return {\n      pdfIcon: PdfIcon,\n      docIcon: DocIcon,\n      downloadIcon: DownloadIcon,\n      downloadIconDark: DownloadIconDark\n    }\n  },\n\n  computed: {\n    imageFiles() {\n      return this.message.files.filter(file => isImageFile(file))\n    },\n    videoFiles() {\n      return this.message.files.filter(file => isVideoFile(file))\n    },\n    otherFiles() {\n      return this.message.files.filter(file => !isImageVideoFile(file))\n    },\n    isCheckmarkVisible() {\n      return (\n        this.message.fromMe === 1 &&\n        !this.message.deleted &&\n        (this.message.saved || this.message.distributed || this.message.seen)\n      )\n    }\n  },\n\n  methods: {\n    openFile(file, action) {\n      if (!file.loading && !file.error) {\n        this.$emit('open-file', { file, action })\n      }\n    },\n    handleCarousel(imageFiles, file) {\n      if (!file.loading && !file.error) {\n        this.$emit('carousel-handler', imageFiles)\n      }\n    }\n  },\n\n\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./MessageFiles.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./MessageFiles.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./MessageFiles.vue?vue&type=template&id=ae64ec6a\"\nimport script from \"./MessageFiles.vue?vue&type=script&lang=js\"\nexport * from \"./MessageFiles.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"vac-message-actions-wrapper\"},[(!_vm.message.error)?_c('div',{staticClass:\"vac-options-container\",class:{ 'files-variant': _vm.message.files },style:({\n        display: _vm.hoverAudioProgress ? 'none' : 'initial',\n        width: _vm.filteredMessageActions.length && _vm.showReactionEmojis ? '45px' : '45px'\n      })},[_c('transition-group',{attrs:{\"name\":\"vac-slide-left\",\"tag\":\"span\"}},[(_vm.isMessageActions || _vm.isMessageReactions)?_c('div',{key:\"1\",staticClass:\"vac-blur-container\",class:{\n            'vac-options-me': _vm.message.fromMe === 1\n          }}):_vm._e(),(_vm.isMessageActions)?_c('div',{key:\"2\",ref:\"actionIcon\",staticClass:\"vac-svg-button vac-message-options\",on:{\"click\":_vm.openOptions}},[_vm._t(\"dropdown-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"dropdown\",\"param\":\"message\"}})]})],2):_vm._e()])],1):_vm._e(),(_vm.filteredMessageActions.length)?_c('transition',{attrs:{\"name\":_vm.message.fromMe === 1 ? 'vac-slide-left' : 'vac-slide-right'}},[(_vm.optionsOpened)?_c('div',{directives:[{name:\"click-outside\",rawName:\"v-click-outside\",value:(_vm.closeOptions),expression:\"closeOptions\"}],ref:\"menuOptions\",staticClass:\"vac-menu-options\",class:{\n          'vac-menu-left': !_vm.message.fromMe\n        },style:({ top: `${_vm.menuOptionsTop}px` })},[_c('div',{staticClass:\"vac-menu-list\"},_vm._l((_vm.filteredMessageActions),function(action){return _c('div',{key:action.name},[_c('div',{staticClass:\"vac-menu-item\",on:{\"click\":function($event){return _vm.messageActionHandler(action)}}},[_vm._v(\" \"+_vm._s(action.title)+\" \")])])}),0)]):_vm._e()]):_vm._e(),(_vm.message.error)?_c('div',{staticClass:\"message-error-icon\",on:{\"click\":function($event){$event.preventDefault();return _vm.$emit('toggle-error-modal')}}},[_c('img',{attrs:{\"src\":_vm.errorIcon,\"alt\":\"Error Icon\"}})]):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"vac-message-actions-wrapper\">\n    <div\n      v-if=\"!message.error\"\n      class=\"vac-options-container\"\n      :class=\"{ 'files-variant': message.files }\"\n      :style=\"{\n        display: hoverAudioProgress ? 'none' : 'initial',\n        width: filteredMessageActions.length && showReactionEmojis ? '45px' : '45px'\n      }\"\n    >\n      <transition-group name=\"vac-slide-left\" tag=\"span\">\n        <div\n          v-if=\"isMessageActions || isMessageReactions\"\n          key=\"1\"\n          class=\"vac-blur-container\"\n          :class=\"{\n            'vac-options-me': message.fromMe === 1\n          }\"\n        />\n\n        <div\n          v-if=\"isMessageActions\"\n          ref=\"actionIcon\"\n          key=\"2\"\n          class=\"vac-svg-button vac-message-options\"\n          @click=\"openOptions\"\n        >\n          <slot name=\"dropdown-icon\">\n            <svg-icon name=\"dropdown\" param=\"message\" />\n          </slot>\n        </div>\n      </transition-group>\n    </div>\n\n    <transition\n      v-if=\"filteredMessageActions.length\"\n      :name=\"message.fromMe === 1 ? 'vac-slide-left' : 'vac-slide-right'\"\n    >\n      <div\n        v-if=\"optionsOpened\"\n        ref=\"menuOptions\"\n        v-click-outside=\"closeOptions\"\n        class=\"vac-menu-options\"\n        :class=\"{\n          'vac-menu-left': !message.fromMe\n        }\"\n        :style=\"{ top: `${menuOptionsTop}px` }\"\n      >\n        <div class=\"vac-menu-list\">\n          <div v-for=\"action in filteredMessageActions\" :key=\"action.name\">\n            <div class=\"vac-menu-item\" @click=\"messageActionHandler(action)\">\n              {{ action.title }}\n            </div>\n          </div>\n        </div>\n      </div>\n    </transition>\n\n    <!-- Forward Icon -->\n    <!-- <div\n\t\t\tv-if=\"message.files && !message.deleted && !message.error\"\n\t\t\tclass=\"message-4wd-icon\"\n\t\t\t:class=\"{ 'from-me': message.fromMe }\"\n\t\t\***************=\"$emit('open-forward-modal', message)\"\n\t\t>\n\t\t\t<svg\n\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\tviewBox=\"3.886999999999998 6.484999999999999 16.666 12.352000000000004\"\n\t\t\t>\n\t\t\t\t<path\n\t\t\t\t\td=\"M14.248 6.973a.688.688 0 0 1 1.174-.488l5.131 5.136a.687.687 0 0 1 0 .973l-5.131 5.136a.688.688 0 0 1-1.174-.488v-2.319c-4.326 0-7.495 1.235-9.85 3.914-.209.237-.596.036-.511-.268 1.215-4.391 4.181-8.492 10.361-9.376v-2.22z\"\n\t\t\t\t\tfill=\"#fff\"\n\t\t\t\t/>\n\t\t\t</svg>\n\t\t</div> -->\n    <!-- Error Icon -->\n\n    <div v-if=\"message.error\" class=\"message-error-icon\" @click.prevent=\"$emit('toggle-error-modal')\">\n      <img :src=\"errorIcon\" alt=\"Error Icon\" />\n    </div>\n  </div>\n</template>\n\n<script>\nimport vClickOutside from 'v-click-outside'\n\nimport SvgIcon from '../../../components/SvgIcon/SvgIcon'\nimport ErrorIcon from '../../../../assets/icons/error_icon.svg'\n\nexport default {\n  name: 'MessageActions',\n  components: {\n    SvgIcon\n  },\n\n  directives: {\n    clickOutside: vClickOutside.directive\n  },\n\n  props: {\n    message: { type: Object, required: true },\n    messageActions: { type: Array, required: true },\n    roomFooterRef: { type: HTMLDivElement, default: null },\n    showReactionEmojis: { type: Boolean, required: true },\n    hideOptions: { type: Boolean, required: true },\n    messageHover: { type: Boolean, required: true },\n    hoverMessageId: { type: [String, Number], default: null },\n    hoverAudioProgress: { type: Boolean, required: true }\n  },\n\n  emits: [\n    'update-emoji-opened',\n    'update-options-opened',\n    'update-message-hover',\n    'hide-options',\n    'message-action-handler',\n    'send-message-reaction',\n    'open-forward-modal',\n    'toggle-message-forward'\n  ],\n\n  data() {\n    return {\n      menuOptionsTop: 0,\n      optionsOpened: false,\n      optionsClosing: false,\n      emojiOpened: false,\n      errorIcon: ErrorIcon\n    }\n  },\n\n  computed: {\n    isMessageActions() {\n      return (\n        this.filteredMessageActions.length &&\n        this.messageHover &&\n        !this.message.deleted &&\n        !this.message.disableActions &&\n        !this.hoverAudioProgress\n      )\n    },\n    isMessageReactions() {\n      return (\n        this.showReactionEmojis &&\n        this.messageHover &&\n        !this.message.deleted &&\n        !this.message.disableReactions &&\n        !this.hoverAudioProgress\n      )\n    },\n    filteredMessageActions() {\n      return this.message.fromMe === 1 ? this.messageActions : this.messageActions.filter(message => !message.onlyMe)\n    }\n  },\n\n  watch: {\n    emojiOpened(val) {\n      this.$emit('update-emoji-opened', val)\n      if (val) this.optionsOpened = false\n    },\n    hideOptions(val) {\n      if (val) {\n        this.closeEmoji()\n        this.closeOptions()\n      }\n    },\n    optionsOpened(val) {\n      this.$emit('update-options-opened', val)\n    }\n  },\n\n  methods: {\n    openOptions() {\n      if (this.optionsClosing) return\n\n      this.optionsOpened = !this.optionsOpened\n      if (!this.optionsOpened) return\n\n      this.$emit('hide-options', false)\n\n      setTimeout(() => {\n        if (!this.roomFooterRef || !this.$refs.menuOptions || !this.$refs.actionIcon) {\n          return\n        }\n\n        const menuOptionsTop = this.$refs.menuOptions.getBoundingClientRect().height\n\n        const actionIconTop = this.$refs.actionIcon.getBoundingClientRect().top\n        const roomFooterTop = this.roomFooterRef.getBoundingClientRect().top\n\n        const optionsTopPosition = roomFooterTop - actionIconTop > menuOptionsTop + 50\n\n        if (optionsTopPosition) this.menuOptionsTop = 30\n        else this.menuOptionsTop = -menuOptionsTop\n      })\n    },\n    closeOptions() {\n      this.optionsOpened = false\n      this.optionsClosing = true\n      this.updateMessageHover()\n      setTimeout(() => (this.optionsClosing = false), 100)\n    },\n\n    closeEmoji() {\n      this.emojiOpened = false\n      this.updateMessageHover()\n    },\n    updateMessageHover() {\n      if (this.hoverMessageId !== this.message._id) {\n        this.$emit('update-message-hover', false)\n      }\n    },\n    messageActionHandler(action) {\n      this.closeOptions()\n\n      this.$emit('message-action-handler', action)\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./MessageActions.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./MessageActions.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./MessageActions.vue?vue&type=template&id=f3050014\"\nimport script from \"./MessageActions.vue?vue&type=script&lang=js\"\nexport * from \"./MessageActions.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return (!_vm.message.deleted)?_c('transition-group',{attrs:{\"name\":\"vac-slide-left\",\"tag\":\"span\"}},_vm._l((_vm.message.reactions),function(reaction,key){return _c('button',{directives:[{name:\"show\",rawName:\"v-show\",value:(reaction.length),expression:\"reaction.length\"}],key:key + 0,staticClass:\"vac-button-reaction\",class:{\n      'vac-reaction-me': reaction.indexOf(_vm.currentUserId) !== -1\n    },style:({\n      float: _vm.message.fromMe === 1 ? 'right' : 'left'\n    }),on:{\"click\":function($event){return _vm.sendMessageReaction({ unicode: key }, reaction)}}},[_vm._v(\" \"+_vm._s(key)),_c('span',[_vm._v(_vm._s(reaction.length))])])}),0):_vm._e()\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <transition-group v-if=\"!message.deleted\" name=\"vac-slide-left\" tag=\"span\">\n    <button\n      v-for=\"(reaction, key) in message.reactions\"\n      v-show=\"reaction.length\"\n      :key=\"key + 0\"\n      class=\"vac-button-reaction\"\n      :class=\"{\n        'vac-reaction-me': reaction.indexOf(currentUserId) !== -1\n      }\"\n      :style=\"{\n        float: message.fromMe === 1 ? 'right' : 'left'\n      }\"\n      @click=\"sendMessageReaction({ unicode: key }, reaction)\"\n    >\n      {{ key }}<span>{{ reaction.length }}</span>\n    </button>\n  </transition-group>\n</template>\n\n<script>\nexport default {\n  name: 'MessageReactions',\n\n  props: {\n    currentUserId: { type: [String, Number], required: true },\n    message: { type: Object, required: true }\n  },\n\n  emits: ['send-message-reaction'],\n\n  methods: {\n    sendMessageReaction(emoji, reaction) {\n      this.$emit('send-message-reaction', { emoji, reaction })\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./MessageReactions.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./MessageReactions.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./MessageReactions.vue?vue&type=template&id=2126c143\"\nimport script from \"./MessageReactions.vue?vue&type=script&lang=js\"\nexport * from \"./MessageReactions.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('div',{staticClass:\"vac-audio-player\"},[_c('div',{staticClass:\"vac-svg-button\",on:{\"click\":_vm.playback}},[(_vm.isPlaying)?_vm._t(\"audio-pause-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"audio-pause\"}})]}):_vm._t(\"audio-play-icon\",function(){return [_c('svg-icon',{attrs:{\"name\":\"audio-play\"}})]})],2),_c('audio-control',{attrs:{\"percentage\":_vm.progress},on:{\"change-linehead\":_vm.onUpdateProgress,\"hover-audio-progress\":function($event){return _vm.$emit('hover-audio-progress', $event)}}}),_c('audio',{attrs:{\"id\":_vm.playerUniqId,\"src\":_vm.audioSource}})],1)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{ref:\"progress\",staticClass:\"vac-player-bar\",on:{\"mousedown\":_vm.onMouseDown,\"mouseover\":function($event){return _vm.$emit('hover-audio-progress', true)},\"mouseout\":function($event){return _vm.$emit('hover-audio-progress', false)}}},[_c('div',{staticClass:\"vac-player-progress\"},[_c('div',{staticClass:\"vac-line-container\"},[_c('div',{staticClass:\"vac-line-progress\",style:({ width: `${_vm.percentage}%` })}),_c('div',{staticClass:\"vac-line-dot\",class:{ 'vac-line-dot__active': _vm.isMouseDown },style:({ left: `${_vm.percentage}%` })})])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div\n    ref=\"progress\"\n    class=\"vac-player-bar\"\n    @mousedown=\"onMouseDown\"\n    @mouseover=\"$emit('hover-audio-progress', true)\"\n    @mouseout=\"$emit('hover-audio-progress', false)\"\n  >\n    <div class=\"vac-player-progress\">\n      <div class=\"vac-line-container\">\n        <div class=\"vac-line-progress\" :style=\"{ width: `${percentage}%` }\" />\n        <div\n          class=\"vac-line-dot\"\n          :class=\"{ 'vac-line-dot__active': isMouseDown }\"\n          :style=\"{ left: `${percentage}%` }\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  props: {\n    percentage: { type: Number, default: 0 }\n  },\n\n  emits: ['hover-audio-progress', 'change-linehead'],\n\n  data() {\n    return {\n      isMouseDown: false\n    }\n  },\n\n  methods: {\n    onMouseDown(ev) {\n      this.isMouseDown = true\n      const seekPos = this.calculateLineHeadPosition(ev, this.$refs.progress)\n      this.$emit('change-linehead', seekPos)\n      document.addEventListener('mousemove', this.onMouseMove)\n      document.addEventListener('mouseup', this.onMouseUp)\n    },\n    onMouseUp(ev) {\n      this.isMouseDown = false\n      document.removeEventListener('mouseup', this.onMouseUp)\n      document.removeEventListener('mousemove', this.onMouseMove)\n      const seekPos = this.calculateLineHeadPosition(ev, this.$refs.progress)\n      this.$emit('change-linehead', seekPos)\n    },\n    onMouseMove(ev) {\n      const seekPos = this.calculateLineHeadPosition(ev, this.$refs.progress)\n      this.$emit('change-linehead', seekPos)\n    },\n    calculateLineHeadPosition(ev, element) {\n      const progressWidth = element.getBoundingClientRect().width\n      const leftPosition = element.getBoundingClientRect().left\n      let pos = (ev.clientX - leftPosition) / progressWidth\n\n      pos = pos < 0 ? 0 : pos\n      pos = pos > 1 ? 1 : pos\n\n      return pos\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./AudioControl.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./AudioControl.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./AudioControl.vue?vue&type=template&id=119ba131\"\nimport script from \"./AudioControl.vue?vue&type=script&lang=js\"\nexport * from \"./AudioControl.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div>\n    <div class=\"vac-audio-player\">\n      <div class=\"vac-svg-button\" @click=\"playback\">\n        <slot v-if=\"isPlaying\" name=\"audio-pause-icon\">\n          <svg-icon name=\"audio-pause\" />\n        </slot>\n        <slot v-else name=\"audio-play-icon\">\n          <svg-icon name=\"audio-play\" />\n        </slot>\n      </div>\n      <audio-control\n        :percentage=\"progress\"\n        @change-linehead=\"onUpdateProgress\"\n        @hover-audio-progress=\"$emit('hover-audio-progress', $event)\"\n      />\n\n      <audio :id=\"playerUniqId\" :src=\"audioSource\" />\n    </div>\n  </div>\n</template>\n\n<script>\nimport SvgIcon from '../../../components/SvgIcon/SvgIcon'\n\nimport AudioControl from '../AudioControl/AudioControl'\n\nexport default {\n  name: 'AudioPlayer',\n  components: {\n    SvgIcon,\n    AudioControl\n  },\n\n  props: {\n    src: { type: String, default: null }\n  },\n\n  emits: ['hover-audio-progress', 'update-progress-time'],\n\n  data() {\n    return {\n      isPlaying: false,\n      duration: this.convertTimeMMSS(0),\n      playedTime: this.convertTimeMMSS(0),\n      progress: 0\n    }\n  },\n\n  computed: {\n    playerUniqId() {\n      return `audio-player${this._uid}`\n    },\n    audioSource() {\n      if (this.src) return this.src\n      this.resetProgress()\n      return null\n    }\n  },\n\n  mounted() {\n    this.player = document.getElementById(this.playerUniqId)\n\n    this.player.addEventListener('ended', () => {\n      this.isPlaying = false\n    })\n\n    this.player.addEventListener('loadeddata', () => {\n      this.resetProgress()\n      this.duration = this.convertTimeMMSS(this.player.duration)\n      this.updateProgressTime()\n    })\n\n    this.player.addEventListener('timeupdate', this.onTimeUpdate)\n  },\n\n  methods: {\n    convertTimeMMSS(seconds) {\n      return new Date(seconds * 1000).toISOString().substr(14, 5)\n    },\n    playback() {\n      if (!this.audioSource) return\n\n      if (this.isPlaying) this.player.pause()\n      else setTimeout(() => this.player.play())\n\n      this.isPlaying = !this.isPlaying\n    },\n    resetProgress() {\n      if (this.isPlaying) this.player.pause()\n\n      this.duration = this.convertTimeMMSS(0)\n      this.playedTime = this.convertTimeMMSS(0)\n      this.progress = 0\n      this.isPlaying = false\n      this.updateProgressTime()\n    },\n    onTimeUpdate() {\n      this.playedTime = this.convertTimeMMSS(this.player.currentTime)\n      this.progress = (this.player.currentTime / this.player.duration) * 100\n      this.updateProgressTime()\n    },\n    onUpdateProgress(pos) {\n      if (pos) this.player.currentTime = pos * this.player.duration\n    },\n    updateProgressTime() {\n      this.$emit('update-progress-time', this.progress > 1 ? this.playedTime : this.duration)\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./AudioPlayer.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./AudioPlayer.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./AudioPlayer.vue?vue&type=template&id=395d8c86\"\nimport script from \"./AudioPlayer.vue?vue&type=script&lang=js\"\nexport * from \"./AudioPlayer.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div :id=\"message._id\" :ref=\"message._id\" class=\"vac-message-wrapper\">\n    <div v-if=\"showDate\" class=\"vac-card-info vac-card-date\">\n      {{ message.date }}\n    </div>\n    <div v-if=\"unreadCounts[roomId]?.msg_id === message._id\" class=\"vac-line-new\">\n      {{ textMessages.NEW_MESSAGES }}\n    </div>\n\n    <div v-if=\"message.system\" class=\"vac-card-info vac-card-system\">\n      <format-message\n        :content=\"message.content\"\n        :users=\"roomUsers\"\n        :text-formatting=\"textFormatting\"\n        :link-options=\"linkOptions\"\n        :msg-search-query=\"msgSearchQuery\"\n      >\n        <template v-for=\"(i, name) in $scopedSlots\">\n          <slot :name=\"name\" v-bind=\"data\" />\n        </template>\n      </format-message>\n    </div>\n\n    <div v-else class=\"vac-message-box\" :class=\"{ 'vac-offset-current': message.fromMe === 1 }\">\n      <slot name=\"message\" v-bind=\"{ message }\">\n        <div\n          class=\"vac-message-container\"\n          :class=\"{\n            'vac-message-container-offset': messageOffset\n          }\"\n        >\n          <div\n            :id=\"`${message._id}-child`\"\n            class=\"vac-message-card\"\n            :class=\"{\n              'vac-message-highlight': isMessageHover,\n              'vac-message-wait': !message.saved && message.fromMe === 1,\n              'vac-message-current': message.fromMe === 1,\n              'vac-message-deleted': message.deleted,\n              'no-event': message.files ? message.files[0].loading : null\n            }\"\n            @mouseover=\"onHoverMessage\"\n            @mouseleave=\"onLeaveMessage\"\n          >\n            <div\n              v-if=\"isGroup && messageOffset\"\n              class=\"vac-text-username\"\n              :class=\"{\n                'vac-username-reply': !message.deleted && message.replyMessage\n              }\"\n            >\n              <span>{{ message.username }}</span>\n            </div>\n\n            <message-reply\n              v-if=\"!message.deleted && message.replyMessage\"\n              :message=\"message\"\n              :room-users=\"roomUsers\"\n              :text-formatting=\"textFormatting\"\n              :link-options=\"linkOptions\"\n              :is-group=\"isGroup\"\n              @reply-msg-handler=\"$emit('reply-msg-handler', message)\"\n            >\n              <template v-for=\"(i, name) in $scopedSlots\">\n                <slot :name=\"name\" v-bind=\"data\" />\n              </template>\n            </message-reply>\n\n            <div v-if=\"message.deleted\">\n              <slot name=\"deleted-icon\">\n                <svg-icon name=\"deleted\" class=\"vac-icon-deleted\" />\n              </slot>\n              <span>{{ textMessages.MESSAGE_DELETED }}</span>\n            </div>\n\n            <format-message\n              v-else-if=\"!message.files || !message.files.length\"\n              :content=\"message.content\"\n              :users=\"roomUsers\"\n              :text-formatting=\"textFormatting\"\n              :link-options=\"linkOptions\"\n              :msg-search-query=\"msgSearchQuery\"\n            >\n              <template v-for=\"(i, name) in $scopedSlots\">\n                <slot :name=\"name\" v-bind=\"data\" />\n              </template>\n            </format-message>\n\n            <message-files\n              v-else-if=\"!isAudio || message.files.length > 1\"\n              :current-user-id=\"currentUserId\"\n              :message=\"message\"\n              :room-users=\"roomUsers\"\n              :text-formatting=\"textFormatting\"\n              :link-options=\"linkOptions\"\n              :msg-search-query=\"msgSearchQuery\"\n              @open-file=\"openFile\"\n              @carousel-handler=\"carouselHandler\"\n            >\n              <template v-for=\"(i, name) in $scopedSlots\">\n                <slot :name=\"name\" v-bind=\"data\" />\n              </template>\n            </message-files>\n\n            <template v-else>\n              <div :class=\"{ 'vac-loading': message.files[0].loading }\">\n                <audio-player\n                  :src=\"message.files[0].url\"\n                  @update-progress-time=\"progressTime = $event\"\n                  @hover-audio-progress=\"hoverAudioProgress = $event\"\n                >\n                  <template v-for=\"(i, name) in $scopedSlots\">\n                    <slot :name=\"name\" v-bind=\"data\" />\n                  </template>\n                </audio-player>\n\n                <div v-if=\"!message.deleted\" class=\"vac-progress-time\">\n                  {{ progressTime }}\n                </div>\n                <div class=\"vac-text-timestamp\">\n                  <div v-if=\"message.edited && !message.deleted\" class=\"vac-icon-edited\">\n                    <slot name=\"pencil-icon\">\n                      <svg-icon name=\"pencil\" />\n                    </slot>\n                  </div>\n                  <span>{{ message.timestamp }}</span>\n                  <span v-if=\"isCheckmarkVisible\">\n                    <slot name=\"checkmark-icon\" v-bind=\"{ message }\">\n                      <svg-icon\n                        :name=\"\n                          message.distributed === 'wait' && !message.saved\n                            ? 'wait'\n                            : message.distributed\n                            ? 'double-checkmark'\n                            : 'checkmark'\n                        \"\n                        :param=\"message.seen ? 'seen' : ''\"\n                        class=\"vac-icon-check\"\n                      />\n                    </slot>\n                  </span>\n                </div>\n              </div>\n            </template>\n\n            <div v-if=\"showTimeStamp\" class=\"vac-text-timestamp\">\n              <div v-if=\"message.edited && !message.deleted\" class=\"vac-icon-edited\">\n                <slot name=\"pencil-icon\">\n                  <svg-icon name=\"pencil\" />\n                </slot>\n              </div>\n              <span>{{ message.timestamp }}</span>\n              <span v-if=\"isCheckmarkVisible\" v-tooltip=\"message.reason\">\n                <slot name=\"checkmark-icon\" v-bind=\"{ message }\">\n                  <svg-icon\n                    :reason=\"message.reason\"\n                    :name=\"\n                      message.failed\n                        ? 'error'\n                        : message.distributed === 'wait' && !message.saved\n                        ? 'wait'\n                        : message.distributed\n                        ? 'double-checkmark'\n                        : 'checkmark'\n                    \"\n                    :param=\"message.seen ? 'seen' : ''\"\n                    class=\"vac-icon-check\"\n                  />\n                </slot>\n              </span>\n            </div>\n\n            <message-actions\n              :current-user-id=\"currentUserId\"\n              :message=\"message\"\n              :message-actions=\"messageActions\"\n              :room-footer-ref=\"roomFooterRef\"\n              :show-reaction-emojis=\"showReactionEmojis\"\n              :hide-options=\"hideOptions\"\n              :message-hover=\"messageHover\"\n              :hover-message-id=\"hoverMessageId\"\n              :hover-audio-progress=\"hoverAudioProgress\"\n              :toggle-labels-modal=\"toggleLabelsModal\"\n              @hide-options=\"$emit('hide-options', false)\"\n              @update-message-hover=\"messageHover = $event\"\n              @update-options-opened=\"optionsOpened = $event\"\n              @update-emoji-opened=\"emojiOpened = $event\"\n              @message-action-handler=\"messageActionHandler\"\n              @send-message-reaction=\"sendMessageReaction\"\n              @open-forward-modal=\"openForwardModal\"\n              @toggle-error-modal=\"toggleErrorModal\"\n            >\n              <template v-for=\"(i, name) in $scopedSlots\">\n                <slot :name=\"name\" v-bind=\"data\" />\n              </template>\n            </message-actions>\n          </div>\n\n          <message-reactions\n            :current-user-id=\"currentUserId\"\n            :message=\"message\"\n            @send-message-reaction=\"sendMessageReaction\"\n          />\n        </div>\n      </slot>\n    </div>\n  </div>\n</template>\n\n<script>\nimport SvgIcon from '../../components/SvgIcon/SvgIcon'\nimport FormatMessage from '../../components/FormatMessage/FormatMessage'\n\nimport MessageReply from './MessageReply/MessageReply'\nimport MessageFiles from './MessageFiles/MessageFiles'\nimport MessageActions from './MessageActions/MessageActions'\nimport MessageReactions from './MessageReactions/MessageReactions'\nimport AudioPlayer from './AudioPlayer/AudioPlayer'\n\nconst { messagesValidation } = require('../../utils/data-validation')\nconst { isAudioFile } = require('../../utils/media-file')\n\nexport default {\n  name: 'Message',\n  components: {\n    SvgIcon,\n    FormatMessage,\n    AudioPlayer,\n    MessageReply,\n    MessageFiles,\n    MessageActions,\n    MessageReactions\n  },\n\n  props: {\n    currentUserId: { type: [String, Number], required: true },\n    textMessages: { type: Object, required: true },\n    index: { type: Number, required: true },\n    message: { type: Object, required: true },\n    messages: { type: Array, required: true },\n    editedMessage: { type: Object, required: true },\n    roomUsers: { type: Array, default: () => [] },\n    messageActions: { type: Array, required: true },\n    roomFooterRef: { type: HTMLDivElement, default: null },\n    newMessages: { type: Array, default: () => [] },\n    showReactionEmojis: { type: Boolean, required: true },\n    showNewMessagesDivider: { type: Boolean, required: true },\n    textFormatting: { type: Boolean, required: true },\n    linkOptions: { type: Object, required: true },\n    hideOptions: { type: Boolean, required: true },\n    msgSearchQuery: { type: String, required: true },\n    toggleLabelsModal: { type: Function, default: () => ({}) },\n    isGroup: { type: Boolean, default: false },\n    unreadCounts: { type: Object, required: true },\n    roomId: { type: [String, Number], required: true }\n  },\n\n  emits: [\n    'hide-options',\n    'message-added',\n    'open-file',\n    'message-action-handler',\n    'send-message-reaction',\n    'carousel-handler',\n    'open-forward-modal',\n    'reply-msg-handler'\n  ],\n\n  data() {\n    return {\n      hoverMessageId: null,\n      messageHover: false,\n      optionsOpened: false,\n      emojiOpened: false,\n      newMessage: {},\n      progressTime: '- : -',\n      hoverAudioProgress: false\n    }\n  },\n\n  computed: {\n    showTimeStamp() {\n      return !this.message.files || (this.message.files && this.message.deleted)\n    },\n    showDate() {\n      return this.index > 0 && this.message.date !== this.messages[this.index - 1].date\n    },\n    messageOffset() {\n      return this.index === 0\n        ? true\n        : this.index > 0 && this.message.username !== this.messages[this.index - 1].username\n    },\n    isMessageHover() {\n      return this.editedMessage._id === this.message._id || this.hoverMessageId === this.message._id\n    },\n    isAudio() {\n      return this.message.files?.some(file => isAudioFile(file))\n    },\n\n    isCheckmarkVisible() {\n      return (\n        this.message.fromMe === 1 &&\n        !this.message.deleted &&\n        (this.message.saved ||\n          this.message.distributed ||\n          this.message.seen ||\n          this.message.distributed === 'wait' ||\n          this.message.failed)\n      )\n    }\n  },\n\n  watch: {\n    newMessages: {\n      immediate: true,\n      deep: true,\n      handler(val) {\n        if (!val.length || !this.showNewMessagesDivider) {\n          return (this.newMessage = {})\n        }\n\n        this.newMessage = val.reduce((res, obj) => (obj.index < res.index ? obj : res))\n      }\n    }\n  },\n\n  mounted() {\n    messagesValidation(this.message)\n\n    this.$emit('message-added', {\n      message: this.message,\n      index: this.index,\n      ref: this.$refs[this.message._id]\n    })\n  },\n\n  methods: {\n    onHoverMessage() {\n      this.messageHover = true\n      if (this.canEditMessage()) this.hoverMessageId = this.message._id\n    },\n    canEditMessage() {\n      return !this.message.deleted\n    },\n    onLeaveMessage() {\n      if (!this.optionsOpened && !this.emojiOpened) this.messageHover = false\n      this.hoverMessageId = null\n    },\n    openFile(file) {\n      this.$emit('open-file', { message: this.message, file })\n    },\n  \n    messageActionHandler(action) {\n      this.messageHover = false\n      this.hoverMessageId = null\n\n      setTimeout(() => {\n        this.$emit('message-action-handler', { action, message: this.message })\n      }, 300)\n    },\n    sendMessageReaction({ emoji, reaction }) {\n      this.$emit('send-message-reaction', {\n        messageId: this.message._id,\n        reaction: emoji,\n        remove: reaction && reaction.indexOf(this.currentUserId) !== -1\n      })\n      this.messageHover = false\n    },\n    carouselHandler(data) {\n      this.$emit('carousel-handler', data)\n    },\n    openForwardModal(msg) {\n      this.$emit('open-forward-modal', msg)\n    },\n    toggleErrorModal() {\n      this.$emit('toggle-error-modal')\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Message.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Message.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Message.vue?vue&type=template&id=74091b4b\"\nimport script from \"./Message.vue?vue&type=script&lang=js\"\nexport * from \"./Message.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "export default (items, prop1, prop2, val, startsWith = false) => {\n  if (!val || val === '') return items\n\n  return items.filter(v => {\n    if (startsWith)\n      return (\n        formatString(v[prop1]).startsWith(formatString(val)) || formatString(v[prop2]).startsWith(formatString(val))\n      )\n    return formatString(v[prop1]).includes(formatString(val)) || formatString(v[prop2]).includes(formatString(val))\n  })\n}\n\nfunction formatString(string) {\n  // console.log(string)\n  return string\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')\n}\n", "// Credits to https://github.com/g<PERSON><PERSON><PERSON><PERSON>/vue-audio-recorder\n\nlet lamejs\ntry {\n  lamejs = require('lamejs')\n} catch (_) {\n  lamejs = { missing: true }\n}\n\nconst { Mp3Encoder } = lamejs\n\nexport default class {\n  constructor(config) {\n    if (lamejs.missing) {\n      throw new Error(\n        'You must add lamejs in your dependencies to use the audio recorder. Please run \"npm install lamejs --save\"'\n      )\n    }\n\n    this.bitRate = config.bitRate\n    this.sampleRate = config.sampleRate\n    this.dataBuffer = []\n    this.encoder = new Mp3Encoder(1, this.sampleRate, this.bitRate)\n  }\n\n  encode(arrayBuffer) {\n    const maxSamples = 1152\n    const samples = this._convertBuffer(arrayBuffer)\n    let remaining = samples.length\n\n    for (let i = 0; remaining >= 0; i += maxSamples) {\n      const left = samples.subarray(i, i + maxSamples)\n      const buffer = this.encoder.encodeBuffer(left)\n      this.dataBuffer.push(new Int8Array(buffer))\n      remaining -= maxSamples\n    }\n  }\n\n  finish() {\n    this.dataBuffer.push(this.encoder.flush())\n    const blob = new Blob(this.dataBuffer, { type: 'audio/mp3' })\n    this.dataBuffer = []\n\n    return {\n      id: Date.now(),\n      blob,\n      url: URL.createObjectURL(blob)\n    }\n  }\n\n  _floatTo16BitPCM(input, output) {\n    for (let i = 0; i < input.length; i++) {\n      const s = Math.max(-1, Math.min(1, input[i]))\n      output[i] = s < 0 ? s * 0x8000 : s * 0x7fff\n    }\n  }\n\n  _convertBuffer(arrayBuffer) {\n    const data = new Float32Array(arrayBuffer)\n    const out = new Int16Array(arrayBuffer.length)\n    this._floatTo16BitPCM(data, out)\n    return out\n  }\n}\n", "// Credits to https://github.com/g<PERSON><PERSON><PERSON><PERSON>/vue-audio-recorder\n\nimport Mp3Encoder from './mp3-encoder'\n\nexport default class {\n  constructor(options = {}) {\n    this.beforeRecording = options.beforeRecording\n    this.pauseRecording = options.pauseRecording\n    this.afterRecording = options.afterRecording\n    this.micFailed = options.micFailed\n\n    this.encoderOptions = {\n      bitRate: options.bitRate,\n      sampleRate: options.sampleRate\n    }\n\n    this.bufferSize = 4096\n    this.records = []\n\n    this.isPause = false\n    this.isRecording = false\n\n    this.duration = 0\n    this.volume = 0\n\n    this._duration = 0\n  }\n\n  start() {\n    const constraints = {\n      video: false,\n      audio: {\n        channelCount: 1,\n        echoCancellation: false\n      }\n    }\n\n    this.beforeRecording && this.beforeRecording('start recording')\n\n    navigator.mediaDevices.getUserMedia(constraints).then(this._micCaptured.bind(this)).catch(this._micError.bind(this))\n\n    this.isPause = false\n    this.isRecording = true\n\n    if (!this.lameEncoder) {\n      this.lameEncoder = new Mp3Encoder(this.encoderOptions)\n    }\n  }\n\n  stop() {\n    this.stream.getTracks().forEach(track => track.stop())\n    this.input.disconnect()\n    this.processor.disconnect()\n    this.context.close()\n\n    let record = null\n\n    record = this.lameEncoder.finish()\n\n    record.duration = this.duration\n    this.records.push(record)\n\n    this._duration = 0\n    this.duration = 0\n\n    this.isPause = false\n    this.isRecording = false\n\n    this.afterRecording && this.afterRecording(record)\n  }\n\n  pause() {\n    this.stream.getTracks().forEach(track => track.stop())\n    this.input.disconnect()\n    this.processor.disconnect()\n\n    this._duration = this.duration\n    this.isPause = true\n\n    this.pauseRecording && this.pauseRecording('pause recording')\n  }\n\n  _micCaptured(stream) {\n    this.context = new (window.AudioContext || window.webkitAudioContext)()\n    this.duration = this._duration\n    this.input = this.context.createMediaStreamSource(stream)\n    this.processor = this.context.createScriptProcessor(this.bufferSize, 1, 1)\n    this.stream = stream\n\n    this.processor.onaudioprocess = ev => {\n      const sample = ev.inputBuffer.getChannelData(0)\n      let sum = 0.0\n\n      if (this.lameEncoder) {\n        this.lameEncoder.encode(sample)\n      }\n\n      for (let i = 0; i < sample.length; ++i) {\n        sum += sample[i] * sample[i]\n      }\n\n      this.duration = parseFloat(this._duration) + parseFloat(this.context.currentTime.toFixed(2))\n      this.volume = Math.sqrt(sum / sample.length).toFixed(2)\n    }\n\n    this.input.connect(this.processor)\n    this.processor.connect(this.context.destination)\n  }\n\n  _micError(error) {\n    this.micFailed && this.micFailed(error)\n  }\n}\n", "<template>\n  <div class=\"overflow-hidden w-100 room-container\">\n    <info v-if=\"disableFooter\" />\n    <div v-show=\"(isMobile && !showRoomsList) || !isMobile || singleRoom\" class=\"vac-col-messages\"\n      @touchstart=\"touchStart\">\n      <slot v-if=\"showNoRoom\" name=\"no-room-selected\">\n        <div class=\"vac-container-center vac-room-empty\">\n          <div>{{ textMessages.ROOM_EMPTY }}</div>\n        </div>\n      </slot>\n\n      <room-header v-else :current-user-id=\"currentUserId\" :text-messages=\"textMessages\" :single-room=\"singleRoom\"\n        :show-rooms-list=\"showRoomsList\" :is-mobile=\"isMobile\"\n        :menu-actions=\"menuActions\" :isMsgFetched=\"isMsgFetched\" :room=\"room\" :templates=\"templates\"\n        @handle-message-search=\"handleMessageSearch\" @add-template=\"addTemplate\" @add-template-msg=\"forwardTemplateMsg\"\n        @toggle-rooms-list=\"$emit('toggle-rooms-list')\"\n        @toggle-menu-bar=\"$emit('toggle-menu-bar')\" @redirect-to-hubspot=\"$emit('redirect-to-hubspot', room)\">\n        <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n          <slot :name=\"name\" v-bind=\"data\" />\n        </template>\n      </room-header>\n\n      <div ref=\"scrollContainer\" class=\"vac-container-scroll\" @scroll=\"onContainerScroll\">\n        <div class=\"vac-messages-container\">\n          <div :class=\"{ 'vac-messages-hidden': loadingMessages }\">\n            <transition name=\"vac-fade-message\">\n              <div>\n                <div v-if=\"showNoMessages\" class=\"vac-text-started\">\n                  <slot name=\"messages-empty\">\n                    {{ textMessages.MESSAGES_EMPTY }}\n                  </slot>\n                </div>\n                <div v-if=\"showMessagesStarted\" class=\"vac-text-started\">\n                  {{ textMessages.CONVERSATION_STARTED }} {{ messages[0].date }}\n                </div>\n              </div>\n            </transition>\n            <transition name=\"vac-fade-message\">\n              <infinite-loading v-if=\"messages.length\" :class=\"{ 'vac-infinite-loading': !messagesLoaded }\"\n                force-use-infinite-wrapper=\".vac-container-scroll\" web-component-name=\"vue-advanced-chat\"\n                spinner=\"spiral\" direction=\"top\" :distance=\"40\" @infinite=\"loadMoreMessages\">\n                <template #spinner>\n                  <loader :show=\"true\" :infinite=\"true\" />\n                </template>\n                <template #no-results>\n                  <div />\n                </template>\n                <template #no-more>\n                  <div />\n                </template>\n              </infinite-loading>\n            </transition>\n            <transition-group :key=\"roomId\" name=\"vac-fade-message\" tag=\"span\">\n              <div v-for=\"(m, i) in messages\" :key=\"m.indexId || m._id\">\n                <message :current-user-id=\"currentUserId\" :message=\"m\" :index=\"i\" :messages=\"messages\"\n                  :edited-message=\"editedMessage\" :message-actions=\"messageActions\" :room-users=\"room.users\"\n                  :text-messages=\"textMessages\" :room-footer-ref=\"$refs.roomFooter\" :new-messages=\"newMessages\"\n                  :show-reaction-emojis=\"showReactionEmojis\" :show-new-messages-divider=\"showNewMessagesDivider\"\n                  :text-formatting=\"textFormatting\" :link-options=\"linkOptions\" :hide-options=\"hideOptions\"\n                  :toggle-labels-modal=\"toggleLabelsModal\" :msg-search-query=\"msgSearchQuery\" :is-group=\"room.isGroup\"\n                  :unread-counts=\"unreadCounts\" :room-id=\"roomId\" @toggle-error-modal=\"toggleErrorModal\"\n                  @message-added=\"onMessageAdded\" @message-action-handler=\"messageActionHandler\" @open-file=\"openFile\"\n                  @send-message-reaction=\"sendMessageReaction\"\n                  @hide-options=\"hideOptions = $event\" @carousel-handler=\"carouselHandler\"\n                  @reply-msg-handler=\"replyMsgHandler\">\n                  <template v-for=\"(idx, name) in $scopedSlots\" #[name]=\"data\">\n                    <slot :name=\"name\" v-bind=\"data\" />\n                  </template>\n                </message>\n              </div>\n            </transition-group>\n          </div>\n        </div>\n      </div>\n      <div v-if=\"!loadingMessages\">\n        <transition name=\"vac-bounce\">\n          <div v-if=\"scrollIcon\" class=\"vac-icon-scroll\" @click=\"scrollToBottom\">\n            <transition name=\"vac-bounce\">\n              <div v-if=\"scrollMessagesCount\" class=\"vac-badge-counter vac-messages-count\">\n                {{ scrollMessagesCount }}\n              </div>\n            </transition>\n            <slot name=\"scroll-icon\">\n              <svg-icon name=\"dropdown\" param=\"scroll\" />\n            </slot>\n          </div>\n        </transition>\n      </div>\n      <div ref=\"roomFooter\" class=\"vac-room-footer\" :class=\"{\n        'vac-app-box-shadow': shadowFooter,\n        'footer-disabled': disableFooter,\n      }\">\n        <room-emojis :filtered-emojis=\"filteredEmojis\" :select-item=\"selectEmojiItem\"\n          :active-up-or-down=\"activeUpOrDownEmojis\" @select-emoji=\"selectEmoji($event)\"\n          @activate-item=\"activeUpOrDownEmojis = 0\" />\n\n        <room-message-reply :room=\"room\" :message-reply=\"messageReply\" :text-formatting=\"textFormatting\"\n          :link-options=\"linkOptions\" @reset-message=\"resetMessage\">\n          <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n            <slot :name=\"name\" v-bind=\"data\" />\n          </template>\n        </room-message-reply>\n\n        <div class=\"vac-box-footer\">\n          <div v-if=\"!showAudio && !files.length\" class=\"vac-icon-textarea-left\">\n            <template v-if=\"isRecording\">\n              <div class=\"vac-svg-button vac-icon-audio-stop\" @click=\"stopRecorder\">\n                <slot name=\"audio-stop-icon\">\n                  <svg-icon name=\"close-outline\" />\n                </slot>\n              </div>\n\n              <div class=\"vac-dot-audio-record\" />\n\n              <div class=\"vac-dot-audio-record-time\">\n                {{ recordedTime }}\n              </div>\n\n              <div class=\"vac-svg-button vac-icon-audio-confirm\" @click=\"toggleRecorder(false)\">\n                <slot name=\"audio-stop-icon\">\n                  <svg-icon name=\"checkmark\" />\n                </slot>\n              </div>\n            </template>\n\n            <div v-else class=\"vac-svg-button\" @click=\"toggleRecorder(true)\">\n              <slot name=\"microphone-icon\">\n                <svg-icon name=\"microphone\" class=\"vac-icon-microphone\" />\n              </slot>\n            </div>\n          </div>\n\n          <room-files v-if=\"!showUploadModal\" :files=\"files\" @remove-file=\"removeFile\" @reset-message=\"resetMessage\">\n            <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n              <slot :name=\"name\" v-bind=\"data\" />\n            </template>\n          </room-files>\n\n          <div class=\"textarea-wrapper\">\n            <div class=\"vac-icon-textarea\">\n              <div v-if=\"editedMessage._id\" class=\"vac-svg-button\" @click=\"resetMessage\">\n                <slot name=\"edit-close-icon\">\n                  <svg-icon name=\"close-outline\" />\n                </slot>\n              </div>\n\n              <emoji-picker-container v-if=\"showEmojis\" v-click-outside=\"() => (emojiOpened = false)\"\n                :emoji-opened=\"emojiOpened\" :position-top=\"true\" @add-emoji=\"addEmoji\"\n                @open-emoji=\"emojiOpened = $event\">\n                <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n                  <slot :name=\"name\" v-bind=\"data\" />\n                </template>\n              </emoji-picker-container>\n\n              <div v-if=\"showFiles\" class=\"vac-svg-button paperclip-icon\" @click=\"launchFilePicker\">\n                <slot name=\"paperclip-icon\">\n                  <svg-icon name=\"paperclip\" width=\"19\" height=\"40\" />\n                </slot>\n              </div>\n\n              <div v-if=\"textareaActionEnabled\" class=\"vac-svg-button\" @click=\"textareaActionHandler\">\n                <slot name=\"custom-action-icon\">\n                  <svg-icon name=\"deleted\" />\n                </slot>\n              </div>\n\n              <input v-if=\"showFiles\" ref=\"file\" type=\"file\" multiple :accept=\"acceptedFiles\" style=\"display: none;\"\n                @change=\"onFileChange($event.target.files)\" />\n            </div>\n            <div class=\"textarea-box\">\n              <textarea :disabled=\"disableFooter || messageInTransit\" ref=\"roomTextarea\" placeholder=\"Type Something...\"\n                class=\"vac-textarea\" :class=\"{\n                  'vac-textarea-outline': editedMessage._id,\n                }\" :style=\"{\n                  position: `relative`,\n                  maxHeight: `100px`,\n                }\" @input=\"onChangeInput\" @keydown.esc=\"escapeTextarea\" @keydown.enter.exact.prevent=\"selectItem\"\n                @paste=\"onPasteImage\" @keydown.tab.exact.prevent=\"\" @keydown.tab=\"selectItem\"\n                @keydown.up=\"updateActiveUpOrDown($event, -1)\" @keydown.down=\"updateActiveUpOrDown($event, 1)\" />\n\n              <div v-if=\"showSendIcon\" class=\"vac-svg-button send-icon\" :class=\"{ 'vac-send-disabled': isMessageEmpty }\"\n                @click=\"sendMessage\">\n                <img v-if=\"!messageInTransit\" :src=\"isMessageEmpty ? sendIconDisabled : sendIcon\" alt=\"Send Icon\"\n                  :param=\"isMessageEmpty ? 'disabled' : ''\" />\n                <div v-if=\"messageInTransit\" class=\"spinner-border text-primary\" role=\"status\">\n                  <span class=\"visually-hidden\">Loading...</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <upload-modal :show=\"showUploadModal\" :files=\"files\" :toggle=\"toggleUploadModal\" :handle-upload=\"handleUpload\" />\n  </div>\n</template>\n\n<script>\nimport InfiniteLoading from 'vue-infinite-loading'\nimport vClickOutside from 'v-click-outside'\nimport { Database } from 'emoji-picker-element'\n\nimport Loader from '../../components/Loader/Loader'\nimport SvgIcon from '../../components/SvgIcon/SvgIcon'\nimport SendIconDisabled from '../../components/SvgIcon/send_icon-disabled.svg'\nimport SendIcon from '../../components/SvgIcon/send_icon.svg'\nimport EmojiPickerContainer from '../../components/EmojiPickerContainer/EmojiPickerContainer'\nimport UploadModal from '../../components/UploadModal/UploadModal'\nimport Info from '../../components/InfoModal/Info'\n\nimport RoomHeader from './RoomHeader/RoomHeader'\nimport RoomFiles from './RoomFiles/RoomFiles'\nimport RoomMessageReply from './RoomMessageReply/RoomMessageReply'\nimport RoomEmojis from './RoomEmojis/RoomEmojis'\nimport Message from '../Message/Message'\n\nimport filteredItems from '../../utils/filter-items'\nimport Recorder from '../../utils/recorder'\nimport axios from 'axios'\n\nconst { detectMobile, iOSDevice } = require('../../utils/mobile-detection')\n\nconst debounce = (func, delay) => {\n  let inDebounce\n  return function () {\n    const context = this\n    const args = arguments\n    clearTimeout(inDebounce)\n    inDebounce = setTimeout(() => func.apply(context, args), delay)\n  }\n}\n\nexport default {\n  name: 'Room',\n  components: {\n    InfiniteLoading,\n    Loader,\n    SvgIcon,\n    EmojiPickerContainer,\n    RoomHeader,\n    RoomFiles,\n    RoomMessageReply,\n    RoomEmojis,\n    Message,\n    UploadModal,\n    Info\n  },\n\n  directives: {\n    clickOutside: vClickOutside.directive,\n  },\n\n  props: {\n    currentUserId: { type: [String, Number], required: true },\n    textMessages: { type: Object, required: true },\n    singleRoom: { type: Boolean, required: true },\n    showRoomsList: { type: Boolean, required: true },\n    isMobile: { type: Boolean, required: true },\n    rooms: { type: Array, required: true },\n    roomId: { type: [String, Number], required: true },\n    loadFirstRoom: { type: Boolean, required: true },\n    messages: { type: Array, required: true },\n    roomMessage: { type: String, default: null },\n    messagesLoaded: { type: Boolean, required: true },\n    menuActions: { type: Array, required: true },\n    messageActions: { type: Array, required: true },\n    showSendIcon: { type: Boolean, required: true },\n    isMsgFetched: { type: Boolean, default: true },\n    showFiles: { type: Boolean, required: true },\n    showAudio: { type: Boolean, required: true },\n    audioBitRate: { type: Number, required: true },\n    audioSampleRate: { type: Number, required: true },\n    showEmojis: { type: Boolean, required: true },\n    showReactionEmojis: { type: Boolean, required: true },\n    showNewMessagesDivider: { type: Boolean, required: true },\n    showFooter: { type: Boolean, required: true },\n    acceptedFiles: { type: String, required: true },\n    textFormatting: { type: Boolean, required: true },\n    linkOptions: { type: Object, required: true },\n    loadingRooms: { type: Boolean, required: true },\n    messageInTransit: { type: Boolean, required: true },\n    textareaActionEnabled: { type: Boolean, required: true },\n    templatesText: { type: Array, default: null },\n    toggleLabelsModal: { type: Function, default: () => ({}) },\n    templates: { type: Array, required: true },\n    unreadCounts: { type: Object, required: true },\n  },\n\n  emits: [\n    'toggle-rooms-list',\n    'edit-message',\n    'send-message',\n    'delete-message',\n    'message-action-handler',\n    'fetch-messages',\n    'send-message-reaction',\n    'typing-message',\n    'open-file',\n    'textarea-action-handler',\n    'carousel-handler',\n    'open-forward-modal',\n    'open-template-modal',\n    'toggle-menu-bar',\n    'redirect-to-hubspot',\n  ],\n\n  data() {\n    return {\n      disableFooter: false,\n      sendIcon: SendIcon,\n      sendIconDisabled: SendIconDisabled,\n      message: '',\n      editedMessage: {},\n      messageReply: null,\n      infiniteState: null,\n      loadingMessages: true,\n      loadingMoreMessages: false,\n      files: [],\n      fileDialog: false,\n      emojiOpened: false,\n      hideOptions: true,\n      scrollIcon: false,\n      scrollMessagesCount: 0,\n      newMessages: [],\n      keepKeyboardOpen: false,\n      filteredEmojis: [],\n      filteredUsersTag: [],\n      selectedUsersTag: [],\n      filteredTemplatesText: [],\n      selectEmojiItem: null,\n      selectUsersTagItem: null,\n      selectTemplatesTextItem: null,\n      activeUpOrDownEmojis: null,\n      activeUpOrDownUsersTag: null,\n      activeUpOrDownTemplatesText: null,\n      textareaCursorPosition: null,\n      cursorRangePosition: null,\n      emojisDB: new Database(),\n      recorder: this.initRecorder(),\n      isRecording: false,\n      format: 'mp3',\n      showUploadModal: false,\n      msgSearchQuery: '',\n\n    }\n  },\n\n  computed: {\n    room() {\n      return this.rooms.find((room) => room.roomId === this.roomId) || {}\n    },\n    showNoMessages() {\n      return (\n        this.room.roomId &&\n        !this.messages.length &&\n        !this.loadingMessages &&\n        !this.loadingRooms\n      )\n    },\n    showNoRoom() {\n      const noRoomSelected =\n        (!this.rooms.length && !this.loadingRooms) ||\n        (!this.room.roomId && !this.loadFirstRoom)\n\n      if (noRoomSelected) {\n        this.loadingMessages = false /* eslint-disable-line vue/no-side-effects-in-computed-properties */\n      }\n      return noRoomSelected\n    },\n    showMessagesStarted() {\n      return this.messages.length && this.messagesLoaded\n    },\n    isMessageEmpty() {\n      return !this.files.length && !this.message.trim()\n    },\n    recordedTime() {\n      return new Date(this.recorder.duration * 1000).toISOString().substr(14, 5)\n    },\n    shadowFooter() {\n      return (\n        !!this.filteredEmojis.length ||\n        !!this.filteredUsersTag.length ||\n        !!this.filteredTemplatesText.length ||\n        !!this.files.length ||\n        !!this.messageReply\n      )\n    },\n  },\n\n  watch: {\n    message(val) {\n      this.getTextareaRef().value = val\n    },\n    loadingMessages(val) {\n      if (val) {\n        this.infiniteState = null\n      } else {\n        if (this.infiniteState) this.infiniteState.loaded()\n        this.focusTextarea(true)\n      }\n    },\n    room: {\n      immediate: true,\n      handler(newVal, oldVal) {\n        if (newVal.roomId && (!oldVal || newVal.roomId !== oldVal.roomId)) {\n          this.onRoomChanged()\n        }\n      },\n    },\n    roomMessage: {\n      immediate: true,\n      handler(val) {\n        if (val) this.message = this.roomMessage\n      },\n    },\n    messages: {\n      deep: true,\n      handler(newVal, oldVal) {\n        this.newMessages = [] // experiment\n        newVal.forEach((message, i) => {\n          if (this.showNewMessagesDivider && message.new && !message.fromMe) {\n            this.newMessages.push({\n              _id: message._id,\n              index: i,\n            })\n          }\n        })\n        if (oldVal?.length === newVal?.length - 1) {\n          this.newMessages = []\n        }\n        if (this.infiniteState) {\n          this.infiniteState.loaded()\n        }\n        this.getLastMessageFromOther() // Call your function here\n        setTimeout(() => (this.loadingMoreMessages = false))\n      },\n    },\n    messagesLoaded(val) {\n      if (val) this.loadingMessages = false\n      if (this.infiniteState) this.infiniteState.complete()\n    },\n  },\n\n  mounted() {\n    this.newMessages = []\n    const isMobile = detectMobile()\n\n    this.getTextareaRef().addEventListener(\n      'keyup',\n      debounce((e) => {\n        if (e.key === 'Enter' && !e.shiftKey && !this.fileDialog) {\n          if (isMobile) {\n            this.message = this.message + '\\n'\n            setTimeout(() => this.onChangeInput())\n          } else if (\n            !this.filteredEmojis.length &&\n            !this.filteredUsersTag.length &&\n            !this.filteredTemplatesText.length\n          ) {\n            this.sendMessage()\n          }\n        }\n\n        setTimeout(() => {\n          this.updateFooterList('@')\n          this.updateFooterList(':')\n          this.updateFooterList('/')\n        }, 60)\n      }),\n      50\n    )\n\n    this.getTextareaRef().addEventListener('click', () => {\n      if (isMobile) this.keepKeyboardOpen = true\n\n      this.updateFooterList('@')\n      this.updateFooterList(':')\n      this.updateFooterList('/')\n    })\n\n    this.getTextareaRef().addEventListener('blur', () => {\n      this.resetFooterList()\n      if (isMobile) setTimeout(() => (this.keepKeyboardOpen = false))\n    })\n    this.getLastMessageFromOther()\n  },\n\n  beforeDestroy() {\n    this.stopRecorder()\n  },\n\n  methods: {\n\n    forwardTemplateMsg(newItem) {\n      this.$emit('add-template-msg', newItem); // Forward template  msg to the parent\n    },\n\n      getLastMessageFromOther() {\n      if (!Array.isArray(this.messages)) return null;\n\n      const reversedMessages = [...this.messages].reverse();\n      const lastMessageFromOther = reversedMessages.find((message) => message.fromMe === 0);\n\n      const now = new Date();\n\n      if (lastMessageFromOther) {\n        const timestamp = lastMessageFromOther.time;\n        const messageTime = new Date(timestamp * 1000);\n        const hoursDifference = (now - messageTime) / (1000 * 60 * 60);\n        this.disableFooter = hoursDifference > 24;\n      } else {\n        this.disableFooter = true;\n      }\n      if (this.disableFooter && this.$refs.roomTextarea) {\n        this.$refs.roomTextarea.blur();\n      }\n    },\n\n    handleMessageSearch(txt) {\n      this.msgSearchQuery = txt\n    },\n    getTextareaRef() {\n      return this.$refs.roomTextarea\n    },\n    touchStart(touchEvent) {\n      if (this.singleRoom) return\n\n      if (touchEvent.changedTouches.length === 1) {\n        const posXStart = touchEvent.changedTouches[0].clientX\n        const posYStart = touchEvent.changedTouches[0].clientY\n\n        addEventListener(\n          'touchend',\n          (touchEvent) => this.touchEnd(touchEvent, posXStart, posYStart),\n          { once: true }\n        )\n      }\n    },\n    touchEnd(touchEvent, posXStart, posYStart) {\n      if (touchEvent.changedTouches.length === 1) {\n        const posXEnd = touchEvent.changedTouches[0].clientX\n        const posYEnd = touchEvent.changedTouches[0].clientY\n\n        const swippedRight = posXEnd - posXStart > 100\n        const swippedVertically = Math.abs(posYEnd - posYStart) > 50\n\n        if (swippedRight && !swippedVertically) {\n          this.$emit('toggle-rooms-list')\n        }\n      }\n    },\n    onRoomChanged() {\n      this.loadingMessages = true\n      this.scrollIcon = false\n      this.scrollMessagesCount = 0\n      this.resetMessage(true, true)\n\n      if (this.roomMessage) {\n        this.message = this.roomMessage\n        setTimeout(() => this.onChangeInput())\n      }\n\n      if (!this.messages.length && this.messagesLoaded) {\n        this.loadingMessages = false\n      }\n\n      const unwatch = this.$watch(\n        () => this.messages,\n        (val) => {\n          if (!val || !val.length) return\n\n          const element = this.$refs.scrollContainer\n          if (!element) return\n\n          unwatch()\n\n          setTimeout(() => {\n            element.scrollTo({ top: element.scrollHeight })\n            this.loadingMessages = false\n          })\n        }\n      )\n    },\n    onMessageAdded({ message, index, ref }) {\n      if (index !== this.messages.length - 1) return\n      const autoScrollOffset = ref.offsetHeight + 60\n\n      setTimeout(() => {\n        if (\n          this.getBottomScroll(this.$refs.scrollContainer) < autoScrollOffset\n        ) {\n          this.scrollToBottom()\n        } else {\n          if (message.fromMe === 1) {\n            this.scrollToBottom()\n          } else {\n            this.scrollIcon = true\n            this.scrollMessagesCount++\n          }\n        }\n      })\n    },\n    onContainerScroll(e) {\n      this.hideOptions = true\n\n      if (!e.target) return\n\n      const bottomScroll = this.getBottomScroll(e.target)\n      if (bottomScroll < 60) this.scrollMessagesCount = 0\n      this.scrollIcon = bottomScroll > 500 || this.scrollMessagesCount\n    },\n    updateFooterList(tagChar) {\n      if (!this.getTextareaRef()) return\n\n      if (\n        tagChar === '@' &&\n        (!this.room.users || this.room.users.length <= 2)\n      ) {\n        return\n      }\n\n      if (tagChar === '/' && !this.templatesText) {\n        return\n      }\n\n      if (\n        this.textareaCursorPosition === this.getTextareaRef().selectionStart\n      ) {\n        return\n      }\n\n      this.textareaCursorPosition = this.getTextareaRef().selectionStart\n\n      let position = this.textareaCursorPosition\n\n      while (\n        position > 0 &&\n        this.message.charAt(position - 1) !== tagChar &&\n        this.message.charAt(position - 1) !== ' '\n      ) {\n        position--\n      }\n\n      const beforeTag = this.message.charAt(position - 2)\n      const notLetterNumber = !beforeTag.match(/^[0-9a-zA-Z]+$/)\n\n      if (\n        this.message.charAt(position - 1) === tagChar &&\n        (!beforeTag || beforeTag === ' ' || notLetterNumber)\n      ) {\n        const query = this.message.substring(\n          position,\n          this.textareaCursorPosition\n        )\n        if (tagChar === ':') {\n          this.updateEmojis(query)\n        } else if (tagChar === '@') {\n          this.updateShowUsersTag(query)\n        } else if (tagChar === '/') {\n          this.updateShowTemplatesText(query)\n        }\n      } else {\n        this.resetFooterList(tagChar)\n      }\n    },\n    getCharPosition(tagChar) {\n      const cursorPosition = this.getTextareaRef().selectionStart\n\n      let position = cursorPosition\n      while (position > 0 && this.message.charAt(position - 1) !== tagChar) {\n        position--\n      }\n\n      let endPosition = position\n      while (\n        this.message.charAt(endPosition) &&\n        this.message.charAt(endPosition).trim()\n      ) {\n        endPosition++\n      }\n\n      return { position, endPosition }\n    },\n    async updateEmojis(query) {\n      if (!query) return\n\n      const emojis = await this.emojisDB.getEmojiBySearchQuery(query)\n      this.filteredEmojis = emojis.map((emoji) => emoji.unicode)\n    },\n    selectEmoji(emoji) {\n      this.selectEmojiItem = false\n\n      if (!emoji) return\n\n      const { position, endPosition } = this.getCharPosition(':')\n\n      this.message =\n        this.message.substr(0, position - 1) +\n        emoji +\n        this.message.substr(endPosition, this.message.length - 1)\n\n      this.cursorRangePosition = position\n      this.focusTextarea()\n    },\n    updateShowUsersTag(query) {\n      this.filteredUsersTag = filteredItems(\n        this.room.users,\n        'username',\n        query,\n        true\n      ).filter((user) => user._id !== this.currentUserId)\n    },\n    selectUserTag(user, editMode = false) {\n      this.selectUsersTagItem = false\n\n      if (!user) return\n\n      const { position, endPosition } = this.getCharPosition('@')\n\n      const space = this.message.substr(endPosition, endPosition).length\n        ? ''\n        : ' '\n\n      this.message =\n        this.message.substr(0, position) +\n        user.username +\n        space +\n        this.message.substr(endPosition, this.message.length - 1)\n\n      this.selectedUsersTag = [...this.selectedUsersTag, { ...user }]\n\n      if (!editMode) {\n        this.cursorRangePosition =\n          position + user.username.length + space.length + 1\n      }\n\n      this.focusTextarea()\n    },\n    updateShowTemplatesText(query) {\n      this.filteredTemplatesText = filteredItems(\n        this.templatesText,\n        'tag',\n        query,\n        true\n      )\n    },\n    selectTemplateText(template) {\n      this.selectTemplatesTextItem = false\n\n      if (!template) return\n\n      const { position, endPosition } = this.getCharPosition('/')\n\n      const space = this.message.substr(endPosition, endPosition).length\n        ? ''\n        : ' '\n\n      this.message =\n        this.message.substr(0, position - 1) +\n        template.text +\n        space +\n        this.message.substr(endPosition, this.message.length - 1)\n\n      this.cursorRangePosition =\n        position + template.text.length + space.length + 1\n\n      this.focusTextarea()\n    },\n    updateActiveUpOrDown(event, direction) {\n      if (this.filteredEmojis.length) {\n        this.activeUpOrDownEmojis = direction\n        event.preventDefault()\n      } else if (this.filteredUsersTag.length) {\n        this.activeUpOrDownUsersTag = direction\n        event.preventDefault()\n      } else if (this.filteredTemplatesText.length) {\n        this.activeUpOrDownTemplatesText = direction\n        event.preventDefault()\n      }\n    },\n    selectItem() {\n      if (this.filteredEmojis.length) {\n        this.selectEmojiItem = true\n      } else if (this.filteredUsersTag.length) {\n        this.selectUsersTagItem = true\n      } else if (this.filteredTemplatesText.length) {\n        this.selectTemplatesTextItem = true\n      }\n    },\n    resetFooterList(tagChar = null) {\n      if (tagChar === ':') {\n        this.filteredEmojis = []\n      } else if (tagChar === '@') {\n        this.filteredUsersTag = []\n      } else if (tagChar === '/') {\n        this.filteredTemplatesText = []\n      } else {\n        this.filteredEmojis = []\n        this.filteredUsersTag = []\n        this.filteredTemplatesText = []\n      }\n\n      this.textareaCursorPosition = null\n    },\n    escapeTextarea() {\n      if (this.filteredEmojis.length) this.filteredEmojis = []\n      else if (this.filteredUsersTag.length) this.filteredUsersTag = []\n      else if (this.filteredTemplatesText.length) {\n        this.filteredTemplatesText = []\n      } else this.resetMessage()\n    },\n    resetMessage(disableMobileFocus = false, initRoom = false) {\n      if (!initRoom) {\n        this.$emit('typing-message', null)\n      }\n\n      this.selectedUsersTag = []\n      this.resetFooterList()\n      this.resetTextareaSize()\n      this.message = ''\n      this.editedMessage = {}\n      this.messageReply = null\n      this.files = []\n      this.emojiOpened = false\n      this.preventKeyboardFromClosing()\n      setTimeout(() => this.focusTextarea(disableMobileFocus))\n    },\n    resetTextareaSize() {\n      if (this.getTextareaRef()) {\n        this.getTextareaRef().style.height = '20px'\n      }\n    },\n    focusTextarea(disableMobileFocus) {\n      if (detectMobile() && disableMobileFocus) return\n      if (!this.getTextareaRef()) return\n      this.getTextareaRef().focus()\n\n      if (this.cursorRangePosition) {\n        setTimeout(() => {\n          this.getTextareaRef().setSelectionRange(\n            this.cursorRangePosition,\n            this.cursorRangePosition\n          )\n          this.cursorRangePosition = null\n        })\n      }\n    },\n    preventKeyboardFromClosing() {\n      if (this.keepKeyboardOpen) this.getTextareaRef().focus()\n    },\n    sendMessage() {\n      let message = this.message.trim()\n\n      if (!this.files.length && !message) return\n\n      this.selectedUsersTag.forEach((user) => {\n        message = message.replace(\n          `@${user.username}`,\n          `<usertag>${user._id}</usertag>`\n        )\n      })\n\n      const files = this.files.length ? this.files : null\n\n      if (this.editedMessage._id) {\n        if (\n          this.editedMessage.content !== message ||\n          this.editedMessage.files?.length ||\n          this.files.length\n        ) {\n          this.$emit('edit-message', {\n            messageId: this.editedMessage._id,\n            newContent: message,\n            files,\n            replyMessage: this.messageReply,\n            usersTag: this.selectedUsersTag,\n          })\n        }\n      } else {\n        this.$emit('send-message', {\n          content: message,\n          files,\n          replyMessage: this.messageReply,\n          usersTag: this.selectedUsersTag,\n        })\n      }\n\n      this.resetMessage(true)\n    },\n    loadMoreMessages(infiniteState) {\n      if (this.loadingMessages) {\n        this.infiniteState = infiniteState\n        return\n      }\n\n      setTimeout(\n        () => {\n          if (this.loadingMoreMessages) return\n\n          if (this.messagesLoaded || !this.room.roomId) {\n            return infiniteState.complete()\n          }\n\n          this.infiniteState = infiniteState\n          this.$emit('fetch-messages')\n          this.loadingMoreMessages = true\n        },\n        // prevent scroll bouncing issue on iOS devices\n        iOSDevice() ? 500 : 0\n      )\n    },\n    messageActionHandler({ action, message }) {\n      switch (action.name) {\n        case 'replyMessage':\n          // console.log('reply')\n          return this.replyMessage(message)\n        case 'editMessage':\n          return this.editMessage(message)\n        case 'deleteMessage':\n          return this.$emit('delete-message', message)\n        default:\n          return this.$emit('message-action-handler', { action, message })\n      }\n    },\n    sendMessageReaction(messageReaction) {\n      this.$emit('send-message-reaction', messageReaction)\n    },\n    replyMessage(message) {\n      this.editedMessage = {}\n      this.messageReply = message\n      this.focusTextarea()\n    },\n    editMessage(message) {\n      this.resetMessage()\n\n      this.editedMessage = { ...message }\n\n      let messageContent = message.content\n      const initialContent = messageContent\n\n      const firstTag = '<usertag>'\n      const secondTag = '</usertag>'\n\n      const usertags = [\n        ...messageContent.matchAll(new RegExp(firstTag, 'gi')),\n      ].map((a) => a.index)\n\n      usertags.forEach((index) => {\n        const userId = initialContent.substring(\n          index + firstTag.length,\n          initialContent.indexOf(secondTag, index)\n        )\n\n        const user = this.room.users.find((user) => user._id === userId)\n\n        messageContent = messageContent.replace(\n          `${firstTag}${userId}${secondTag}`,\n          `@${user?.username || 'unknown'}`\n        )\n\n        this.selectUserTag(user, true)\n      })\n\n      this.message = messageContent\n\n      if (message.files) {\n        this.files = [...message.files]\n      }\n\n      setTimeout(() => this.resizeTextarea())\n    },\n    getBottomScroll(element) {\n      const { scrollHeight, clientHeight, scrollTop } = element\n      return scrollHeight - clientHeight - scrollTop\n    },\n    scrollToBottom() {\n      setTimeout(() => {\n        const element = this.$refs.scrollContainer\n        element.classList.add('vac-scroll-smooth')\n        element.scrollTo({ top: element.scrollHeight, behavior: 'smooth' })\n        setTimeout(() => element.classList.remove('vac-scroll-smooth'))\n      }, 50)\n    },\n    onChangeInput: debounce(function (e) {\n      this.message = e?.target?.value\n      this.keepKeyboardOpen = true\n      this.resizeTextarea()\n      this.$emit('typing-message', this.message)\n    }, 100),\n    resizeTextarea() {\n      const el = this.getTextareaRef()\n\n      if (!el) return\n\n      const padding = window\n        .getComputedStyle(el, null)\n        .getPropertyValue('padding-top')\n        .replace('px', '')\n      el.style.height = 0\n      el.style.height = el.scrollHeight - padding * 2 + 'px'\n    },\n    addEmoji(emoji) {\n      this.message += emoji.unicode\n      this.focusTextarea(true)\n    },\n    launchFilePicker() {\n      this.$refs.file.value = ''\n      this.toggleUploadModal()\n    },\n    onPasteImage(pasteEvent) {\n      const items = [...pasteEvent.clipboardData?.items]\n\n      if (items) {\n        items.forEach((item) => {\n          if (item.type.includes('image')) {\n            const blob = item.getAsFile()\n            this.onFileChange([blob])\n          }\n        })\n      }\n    },\n    async onFileChange(files) {\n      this.fileDialog = true\n      this.focusTextarea()\n\n      const existingFilesArr = this.files.map((el) => el.name)\n\n      const validFiles = Array.from(files).filter((file) => file.type)\n\n      const filesArr = [...new Set(validFiles)]\n      filesArr.forEach(async (file, i) => {\n        try {\n          const fileURL = URL.createObjectURL(file)\n          const blobFile = await fetch(fileURL).then((res) => {\n            return res.blob()\n          })\n          const typeIndex = file.name.lastIndexOf('.')\n          const fileName = file.name.substring(0, typeIndex)\n          if (!existingFilesArr.includes(fileName)) {\n            this.files.push({\n              blob: blobFile,\n              name: fileName,\n              size: file.size,\n              extension: file.name.substring(typeIndex + 1),\n              localUrl: fileURL,\n              url: fileURL,\n              rawData: file,\n              loading: true,\n              error: false,\n            })\n          }\n          if (i === filesArr.length - 1) {\n            setTimeout(() => {\n              this.fileDialog = false\n              this.showUploadModal = false\n            }, 500)\n          }\n        } catch (err) {\n          // console.log(err)\n          setTimeout(() => {\n            this.fileDialog = false\n            this.showUploadModal = false\n          }, 500)\n        }\n      })\n    },\n    removeFile(index) {\n      this.files.splice(index, 1)\n      this.focusTextarea()\n    },\n    initRecorder() {\n      this.isRecording = false\n\n      return new Recorder({\n        bitRate: this.audioBitRate,\n        sampleRate: this.audioSampleRate,\n        beforeRecording: null,\n        afterRecording: null,\n        pauseRecording: null,\n        micFailed: this.micFailed,\n      })\n    },\n    micFailed() {\n      this.isRecording = false\n      this.recorder = this.initRecorder()\n    },\n    toggleRecorder(recording) {\n      this.isRecording = recording\n\n      if (!this.recorder.isRecording) {\n        setTimeout(() => this.recorder.start(), 200)\n      } else {\n        try {\n          this.recorder.stop()\n\n          const record = this.recorder.records[0]\n\n          this.files.push({\n            blob: record.blob,\n            name: `audio.${this.format}`,\n            size: record.blob.size,\n            duration: record.duration,\n            type: record.blob.type,\n            audio: true,\n            localUrl: URL.createObjectURL(record.blob),\n          })\n\n          this.recorder = this.initRecorder()\n          this.sendMessage()\n        } catch {\n          setTimeout(() => this.stopRecorder(), 100)\n        }\n      }\n    },\n    stopRecorder() {\n      if (this.recorder.isRecording) {\n        try {\n          this.recorder.stop()\n          this.recorder = this.initRecorder()\n        } catch {\n          setTimeout(() => this.stopRecorder(), 100)\n        }\n      }\n    },\n    openFile({ message, file }) {\n      this.$emit('open-file', { message, file })\n    },\n    textareaActionHandler() {\n      this.$emit('textarea-action-handler', this.message)\n    },\n    addTemplate(msg) {\n      this.message = msg\n      setTimeout(() => this.resizeTextarea())\n      this.focusTextarea(true)\n    },\n    toggleUploadModal() {\n      this.showUploadModal = !this.showUploadModal\n    },\n    handleUpload($event) {\n      if ($event.dataTransfer) {\n        this.onFileChange($event.dataTransfer.files)\n      } else {\n        this.$refs.file.click()\n      }\n    },\n    carouselHandler(data) {\n      this.$emit('carousel-handler', data)\n    },\n\n    toggleErrorModal() {\n      this.$emit('toggle-error-modal')\n    },\n    replyMsgHandler(msg) {\n      const element = document.getElementById(`${msg.replyMessage._id}-child`)\n      if (element) {\n        element.style.border = '2px solid orangered'\n        element.scrollIntoView()\n        setTimeout(() => (element.style.border = 'none'), 500)\n      }\n    },\n  },\n}\n</script>\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Room.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Room.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Room.vue?vue&type=template&id=a61f0b28\"\nimport script from \"./Room.vue?vue&type=script&lang=js\"\nexport * from \"./Room.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "export default {\n  ROOMS_EMPTY: 'No chats',\n  RO<PERSON>_EMPTY: 'No chat selected',\n  NEW_MESSAGES: 'New Messages',\n  MESSAGE_DELETED: 'This message was deleted',\n  MESSAGES_EMPTY: 'No messages',\n  CONVERSATION_STARTED: 'Conversation started on:',\n  TYPE_MESSAGE: 'Type message',\n  SEARCH: 'Search',\n  IS_ONLINE: 'is online',\n  LAST_SEEN: 'last seen ',\n  IS_TYPING: 'is writing...'\n}\n", "export const defaultThemeStyles = {\n  light: {\n    general: {\n      color: '#0a0a0a',\n      backgroundInput: '#fff',\n      colorPlaceholder: '#9ca6af',\n      colorCaret: '#1976d2',\n      colorSpinner: '#333',\n      borderStyle: '1px solid #e1e4e8',\n      backgroundScrollIcon: '#fff'\n    },\n\n    container: {\n      border: 'none',\n      borderRadius: '4px',\n      boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)'\n    },\n\n    header: {\n      background: '#fff',\n      colorRoomName: '#0a0a0a',\n      colorRoomInfo: '#9ca6af'\n    },\n\n    footer: {\n      background: '#f8f9fa',\n      borderStyleInput: '1px solid #e1e4e8',\n      borderInputSelected: '#1976d2',\n      backgroundReply: '#e5e5e6',\n      backgroundTagActive: '#e5e5e6',\n      backgroundTag: '#f8f9fa'\n    },\n\n    content: {\n      background: '#f8f9fa'\n    },\n\n    sidemenu: {\n      background: '#fff',\n      backgroundHover: '#f6f6f6',\n      backgroundActive: '#e5effa',\n      colorActive: '#1976d2',\n      borderColorSearch: '#e1e5e8'\n    },\n\n    dropdown: {\n      background: '#fff',\n      backgroundHover: '#f6f6f6'\n    },\n\n    message: {\n      background: '#fff',\n      backgroundMe: '#ccf2cf',\n      color: '#0a0a0a',\n      colorStarted: '#9ca6af',\n      backgroundDeleted: '#dadfe2',\n      colorDeleted: '#757e85',\n      colorUsername: '#9ca6af',\n      colorTimestamp: '#828c94',\n      backgroundDate: '#e5effa',\n      colorDate: '#505a62',\n      backgroundSystem: '#e5effa',\n      colorSystem: '#505a62',\n      backgroundMedia: 'rgba(0, 0, 0, 0.15)',\n      backgroundReply: 'rgba(0, 0, 0, 0.08)',\n      colorReplyUsername: '#0a0a0a',\n      colorReply: '#6e6e6e',\n      colorTag: '#0d579c',\n      backgroundImage: '#ddd',\n      colorNewMessages: '#34b7f1',\n      backgroundScrollCounter: '#0696c7',\n      colorScrollCounter: '#fff',\n      backgroundReaction: '#eee',\n      borderStyleReaction: '1px solid #eee',\n      backgroundReactionHover: '#fff',\n      borderStyleReactionHover: '1px solid #ddd',\n      colorReactionCounter: '#0a0a0a',\n      backgroundReactionMe: '#cfecf5',\n      borderStyleReactionMe: '1px solid #3b98b8',\n      backgroundReactionHoverMe: '#cfecf5',\n      borderStyleReactionHoverMe: '1px solid #3b98b8',\n      colorReactionCounterMe: '#0b59b3',\n      backgroundAudioRecord: '#eb4034',\n      backgroundAudioLine: 'rgba(0, 0, 0, 0.15)',\n      backgroundAudioProgress: '#455247',\n      backgroundAudioProgressSelector: '#455247',\n      colorFileExtension: '#757e85'\n    },\n\n    markdown: {\n      background: 'rgba(239, 239, 239, 0.7)',\n      border: 'rgba(212, 212, 212, 0.9)',\n      color: '#e01e5a',\n      colorMulti: '#0a0a0a'\n    },\n\n    room: {\n      colorUsername: '#0a0a0a',\n      colorMessage: '#67717a',\n      colorTimestamp: '#a2aeb8',\n      colorStateOnline: '#4caf50',\n      colorStateOffline: '#9ca6af',\n      backgroundCounterBadge: '#34b7f1',\n      colorCounterBadge: '#fff'\n    },\n\n    emoji: {\n      background: '#fff'\n    },\n\n    icons: {\n      search: '#9ca6af',\n      add: '#1976d2',\n      toggle: '#0a0a0a',\n      menu: '#0a0a0a',\n      close: '#9ca6af',\n      closeImage: '#fff',\n      file: '#1976d2',\n      paperclip: '#1976d2',\n      closeOutline: '#000',\n      send: '#1976d2',\n      sendDisabled: '#9ca6af',\n      emoji: '#1976d2',\n      emojiReaction: 'rgba(0, 0, 0, 0.3)',\n      document: '#1976d2',\n      pencil: '#9e9e9e',\n      checkmark: '#9e9e9e',\n      checkmarkSeen: '#0696c7',\n      eye: '#fff',\n      dropdownMessage: '#fff',\n      dropdownMessageBackground: 'rgba(0, 0, 0, 0.25)',\n      dropdownRoom: '#9e9e9e',\n      dropdownScroll: '#0a0a0a',\n      microphone: '#1976d2',\n      audioPlay: '#455247',\n      audioPause: '#455247',\n      audioCancel: '#eb4034',\n      audioConfirm: '#1ba65b'\n    }\n  },\n  dark: {\n    general: {\n      color: '#fff',\n      backgroundInput: '#202223',\n      colorPlaceholder: '#596269',\n      colorCaret: '#fff',\n      colorSpinner: '#fff',\n      borderStyle: 'none',\n      backgroundScrollIcon: '#fff'\n    },\n\n    container: {\n      border: 'none',\n      borderRadius: '4px',\n      boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)'\n    },\n\n    header: {\n      background: '#181a1b',\n      colorRoomName: '#fff',\n      colorRoomInfo: '#9ca6af'\n    },\n\n    footer: {\n      background: '#131415',\n      borderStyleInput: 'none',\n      borderInputSelected: '#1976d2',\n      backgroundReply: '#1b1c1c',\n      backgroundTagActive: '#1b1c1c',\n      backgroundTag: '#131415'\n    },\n\n    content: {\n      background: '#131415'\n    },\n\n    sidemenu: {\n      background: '#181a1b',\n      backgroundHover: '#202224',\n      backgroundActive: '#151617',\n      colorActive: '#fff',\n      borderColorSearch: '#181a1b'\n    },\n\n    dropdown: {\n      background: '#2a2c33',\n      backgroundHover: '#26282e'\n    },\n\n    message: {\n      background: '#22242a',\n      backgroundMe: '#1f7e80',\n      color: '#fff',\n      colorStarted: '#9ca6af',\n      backgroundDeleted: '#1b1c21',\n      colorDeleted: '#a2a5a8',\n      colorUsername: '#b3bac9',\n      colorTimestamp: '#ebedf2',\n      backgroundDate: 'rgba(0, 0, 0, 0.3)',\n      colorDate: '#bec5cc',\n      backgroundSystem: 'rgba(0, 0, 0, 0.3)',\n      colorSystem: '#bec5cc',\n      backgroundMedia: 'rgba(0, 0, 0, 0.18)',\n      backgroundReply: 'rgba(0, 0, 0, 0.18)',\n      colorReplyUsername: '#fff',\n      colorReply: '#d6d6d6',\n      colorTag: '#f0c60a',\n      backgroundImage: '#ddd',\n      colorNewMessages: '#fff',\n      backgroundScrollCounter: '#1976d2',\n      colorScrollCounter: '#fff',\n      backgroundReaction: 'none',\n      borderStyleReaction: 'none',\n      backgroundReactionHover: '#202223',\n      borderStyleReactionHover: 'none',\n      colorReactionCounter: '#fff',\n      backgroundReactionMe: '#4e9ad1',\n      borderStyleReactionMe: 'none',\n      backgroundReactionHoverMe: '#4e9ad1',\n      borderStyleReactionHoverMe: 'none',\n      colorReactionCounterMe: '#fff',\n      backgroundAudioRecord: '#eb4034',\n      backgroundAudioLine: 'rgba(255, 255, 255, 0.15)',\n      backgroundAudioProgress: '#b7d4d3',\n      backgroundAudioProgressSelector: '#b7d4d3',\n      colorFileExtension: '#a2a5a8'\n    },\n\n    markdown: {\n      background: 'rgba(239, 239, 239, 0.7)',\n      border: 'rgba(212, 212, 212, 0.9)',\n      color: '#e01e5a',\n      colorMulti: '#0a0a0a'\n    },\n\n    room: {\n      colorUsername: '#fff',\n      colorMessage: '#6c7278',\n      colorTimestamp: '#6c7278',\n      colorStateOnline: '#4caf50',\n      colorStateOffline: '#596269',\n      backgroundCounterBadge: '#1976d2',\n      colorCounterBadge: '#fff'\n    },\n\n    emoji: {\n      background: '#343740'\n    },\n\n    icons: {\n      search: '#596269',\n      add: '#fff',\n      toggle: '#fff',\n      menu: '#fff',\n      close: '#9ca6af',\n      closeImage: '#fff',\n      file: '#1976d2',\n      paperclip: '#fff',\n      closeOutline: '#fff',\n      send: '#fff',\n      sendDisabled: '#646a70',\n      emoji: '#fff',\n      emojiReaction: '#fff',\n      document: '#1976d2',\n      pencil: '#ebedf2',\n      checkmark: '#ebedf2',\n      checkmarkSeen: '#f0d90a',\n      eye: '#fff',\n      dropdownMessage: '#fff',\n      dropdownMessageBackground: 'rgba(0, 0, 0, 0.25)',\n      dropdownRoom: '#fff',\n      dropdownScroll: '#0a0a0a',\n      microphone: '#fff',\n      audioPlay: '#b7d4d3',\n      audioPause: '#b7d4d3',\n      audioCancel: '#eb4034',\n      audioConfirm: '#1ba65b'\n    }\n  }\n}\n\nexport const cssThemeVars = ({\n  general,\n  container,\n  header,\n  footer,\n  sidemenu,\n  content,\n  dropdown,\n  message,\n  markdown,\n  room,\n  emoji,\n  icons\n}) => {\n  return {\n    // general\n    '--chat-color': general.color,\n    '--chat-bg-color-input': general.backgroundInput,\n    '--chat-color-spinner': general.colorSpinner,\n    '--chat-color-placeholder': general.colorPlaceholder,\n    '--chat-color-caret': general.colorCaret,\n    '--chat-border-style': general.borderStyle,\n    '--chat-bg-scroll-icon': general.backgroundScrollIcon,\n\n    // container\n    '--chat-container-border': container.border,\n    '--chat-container-border-radius': container.borderRadius,\n    '--chat-container-box-shadow': container.boxShadow,\n\n    // header\n    '--chat-header-bg-color': header.background,\n    '--chat-header-color-name': header.colorRoomName,\n    '--chat-header-color-info': header.colorRoomInfo,\n\n    // footer\n    '--chat-footer-bg-color': footer.background,\n    '--chat-border-style-input': footer.borderStyleInput,\n    '--chat-border-color-input-selected': footer.borderInputSelected,\n    '--chat-footer-bg-color-reply': footer.backgroundReply,\n    '--chat-footer-bg-color-tag-active': footer.backgroundTagActive,\n    '--chat-footer-bg-color-tag': footer.backgroundTag,\n\n    // content\n    '--chat-content-bg-color': content.background,\n\n    // sidemenu\n    '--chat-sidemenu-bg-color': sidemenu.background,\n    '--chat-sidemenu-bg-color-hover': sidemenu.backgroundHover,\n    '--chat-sidemenu-bg-color-active': sidemenu.backgroundActive,\n    '--chat-sidemenu-color-active': sidemenu.colorActive,\n    '--chat-sidemenu-border-color-search': sidemenu.borderColorSearch,\n\n    // dropdown\n    '--chat-dropdown-bg-color': dropdown.background,\n    '--chat-dropdown-bg-color-hover': dropdown.backgroundHover,\n\n    // message\n    '--chat-message-bg-color': message.background,\n    '--chat-message-bg-color-me': message.backgroundMe,\n    '--chat-message-color-started': message.colorStarted,\n    '--chat-message-bg-color-deleted': message.backgroundDeleted,\n    '--chat-message-color-deleted': message.colorDeleted,\n    '--chat-message-color-username': message.colorUsername,\n    '--chat-message-color-timestamp': message.colorTimestamp,\n    '--chat-message-bg-color-date': message.backgroundDate,\n    '--chat-message-color-date': message.colorDate,\n    '--chat-message-bg-color-system': message.backgroundSystem,\n    '--chat-message-color-system': message.colorSystem,\n    '--chat-message-color': message.color,\n    '--chat-message-bg-color-media': message.backgroundMedia,\n    '--chat-message-bg-color-reply': message.backgroundReply,\n    '--chat-message-color-reply-username': message.colorReplyUsername,\n    '--chat-message-color-reply-content': message.colorReply,\n    '--chat-message-color-tag': message.colorTag,\n    '--chat-message-bg-color-image': message.backgroundImage,\n    '--chat-message-color-new-messages': message.colorNewMessages,\n    '--chat-message-bg-color-scroll-counter': message.backgroundScrollCounter,\n    '--chat-message-color-scroll-counter': message.colorScrollCounter,\n    '--chat-message-bg-color-reaction': message.backgroundReaction,\n    '--chat-message-border-style-reaction': message.borderStyleReaction,\n    '--chat-message-bg-color-reaction-hover': message.backgroundReactionHover,\n    '--chat-message-border-style-reaction-hover': message.borderStyleReactionHover,\n    '--chat-message-color-reaction-counter': message.colorReactionCounter,\n    '--chat-message-bg-color-reaction-me': message.backgroundReactionMe,\n    '--chat-message-border-style-reaction-me': message.borderStyleReactionMe,\n    '--chat-message-bg-color-reaction-hover-me': message.backgroundReactionHoverMe,\n    '--chat-message-border-style-reaction-hover-me': message.borderStyleReactionHoverMe,\n    '--chat-message-color-reaction-counter-me': message.colorReactionCounterMe,\n    '--chat-message-bg-color-audio-record': message.backgroundAudioRecord,\n    '--chat-message-bg-color-audio-line': message.backgroundAudioLine,\n    '--chat-message-bg-color-audio-progress': message.backgroundAudioProgress,\n    '--chat-message-bg-color-audio-progress-selector': message.backgroundAudioProgressSelector,\n    '--chat-message-color-file-extension': message.colorFileExtension,\n\n    // markdown\n    '--chat-markdown-bg': markdown.background,\n    '--chat-markdown-border': markdown.border,\n    '--chat-markdown-color': markdown.color,\n    '--chat-markdown-color-multi': markdown.colorMulti,\n\n    // room\n    '--chat-room-color-username': room.colorUsername,\n    '--chat-room-color-message': room.colorMessage,\n    '--chat-room-color-timestamp': room.colorTimestamp,\n    '--chat-room-color-online': room.colorStateOnline,\n    '--chat-room-color-offline': room.colorStateOffline,\n    '--chat-room-bg-color-badge': room.backgroundCounterBadge,\n    '--chat-room-color-badge': room.colorCounterBadge,\n\n    // emoji\n    '--chat-emoji-bg-color': emoji.background,\n\n    // icons\n    '--chat-icon-color-search': icons.search,\n    '--chat-icon-color-add': icons.add,\n    '--chat-icon-color-toggle': icons.toggle,\n    '--chat-icon-color-menu': icons.menu,\n    '--chat-icon-color-close': icons.close,\n    '--chat-icon-color-close-image': icons.closeImage,\n    '--chat-icon-color-file': icons.file,\n    '--chat-icon-color-paperclip': icons.paperclip,\n    '--chat-icon-color-close-outline': icons.closeOutline,\n    '--chat-icon-color-send': icons.send,\n    '--chat-icon-color-send-disabled': icons.sendDisabled,\n    '--chat-icon-color-emoji': icons.emoji,\n    '--chat-icon-color-emoji-reaction': icons.emojiReaction,\n    '--chat-icon-color-document': icons.document,\n    '--chat-icon-color-pencil': icons.pencil,\n    '--chat-icon-color-checkmark': icons.checkmark,\n    '--chat-icon-color-checkmark-seen': icons.checkmarkSeen,\n    '--chat-icon-color-eye': icons.eye,\n    '--chat-icon-color-dropdown-message': icons.dropdownMessage,\n    '--chat-icon-bg-dropdown-message': icons.dropdownMessageBackground,\n    '--chat-icon-color-dropdown-room': icons.dropdownRoom,\n    '--chat-icon-color-dropdown-scroll': icons.dropdownScroll,\n    '--chat-icon-color-microphone': icons.microphone,\n    '--chat-icon-color-audio-play': icons.audioPlay,\n    '--chat-icon-color-audio-pause': icons.audioPause,\n    '--chat-icon-color-audio-cancel': icons.audioCancel,\n    '--chat-icon-color-audio-confirm': icons.audioConfirm\n  }\n}\n", "<template>\n  <div class=\"mw-100\">\n    <div class=\"vac-card-window\" :style=\"[cssVars]\">\n      <div class=\"vac-chat-container\" :class=\"{ 'profile-visible': showProfile }\">\n        <room :current-user-id=\"currentUserId\" :rooms=\"rooms\" :room-id=\"room.roomId || ''\"\n          :load-first-room=\"loadFirstRoom\" :messages=\"messages\" :room-message=\"roomMessage\"\n          :messages-loaded=\"messagesLoaded\" :menu-actions=\"menuActions\" :message-actions=\"messageActions\"\n          :show-send-icon=\"showSendIcon\" :show-files=\"showFiles\" :show-audio=\"showAudio\" :audio-bit-rate=\"audioBitRate\"\n          :audio-sample-rate=\"audioSampleRate\" :show-emojis=\"showEmojis\" :show-reaction-emojis=\"showReactionEmojis\"\n          :show-new-messages-divider=\"showNewMessagesDivider\" :show-footer=\"showFooter\" :text-messages=\"t\"\n          :single-room=\"singleRoom\" :show-rooms-list=\"showRoomsList\" :text-formatting=\"textFormatting\"\n          :link-options=\"linkOptions\" :is-mobile=\"false\" :loading-rooms=\"loadingRooms\"\n          :textarea-action-enabled=\"textareaActionEnabled\" :accepted-files=\"acceptedFiles\"\n          :templates-text=\"templatesText\" :templates=\"templates\" :isMsgFetched=\"isMsgFetched\"\n          :unread-counts=\"unreadCounts\" :messageInTransit=\"messageInTransit\" @toggle-error-modal=\"toggleErrorModal\"\n          @fetch-messages=\"fetchMessages\" @send-message=\"sendMessage\" @edit-message=\"editMessage\"\n          @delete-message=\"deleteMessage\" @open-file=\"openFile\" @send-message-reaction=\"sendMessageReaction\"\n          @typing-message=\"typingMessage\" @textarea-action-handler=\"textareaActionHandler\"\n          @carousel-handler=\"carouselHandler\" @toggle-menu-bar=\"$emit('toggle-menu-bar')\"\n          @add-template-msg=\"forwardTemplateMsg\" @redirect-to-hubspot=\"$emit('redirect-to-hubspot', room)\">\n          <template v-for=\"(i, name) in $scopedSlots\" #[name]=\"data\">\n            <slot :name=\"name\" v-bind=\"data\" />\n          </template>\n        </room>\n      </div>\n    </div>\n    <app-carousel :show=\"showCarousel\" :close=\"closeCarousel\" :images=\"carouselData\" />\n\n    <error-modal :show=\"showErrorModal\" :toggle=\"toggleErrorModal\" :error-message=\"this.errorMessage\" />\n    <success-modal :show=\"showSuccessModal\" :toggle=\"toggleSuccessModal\" :success-message=\"successMessage\" />\n    <image-viewer :show=\"previewImage\" :msg=\"previewMessage\" :close=\"closeImageViewer\" />\n  </div>\n</template>\n\n<script>\nimport AppCarousel from '../components/Carousel/Carousel'\nimport ErrorModal from '../components/ErrorModal/ErrorModal'\nimport SuccessModal from '../components/SuccessModal/SuccessModal'\nimport ImageViewer from '../components/ImageViewer/ImageViewer'\nimport axios from 'axios'\nimport Room from './Room/Room'\nimport locales from '../locales'\nimport { defaultThemeStyles, cssThemeVars } from '../themes'\nconst { roomsValidation, partcipantsValidation } = require('../utils/data-validation')\n\nexport default {\n  name: 'ChatContainer',\n  components: {\n    Room,\n    AppCarousel,\n    ErrorModal,\n    SuccessModal,\n    ImageViewer,\n  },\n\n  props: {\n    userData: { type: Object, required: true },\n    theme: { type: String, default: 'light' },\n    styles: { type: Object, default: () => ({}) },\n    singleRoom: { type: Boolean, default: false },\n    roomsListOpened: { type: Boolean, default: true },\n    textMessages: { type: Object, default: null },\n    currentUserId: { type: [String, Number], default: '' },\n    rooms: { type: Array, default: () => [] },\n    loadingRooms: { type: Boolean, default: false },\n    roomsLoaded: { type: Boolean, default: false },\n    engagement: { type: Object, default: null },\n    participants: { type: Object, default: null },\n    loadingTab: { type: Boolean, required: false },\n    showAddToHubspot: { type: Boolean, required: false },\n    setContactObjects: { type: Boolean, required: false },\n    showParticipants: { type: Boolean, required: true },\n    roomId: { type: [String, Number], default: null },\n    loadFirstRoom: { type: Boolean, default: true },\n    isMsgFetched: { type: Boolean, default: true },\n    messages: { type: Array, default: () => [] },\n    messagesLoaded: { type: Boolean, default: false },\n    roomActions: { type: Array, default: () => [] },\n    menuActions: { type: Array, default: () => [] },\n    messageActions: {\n      type: Array,\n      default: () => [\n        { name: 'replyMessage', title: 'Reply' }\n\n      ]\n    },\n    showSearch: { type: Boolean, default: true },\n    showAddRoom: { type: Boolean, default: true },\n    showSendIcon: { type: Boolean, default: true },\n    showFiles: { type: Boolean, default: true },\n    showAudio: { type: Boolean, default: true },\n    audioBitRate: { type: Number, default: 128 },\n    audioSampleRate: { type: Number, default: 44100 },\n    showEmojis: { type: Boolean, default: true },\n    showReactionEmojis: { type: Boolean, default: true },\n    showNewMessagesDivider: { type: Boolean, default: true },\n    showFooter: { type: Boolean, default: true },\n    textFormatting: { type: Boolean, default: true },\n    linkOptions: {\n      type: Object,\n      default: () => ({ disabled: false, target: '_blank', rel: null })\n    },\n    textareaActionEnabled: { type: Boolean, default: false },\n    roomMessage: { type: String, default: '' },\n    acceptedFiles: { type: String, default: '*' },\n    templatesText: { type: Array, default: null },\n    selectedRoom: { type: Object, default: () => { } },\n    labels: { type: Array, required: true },\n    showLabels: { type: Boolean, required: true },\n    roomLabels: { type: Array, required: true },\n    templates: { type: Array, required: true },\n    assigningLabel: { type: Boolean, required: true },\n    toggleErrorModal: { type: Function, required: true },\n    toggleSuccessModal: { type: Function, required: true },\n    unreadCounts: { type: Object, required: true },\n    sidebarVisible: { type: Boolean, required: true },\n    showErrorModal: { type: Boolean, required: true },\n    requests: { type: Array, required: true },\n    request: { type: Object, default: () => { } },\n    showCreateModal: { type: Boolean, required: false },\n    showSuccessModal: { type: Boolean, required: true },\n    saveKey: { type: Boolean, required: false },\n    messageInTransit: { type: Boolean, required: true },\n    errorMessage: { type: String, default: '' },\n    successMessage: {\n      type: Object,\n      default: () => ({ heading: 'Success', content: 'Successful!' })\n    }\n  },\n\n  emits: [\n    'toggle-rooms-list',\n    'fetch-messages',\n    'send-message',\n    'edit-message',\n    'delete-message',\n    'open-file',\n    'message-action-handler',\n    'send-message-reaction',\n    'typing-message',\n    'textarea-action-handler',\n    'add-room',\n    'room-action-handler',\n    'chat-label-handler',\n    'toggle-menu-bar',\n    'toggle-labels-modal',\n    'close-sidebar',\n    'redirect-to-hubspot',\n  ],\n\n  data() {\n    return {\n      room: this.selectedRoom || {},\n      carouselData: [],\n      forwardMessage: {},\n      loadingMoreRooms: false,\n      showRoomsList: true,\n      isMobile: false,\n      showCarousel: false,\n      showProfile: false,\n      previewImage: false,\n      previewMessage: {},\n      startchat: false,\n      // errorMessage: 'Something went wrong!',\n    }\n  },\n\n  computed: {\n    t() {\n      return {\n        ...locales,\n        ...this.textMessages\n      }\n    },\n    // Default style\n    cssVars() {\n      const defaultStyles = defaultThemeStyles[this.theme]\n      const customStyles = {}\n\n      Object.keys(defaultStyles).map(key => {\n        customStyles[key] = {\n          ...defaultStyles[key],\n          ...(this.styles[key] || {})\n        }\n      })\n\n      return cssThemeVars(customStyles)\n    },\n  },\n\n  watch: {\n    rooms: {\n      immediate: true,\n      deep: true,\n      handler(newVal, oldVal) {\n        if (!newVal[0] || !newVal.find(room => room.roomId === this.room.roomId)) {\n          this.showRoomsList = true\n        }\n\n        if (!this.loadingMoreRooms && this.loadFirstRoom && newVal[0] && (!oldVal || newVal.length !== oldVal.length)) {\n          if (this.roomId) {\n            const room = newVal.find(r => r.roomId === this.roomId) || {}\n            this.fetchRoom({ room })\n            // added this\n          } else if (this.room && Object.keys(this.room).length !== 0) {\n            this.fetchRoom({ room: this.room })\n          } else {\n            this.showRoomsList = true\n          }\n        }\n      }\n    },\n\n    loadingRooms(val) {\n      if (val) this.room = {}\n    },\n\n    roomId: {\n      immediate: true,\n      handler(newVal, oldVal) {\n        if (newVal && !this.loadingRooms && this.rooms.length) {\n          const room = this.rooms.find(r => r.roomId === newVal)\n          this.fetchRoom({ room })\n        } else if (oldVal && !newVal) {\n          this.room = {}\n        }\n      }\n    },\n\n    room(val) {\n      if (!val || Object.entries(val).length === 0) return\n\n      roomsValidation(val)\n\n      val.users.forEach(user => {\n        partcipantsValidation(user)\n      })\n    },\n\n    roomsListOpened(val) {\n      this.showRoomsList = val\n    },\n\n    sidebarVisible(val) {\n      if (val && this.showProfile) {\n        this.showProfile = false\n      }\n    }\n  },\n\n  created() { \n    this.fetchMessages()\n  },\n\n  methods: {\n    closeImageViewer() {\n      this.previewImage = false\n      this.previewMessage = {}\n    },\n\n    forwardTemplateMsg(newItem) {\n      this.$emit('add-template-msg', newItem); // Forward template  msg to the parent\n    },\n\n    fetchMessages(options) {\n      const axiosSource = axios.CancelToken.source()\n      this.$emit('fetch-messages', {\n        room: this.room,\n        options,\n        source: axiosSource\n      })\n    },\n\n    sendMessage(message) {\n      this.$emit('send-message', {\n        ...message,\n        roomId: this.room.roomId,\n        phone: this.room.phone\n      })\n    },\n\n    // Currently not include this functionality\n    editMessage(message) {\n      this.$emit('edit-message', { ...message, roomId: this.room.roomId })\n    },\n\n    // Currently not include this functionality\n    deleteMessage(message) {\n      this.$emit('delete-message', { message, roomId: this.room.roomId })\n    },\n\n    openFile({ message, file }) {\n      if (file.action === 'preview') {\n        this.previewImage = true\n        this.previewMessage = file.file\n        return\n      }\n      this.$emit('open-file', { message, file })\n    },\n\n    roomActionHandler({ action, did }) {\n      this.$emit('room-action-handler', {\n        action,\n        did\n      })\n    },\n\n    sendMessageReaction(messageReaction) {\n      this.$emit('send-message-reaction', {\n        ...messageReaction,\n        roomId: this.room.roomId\n      })\n    },\n    typingMessage(message) {\n      this.$emit('typing-message', {\n        message,\n        roomId: this.room.roomId\n      })\n    },\n    textareaActionHandler(message) {\n      this.$emit('textarea-action-handler', {\n        message,\n        roomId: this.room.roomId\n      })\n    },\n    carouselHandler(data) {\n      const images = data.map(el => ({\n        id: el.name,\n        name: el.name + '.' + el.extension,\n        big: el.url,\n        thumb: el.url,\n        timestamp: el.timestamp,\n        date: el.date,\n        username: el.username,\n        msg_id: el._id\n      }))\n      this.carouselData = images\n      this.showCarousel = true\n    },\n    closeCarousel() {\n      this.showCarousel = false\n    },\n\n    toggleProfile() {\n      this.showProfile = !this.showProfile\n    },\n    startChat() {\n      this.startchat = !this.startchat\n    },\n    closeChat() {\n      this.startchat = !this.startchat\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n@import '../styles/index.scss';\n</style>\n", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./ChatWindow.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./ChatWindow.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ChatWindow.vue?vue&type=template&id=2a32cc33\"\nimport script from \"./ChatWindow.vue?vue&type=script&lang=js\"\nexport * from \"./ChatWindow.vue?vue&type=script&lang=js\"\nimport style0 from \"./ChatWindow.vue?vue&type=style&index=0&id=2a32cc33&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"app-spinner\",class:{ 'on-page': _vm.onPage }},[_vm._m(0)])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"loader-inner ball-pulse\"},[_c('div'),_c('div'),_c('div')])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"app-spinner\" :class=\"{ 'on-page': onPage }\">\n    <div class=\"loader-inner ball-pulse\">\n      <div />\n      <div />\n      <div />\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Spinner',\n  props: ['onPage']\n}\n</script>\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Spinner.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Spinner.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Spinner.vue?vue&type=template&id=53a993fc\"\nimport script from \"./Spinner.vue?vue&type=script&lang=js\"\nexport * from \"./Spinner.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "// convert ms to 12hr format\nexport const getTimestamp = date => {\n  let time = new Date(date * 1000)\n  return time.toLocaleString('en-US', {\n    hour: 'numeric',\n    minute: 'numeric',\n    hour12: true\n  })\n}\n\nexport const convertDate = date => {\n  function join(t, a, s) {\n    function format(m) {\n      let f = new Intl.DateTimeFormat('en', m)\n      return f.format(t)\n    }\n    return a.map(format).join(s)\n  }\n\n  let a = [{ day: '2-digit' }, { month: 'short' }, { year: 'numeric' }]\n  return join(new Date(date), a, '-')\n}\n\nexport const randomId = () =>\n  Math.random()\n    .toString(36)\n    .replace(/[^a-z]+/g, '')\n    .substr(2, 10)\n\nexport const isImageFile = file => {\n  const IMAGE_TYPES = ['png', 'jpg', 'jpeg', 'webp', 'svg', 'gif']\n  if (!file || !file.extension) return\n  return IMAGE_TYPES.some(t => file.extension.toLowerCase().includes(t))\n}\n", "export const parseTimestamp = (timestamp, format = '') => {\n  if (!timestamp) return\n\n  const date = new Date(timestamp * 1000)\n\n  if (format === 'HH:mm') {\n    return `${zeroPad(date.getHours(), 2)}:${zeroPad(date.getMinutes(), 2)}`\n  } else if (format === 'DD MMMM YYYY') {\n    const options = { month: 'long', year: 'numeric', day: 'numeric' }\n    return `${new Intl.DateTimeFormat('en-GB', options).format(date)}`\n  } else if (format === 'DD/MM/YY') {\n    const options = { month: 'numeric', year: 'numeric', day: 'numeric' }\n    return `${new Intl.DateTimeFormat('en-GB', options).format(date)}`\n  } else if (format === 'DD MMMM, HH:mm') {\n    const options = { month: 'long', day: 'numeric' }\n    return `${new Intl.DateTimeFormat('en-GB', options).format(date)}, ${zeroPad(date.getHours(), 2)}:${zeroPad(\n      date.getMinutes(),\n      2\n    )}`\n  }\n\n  return date\n}\n\nconst zeroPad = (num, pad) => {\n  return String(num).padStart(pad, '0')\n}\n\nexport const isSameDay = (d1, d2) => {\n  return d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth() && d1.getDate() === d2.getDate()\n}\n\n// format date to : dd MMM yyyy\nexport const formatDate = time => {\n  if (time) {\n    return new Date(time).toLocaleDateString('en-GB', {\n      day: 'numeric',\n      month: 'short',\n      year: 'numeric'\n    })\n  } else {\n    return new Date().toLocaleDateString('en-GB', {\n      day: 'numeric',\n      month: 'short',\n      year: 'numeric'\n    })\n  }\n}\n", "<template>\n  <div class=\"activities-main-div\">\n    <spinner v-if=\"!dataLoaded\" />\n    <chat-window v-else-if=\"noNeedToPermission || (isAdmin || isPermission)\" :user-data=\"userData\" :current-user-id=\"currentUserId\" :rooms=\"rooms\" :engagement=\"engagement\"\n      :participants=\"participants\" :loadingTab=\"loadingTab\" :showAddToHubspot=\"showAddToHubspot\"\n      :showParticipants=\"showParticipants\" :setContactObjects=\"setContactObjects\" :rooms-loaded=\"roomsLoaded\"\n      :messages=\"messages\" :messages-loaded=\"messagesLoaded\" :selected-room=\"selectedRoom\" :requests=\"requests\"\n      :request=\"request\" :labels=\"labels\" :show-labels=\"showLabels\" :room-labels=\"roomLabels\"\n      :assigning-label=\"assigningLabel\" :templates=\"templates\" :error-message=\"errorMessage\"\n      :success-message=\"successMessage\" :save-key=\"saveKey\" :show-error-modal=\"showErrorModal\"\n      :show-create-modal=\"showCreateModal\" :show-success-modal=\"showSuccessModal\" :toggle-error-modal=\"toggleErrorModal\"\n      :toggle-success-modal=\"toggleSuccessModal\" :loading-rooms=\"loadingRooms\" :unread-counts=\"unreadCounts\"\n      :messageInTransit=\"messageInTransit\" :sidebar-visible=\"menuBarShow\" :isMsgFetched=\"isMsgFetched\"\n      @room-action-handler=\"roomActionHandler\" @send-message=\"sendMessage\" @fetch-messages=\"fetchMessages\"\n      @open-file=\"openFile\" @toggle-labels-modal=\"toggleLabelsModal\" @close-sidebar=\"closeSideBar\"\n      @redirect-to-hubspot=\"redirectToHubspot\" @add-template-msg=\"addTemplateMsg\" />\n\n      <Info v-else infoMsg=\"You don't have chat access, Please ask admin for access.\"/>\n  </div>\n</template>\n\n<style lang=\"scss\">\n.activities-main-div {\n  height: 100%;\n\n  .app-spinner {\n    height: 100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n}\n\n.vac-message__media-loader {\n  position: absolute;\n  top: 38%;\n  left: 40%;\n  transform: translate(-50% -50%);\n  background-color: rgba(235, 234, 234, 0.774);\n  border-radius: 50%;\n  padding: 0.6rem;\n\n  .vac-message__progress-bar {\n    border-radius: 50%;\n    background: conic-gradient(green 3.6deg, gray 0deg);\n    padding: 0.5rem;\n\n    & div {\n      background-color: rgb(235, 234, 234);\n      border-radius: 50%;\n    }\n  }\n}\n</style>\n\n<script>\nimport { mapState, mapMutations } from 'vuex'\nimport ChatWindow from '../vuechat/lib/ChatWindow.vue'\nimport Pusher from 'pusher-js'\nimport axios from '@/utils/api.js'\nimport config from '@/utils/config.js'\nimport Spinner from '@/components/Spinner'\nimport Avatar from '@/assets/avatar.png'\nimport { getTimestamp, randomId, isImageFile } from '@/utils/utils.js'\nimport { parseTimestamp, isSameDay, formatDate } from '@/utils/dates'\nimport Info from '../vuechat/components/InfoModal/Info.vue'\n\nexport default {\n  name: 'Home',\n\n  components: {\n    ChatWindow,\n    Spinner,\n    Info\n  },\n\n  data() {\n    return {\n      saveKey: false,\n      requests: [],\n      request: null,\n      showErrorModal: false,\n      showSuccessModal: false,\n      assigningLabel: false,\n      showCreateModal: false,\n      showAddToHubspot: false,\n      showParticipants: true,\n      setContactObjects: false,\n      avatar: Avatar,\n      templates: [],\n      labels: [],\n      engagement: {},\n      participants: {},\n      loadingTab: false,\n      roomLabels: [],\n      showLabels: false,\n      selectedRoom: {},\n      roomsLoaded: false,\n      loadingRooms: true,\n      rooms: [],\n      messages: [],\n      dataLoaded: false,\n      currentUserId: '',\n      currentChatId: null,\n      messagesLoaded: false,\n      user_id: null,\n      username: null,\n      portal_id: null,\n      accountPhone: null,\n      origin: '',\n      errorMessagwhatshive_logoe: '',\n      successMessage: {},\n      messageInTransit: false,\n      isMsgFetched: false,\n      errorMessage: '',\n      fetchMsgData: [],\n      userId: null,\n      objectId: null,\n      phone: null,\n      isAdmin: false,\n      isPermission: false,\n      loader: true,\n      noNeedToPermission: false,\n    }\n  },\n\n  computed: {\n    ...mapState(['unreadCounts', 'menuBarShow']),\n  },\n\n  created() {\n    const roomId = this.$route.params.id\n    const userData = this.$store.state.userData\n\n    this.userData = { username: userData.accountUser }\n    this.user_id = userData.user_id\n    this.portal_id = userData.portal_id\n    this.accountPhone = userData.accountPhone\n    this.username = userData.accountUser\n    this.origin = userData.origin || 'https://app.hubspot.com'\n\n    var urlString = window.location.href\n    var Parmurl = new URL(urlString)\n    this.userId = Parmurl.searchParams.get('user_id')\n    this.objectId = Parmurl.searchParams.get('objectId') || null\n    this.phone = Parmurl.searchParams.get('phone') || null\n\n    this.fetchPermissions()\n    this.initialRequest(roomId)\n    this.subscribe()\n  },\n\n  methods: {\n    ...mapMutations([\n      'setErrorApp',\n      'setErrorMsgApp',\n      'setUnreadCounts',\n      'toggleMenuBar',\n    ]),\n\n    async fetchPermissions() {\n      this.loader = true\n      try {\n        const urlParams = new URLSearchParams(window.location.search);\n        const accountUser = urlParams.get('accountUser');\n        const userId = urlParams.get('user_id');\n\n        const response = await axios.get(`api/users/${accountUser}?user_id=${userId}`);\n\n        if (response.data.user) {\n          const user = response.data.user;\n          const permissionsCache = user.permissions?.map(permission =>\n            permission.replace(/\\s+/g, '')\n          );\n          if(user.active) {\n            this.isPermission = permissionsCache.includes('Chats');\n          }\n\n          this.isAdmin = user.admin;\n          this.loader = false;\n        } else {\n          this.noNeedToPermission = true;\n          this.loader = false;\n        }\n      } catch (error) {\n        this.loader = false;\n        console.error('An error occurred while fetching permissions:', error);\n      }\n    },\n\n\n    // Ignore\n    subscribe() {\n      const dialogId = `${this.portal_id}.${this.accountPhone}`\n      let pusher = new Pusher('8a63ec74543a8782f031', {\n        forceTLS: true,\n        cluster: 'eu',\n        authEndpoint: `${config.baseURL}api/pusher/auth`,\n        auth: {\n          params: {\n            user_id: this.user_id,\n          },\n        },\n      })\n\n      pusher.connection.bind('connected', () => {\n      })\n\n      const channel = pusher.subscribe('private-' + dialogId)\n      channel.bind('new-message', (payload) => {\n        const msg = payload.message\n        if (msg.fromMe) return\n\n        // Remove '+' from phone and chatId before comparison\n        const phone = this.phone.replace('+', '')\n        const chatId = msg.chatId.replace('+', '')        \n\n        if (phone === chatId) {\n          const message = this.formatMessage(msg, true)\n\n          this.messages.push(message)\n        }\n      })\n      channel.bind('new-status', payload => {\n        \n        const data = payload.status\n\n        // Ensure a return statement for the comparison in findIndex\n        const index = this.messages.findIndex(\n          message => String(message.tmp_id) === String(data.id) || String(message._id) === String(data.id)\n        )\n\n        if (index !== -1) {\n          let newMessage = this.messages[index]\n          if (data.status == 'delivered') {\n            newMessage.distributed = true\n          }\n\n          if (data.status == 'read') {\n            newMessage.distributed = true\n            newMessage.seen = true\n          }\n\n          if (data.status == 'failed') {\n            newMessage.failed = true\n            newMessage.reason = data?.status_reason\n          }\n\n          this.messages[index] = newMessage\n          this.resetCounts()\n        } else {\n          console.log('not found')\n        }\n      })\n    },\n\n    async initialRequest(roomId) {\n      try {\n        const templateReq = axios.get(\n          `api/v1/whatsapp/templates?user_id=${this.user_id}`\n        )\n        const responses = await Promise.all([templateReq])\n        this.dataLoaded = true\n\n        this.templates = responses[0]?.data?.data || [];\n\n        this.setErrorApp(false)\n      } catch (err) {\n        console.log(err)\n        this.setErrorApp(true)\n      }\n    },\n\n    formatTimestamp(timestamp) {\n      const date = new Date(timestamp * 1000)\n      const timestampFormat = isSameDay(date, new Date()) ? 'HH:mm' : 'DD/MM/YY'\n      const result = parseTimestamp(timestamp, timestampFormat)\n      return timestampFormat === 'HH:mm' ? `Today, ${result}` : result\n    },\n\n    toggleLabelsModal(room) {\n      if (room) {\n        this.roomLabels = room.labels.map((el) => el.id)\n        this.labels = this.labels.map((el) => ({\n          ...el,\n          roomId: room.roomId,\n          selected: this.roomLabels.includes(el.id),\n        }))\n      } else {\n        this.roomLabels = []\n      }\n      this.showLabels = !this.showLabels\n    },\n\n    roomActionHandler({ action, did }) {\n      if (action.name === 'pinChat') {\n        this.handlePin(did)\n      }\n      return\n    },\n\n    async handlePin(did) {\n      const itemIndex = this.rooms.findIndex((el) => el.did === did)\n      const pinned = !this.rooms[itemIndex].pinned\n      try {\n        const req = `api/dialog/${did}?user_id=${this.user_id}`\n        const { data } = await axios.post(req, { pinned })\n        if (data.ok) {\n          this.rooms[itemIndex].pinned = pinned\n        } else {\n          throw new Error()\n        }\n      } catch (err) {\n        this.showErrorModal = true\n        console.log(err)\n      }\n    },\n\n    // Add template msg after successful sent\n    addTemplateMsg(newItem) {\n\n      this.fetchMsgData.unshift(newItem.message);\n\n      this.messages = []\n      let messages = this.fetchMsgData.map((el) => {\n        return this.formatMessage(el)\n      })\n\n      const combinedMessages = this.combineFiles(messages)\n\n      combinedMessages.forEach(msg => {\n        this.messages.unshift(msg)\n      })\n\n      this.resetCounts()\n    },\n\n    async fetchMessages({ room, options = {}, source }) {\n      this.isMsgFetched = true\n      if (options.reset) {\n        this.messages = []\n\n        this.messagesLoaded = false\n      }\n\n      let currentTimeSeconds = Math.round(Date.now() / 1000)\n      const time =\n        this.messages.length > 0 ? this.messages[0].time : currentTimeSeconds\n\n      if (time === currentTimeSeconds) {\n        this.showAddToHubspot = false\n        this.engagement = {}\n      }\n\n      this.request = {\n        cancel: source.cancel,\n        msg: 'Loading...',\n        message: 'Request Cancelled',\n      }\n      const response = await axios\n        .get(`api/chat?user_id=${this.userId}&chatId=${this.phone}`, {\n          cancelToken: source.token,\n        })\n        .catch(this.logResponseErrors, this.isMsgFetche = false)\n\n      if (response) {\n        this.isMsgFetched = false\n        const { data } = response\n        if (data.status === 'ok') {\n          this.fetchMsgData = data.messages\n\n          const elems = document.getElementsByClassName('text__highlight')\n          const elem = elems[0] || null\n          setTimeout(() => {\n            if (elem) {\n              document.getElementById('count').innerHTML = elems.length\n            }\n          }, 350)\n\n          this.clearOldRequest('Success')\n          this.messagesLoaded = true\n\n          if (data.messages.length === 0) {\n            return\n          }\n\n          this.messages = []\n          let messages = this.fetchMsgData.map((el) => {\n            return this.formatMessage(el)\n          })\n\n          const combinedMessages = this.combineFiles(messages)\n\n          combinedMessages.forEach(msg => {\n            this.messages.unshift(msg)\n          })\n\n          this.resetCounts()\n          this.setErrorApp(false)\n        } else {\n          console.log(data)\n\n        }\n      }\n    },\n\n    logResponseErrors(err) {\n      console.log(err)\n      console.log('Request cancelled')\n    },\n\n    clearOldRequest(msg) {\n      this.request.msg = msg\n      this.requests.push(this.request)\n      this.request = null\n    },\n\n    // For combine all messages\n    combineFiles(messages) {\n      const allMessages = []\n      let message = {}\n\n      for (let [i, msg] of messages.entries()) {\n        if (!msg.files) {\n          message = msg\n        } else {\n          const files = [\n            {\n              ...msg.files[0],\n              timestamp: msg.timestamp,\n              distributed: msg.distributed,\n              seen: msg.seen,\n              date: msg.date,\n              username: msg.username,\n              _id: msg._id,\n            },\n          ]\n\n          if (i === 0) {\n            message = { ...msg, files }\n          } else {\n            const lastIndex = allMessages.length - 1\n            const lastFileIndex = allMessages[lastIndex].files?.length - 1\n\n            if (\n              allMessages[lastIndex].files &&\n              msg.fromMe === allMessages[lastIndex].fromMe &&\n              msg.username === allMessages[lastIndex].username &&\n              !allMessages[lastIndex].content &&\n              isImageFile(msg.files[0]) &&\n              isImageFile(allMessages[lastIndex].files[lastFileIndex]) &&\n              !msg.content\n            ) {\n              allMessages[lastIndex].files.unshift(files[0])\n              continue\n            } else {\n              message = { ...msg, files }\n            }\n          }\n        }\n        allMessages.push(message)\n        message = {}\n      }\n\n      return allMessages\n    },\n\n    // inc = incoming message\n    formatMessage(msg, inc = false) {\n      const replyMessage = msg.quotedMsgId\n        ? {\n          content: msg.quotedFiles ? '' : msg.quotedMsgBody,\n          _id: msg.quotedMsgId,\n          username: msg.name || msg.senderName,\n          files: msg.quotedFiles,\n        }\n        : null\n      return {\n        _id: msg.id,\n        content: msg?.caption ? msg.caption : msg.body,\n        username: msg.name ? msg.name || msg.senderName : '',\n        date: formatDate(msg.time * 1000),\n        timestamp: getTimestamp(msg.time),\n        failed: msg.status === 'failed',\n        reason: msg.status_reason,\n        saved: msg.status === 'sent', // single tick\n        distributed: msg.status === 'read' || msg.status === 'delivered', // double tick\n        seen: msg.status === 'read', // blue tick\n        files: msg.files,\n        replyMessage: replyMessage,\n        fromMe: inc ? 0 : msg.fromMe,\n        time: msg.time,\n      }\n    },\n\n    getCounts() {\n      const countInStorage = localStorage.getItem('hw-counts')\n      return countInStorage ? JSON.parse(countInStorage) : {}\n    },\n\n    setCounts(counts) {\n      this.setUnreadCounts(counts)\n      const countsStr = JSON.stringify(counts)\n      localStorage.setItem('hw-counts', countsStr)\n    },\n\n    resetCounts() {\n      // update in localstorage\n      const countInStorage = this.getCounts()\n      const obj = {\n        val: 0,\n        msg_id:\n          countInStorage[this.currentChatId]?.val === 0\n            ? null\n            : countInStorage[this.currentChatId]?.msg_id,\n      }\n      countInStorage[this.currentChatId] = obj\n      this.setCounts(countInStorage)\n    },\n\n    sendMessage(message) {\n      if (message.files) {\n        this.sendFileLocal(message)\n      } else {\n        this.sendText(message)\n      }\n    },\n\n    async sendText(msg) {\n      const { content, replyMessage } = msg\n      try {\n        const tempMsgId = randomId()\n        const currentMsgIndex = this.messages.length\n        const timestamp = getTimestamp(new Date().getTime() / 1000)\n        let url = 'send'\n        this.messages.push({\n          _id: tempMsgId,\n          content: content,\n          username: this.username,\n          date: formatDate(),\n          timestamp,\n          saved: false,\n          fromMe: 1,\n          replyMessage: replyMessage,\n          distributed: 'wait',\n        })\n\n        let reqData = {\n          user_id: this.userId,\n          phone: this.phone,\n          message: content,\n          objectId: this.objectId,\n        }\n\n        if (replyMessage) {\n          reqData = {\n            ...reqData,\n            quotedMsgId: replyMessage._id,\n            quotedMsgBody: replyMessage.content,\n          }\n          url = 'reply'\n        }\n\n        this.messageInTransit = true\n        const { data } = await axios.post(`api/${url}`, reqData)\n        const el = data.savedMessage\n        if (data.ok) {\n          this.messageInTransit = false\n          this.$set(this.messages, currentMsgIndex, {\n            ...this.messages[currentMsgIndex],\n            content: data.sentMessage,\n            username: el.name || el.senderName,\n            date: formatDate(el.time * 1000),\n            timestamp: getTimestamp(el.time),\n            saved: true,\n            fromMe: Number(el.fromMe),\n            replyMessage: replyMessage,\n            distributed: false,\n            tmp_id: el.id\n          })\n        } else {\n          this.showErrorModal = true\n          this.errorMessage = data.message || 'Unable to send message'\n          this.messageInTransit = false\n        }\n      } catch (err) {\n        this.errorMessage = err.response?.data?.message || 'Unable to send message'\n        this.messageInTransit = false\n        this.showErrorModal = true\n        console.log(err)\n      }\n    },\n\n    sendFileLocal(msg) {\n      const timestamp = getTimestamp(new Date().getTime() / 1000)\n\n      msg.files.forEach((m, i) => {\n        const content = i === 0 ? msg.content : ''\n        const newMessage = {\n          _id: randomId(),\n          content: content,\n          username: this.username,\n          date: formatDate(),\n          timestamp,\n          saved: true,\n          fromMe: 1,\n          files: [{ ...m, timestamp, saved: true }],\n        }\n\n        this.messages = [...this.messages, newMessage]\n        this.sendFileApi(content, msg.roomId, newMessage)\n      })\n    },\n\n    // Upload media files\n    async sendFileApi(content, roomId, msg) {\n\n      let formData = new FormData()\n      formData.append('file', msg.files[0].rawData)\n      formData.append('message', content)\n      formData.append('phone', this.phone)\n      formData.append('user_id', this.userId)\n      formData.append('objectId', this.objectId)\n\n      try {\n        const { data } = await axios.post(`api/upload`, formData)\n        if (data.ok) {\n          this.updateFileState(false, msg._id, data)\n        } else {\n          throw new Error()\n        }\n      } catch (err) {\n        this.showErrorModal = true\n        this.errorMessage = 'Unable to send file'\n        this.updateFileState(true, msg._id)\n        this.messages.pop()\n        console.log(err)\n      }\n    },\n\n\n    updateFileState(error, id, data) {\n      const msg = this.messages.find((m) => m._id === id)\n      if (!msg) return\n\n      if (error) {\n        msg.files[0].error = true\n        msg.files[0].loading = false\n      } else {\n        msg.tmp_id = data.messageId\n        msg.files[0].loading = false\n        msg.files[0].url = data.url ? data.url : msg.files[0].url\n        msg._id = data.messageId\n      }\n    },\n\n    openFile({ file }) {\n      window.open(file.file.url, '_blank')\n    },\n\n    toggleErrorModal() {\n      this.showErrorModal = !this.showErrorModal\n      this.errorMessage = ''\n    },\n\n    toggleSuccessModal() {\n      this.showSuccessModal = !this.showSuccessModal\n      this.successMessage = {}\n    },\n\n    closeSideBar() {\n      this.toggleMenuBar()\n    },\n\n    redirectToHubspot(room) {\n      const url = `${this.origin}/contacts/${this.portal_id}/contact/${room.object_id}/`\n      window.open(url)\n    },\n  },\n}\n</script>\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Activities.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Activities.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Activities.vue?vue&type=template&id=fdaf625a\"\nimport script from \"./Activities.vue?vue&type=script&lang=js\"\nexport * from \"./Activities.vue?vue&type=script&lang=js\"\nimport style0 from \"./Activities.vue?vue&type=style&index=0&id=fdaf625a&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\nimport VueRouter from 'vue-router'\nimport Activities from '../views/Activities.vue'\n\nVue.use(VueRouter)\n\nconst routes = [\n  {\n    path: '/',\n    alias: '/activities',\n    name: 'Chats', // Don't change this\n    component: Activities\n  },\n  {\n    path: '/:catchAll(.*)*',\n    redirect: { name: 'Chats' }\n  }\n]\n\nconst router = new VueRouter({\n  // mode: 'history',\n  base: process.env.BASE_URL,\n  routes,\n  linkExactActiveClass: 'active-link'\n})\n\nexport default router\n", "import Vue from 'vue'\nimport Vuex from 'vuex'\n// import axios from '@/utils/api.js';\n\nVue.use(Vuex)\n\nexport default new Vuex.Store({\n  state: {\n    userData: null,\n    menuBarShow: false,\n    loggedOut: false,\n    errorApp: false,\n    errorMsgApp: '',\n    showBanner: false,\n    showConflict: false,\n    bannerContent: null,\n    unreadCounts: {}\n  },\n\n  mutations: {\n    setUserData: (state, data) => {\n      state.userData = data\n    },\n    toggleMenuBar: state => {\n      state.menuBarShow = !state.menuBarShow\n    },\n    logoutUser: state => {\n      state.loggedOut = true\n    },\n    setErrorApp: (state, data) => {\n      state.errorApp = data\n    },\n    setErrorMsgApp: (state, msg) => {\n      state.errorMsgApp = msg\n    },\n    setBanner: (state, data) => {\n      state.showBanner = data\n    },\n    setConflict: (state, data) => {\n      state.showConflict = data\n    },\n    setBannerContent: (state, content) => {\n      state.bannerContent = content\n    },\n    setUnreadCounts: (state, counts) => {\n      state.unreadCounts = counts\n    }\n  },\n\n  actions: {\n    async logout(ctx, user_id) {\n      //\tTODO: Enable API\n      console.log(user_id)\n      // const { data } = await axios.get(`api/logout?user_id=${user_id}`);\n      // if (data.status === 'ok') {\n      ctx.commit('logoutUser')\n      // } else {\n      //   throw new Error();\n      // }\n    }\n  }\n})\n", "import Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\nimport FloatingVue from 'floating-vue'\n\n// import \"vue-advanced-chat/dist/vue-advanced-chat.css\";\n\nVue.config.productionTip = false\nVue.use(FloatingVue)\n\nconst app = new Vue({\n  router,\n  store,\n  render: h => h(App)\n}).$mount('#app')\n", "export const IMAGE_TYPES = ['png', 'jpg', 'jpeg', 'webp', 'svg', 'gif']\nexport const VIDEO_TYPES = ['mp4', 'video/ogg', 'webm', 'quicktime']\nexport const AUDIO_TYPES = ['mp3', 'audio/ogg', 'wav', 'mpeg', 'mpga']\n", "export function roomsValidation(obj) {\n  const roomsValidate = [\n    { key: 'roomId', type: ['string', 'number'] },\n    { key: 'roomName', type: ['string'] },\n    { key: 'users', type: ['array'] }\n  ]\n\n  const validate = (obj, props) => {\n    return props.every(prop => {\n      let validType = false\n\n      if (prop.type[0] === 'array' && Array.isArray(obj[prop.key])) {\n        validType = true\n      } else if (prop.type.find(t => t === typeof obj[prop.key])) {\n        validType = true\n      }\n\n      return validType && checkObjectValid(obj, prop.key)\n    })\n  }\n\n  if (!validate(obj, roomsValidate)) {\n    throw new Error('Rooms object is not valid! Must contain roomId[String, Number], roomName[String] and users[Array]')\n  }\n}\n\nexport function partcipantsValidation(obj) {\n  const participantsValidate = [\n    { key: '_id', type: ['string', 'number'] },\n    { key: 'username', type: ['string'] }\n  ]\n\n  const validate = (obj, props) => {\n    return props.every(prop => {\n      const validType = prop.type.find(t => t === typeof obj[prop.key])\n      return validType && checkObjectValid(obj, prop.key)\n    })\n  }\n\n  if (!validate(obj, participantsValidate)) {\n    throw new Error('Participants object is not valid! Must contain _id[String, Number] and username[String]')\n  }\n}\n\nexport function messagesValidation(obj) {\n  const messagesValidate = [\n    { key: '_id', type: ['string', 'number'] },\n    { key: 'content', type: ['string', 'number'], required: false },\n    { key: 'username', type: ['string', 'number'] }\n  ]\n\n  const validate = (obj, props) => {\n    return props.every(prop => {\n      if (!prop?.required) {\n        return true\n      }\n\n      const validType = prop.type.find(t => t === typeof obj[prop.key])\n      return validType && checkObjectValid(obj, prop.key)\n    })\n  }\n\n  if (!validate(obj, messagesValidate)) {\n    throw new Error(\n      'Messages object is not valid! Must contain _id[String, Number], content[String, Number] and username[String, Number]'\n    )\n  }\n}\n\nfunction checkObjectValid(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key) && obj[key] !== null && obj[key] !== undefined\n}\n", "import { IMAGE_TYPES, VIDEO_TYPES, AUDIO_TYPES } from './constants'\n\nfunction checkMediaType(types, file) {\n  if (!file || !file.extension) return\n  return types.some(t => file.extension.toLowerCase().includes(t))\n}\n\nexport function isImageFile(file) {\n  return checkMediaType(IMAGE_TYPES, file)\n}\n\nexport function isVideoFile(file) {\n  return checkMediaType(VIDEO_TYPES, file)\n}\n\nexport function isImageVideoFile(file) {\n  return checkMediaType(IMAGE_TYPES, file) || checkMediaType(VIDEO_TYPES, file)\n}\n\nexport function isAudioFile(file) {\n  return checkMediaType(AUDIO_TYPES, file)\n}\n\nexport function isPdfFile(file) {\n  return checkMediaType(['pdf'], file)\n}\n", "export function detectMobile() {\n  const userAgent = getUserAgent()\n\n  const userAgentPart = userAgent.substr(0, 4)\n\n  return (\n    /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(\n      userAgent\n    ) ||\n    /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw(n|u)|c55\\/|capi|ccwa|cdm|cell|chtm|cldc|cmd|co(mp|nd)|craw|da(it|ll|ng)|dbte|dcs|devi|dica|dmob|do(c|p)o|ds(12|d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(|_)|g1 u|g560|gene|gf5|gmo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd(m|p|t)|hei|hi(pt|ta)|hp( i|ip)|hsc|ht(c(| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i(20|go|ma)|i230|iac( ||\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|[a-w])|libw|lynx|m1w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|mcr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|([1-8]|c))|phil|pire|pl(ay|uc)|pn2|po(ck|rt|se)|prox|psio|ptg|qaa|qc(07|12|21|32|60|[2-7]|i)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h|oo|p)|sdk\\/|se(c(|0|1)|47|mc|nd|ri)|sgh|shar|sie(|m)|sk0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h|v|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl|tdg|tel(i|m)|tim|tmo|to(pl|sh)|ts(70|m|m3|m5)|tx9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas|your|zeto|zte/i.test(\n      userAgentPart\n    )\n  )\n}\n\nfunction getUserAgent() {\n  const userAgent = navigator.userAgent || navigator.vendor || window.opera || null\n\n  if (!userAgent) throw new Error('Failed to look for user agent information.')\n\n  return userAgent\n}\n\nexport function iOSDevice() {\n  return (\n    ['iPad', 'iPhone', 'iPod'].includes(navigator.platform) ||\n    (navigator.userAgent.includes('Mac') && 'ontouchend' in document)\n  )\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.p = \"/send/\";", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkchat_app\"] = self[\"webpackChunkchat_app\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], function() { return __webpack_require__(6608); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["render", "_vm", "this", "_c", "_self", "userData", "loggedOut", "staticRenderFns", "_m", "staticClass", "_v", "name", "component", "class", "mod", "modify", "inactive", "active", "components", "computed", "$store", "state", "menuBarShow", "pathName", "$route", "AppLayout", "AppLogout", "data", "testing", "interval", "mapState", "watch", "clearInterval", "methods", "mapMutations", "initApp", "user", "urlString", "window", "location", "href", "url", "URL", "user_id", "searchParams", "get", "portal_id", "accountUser", "accountPhone", "setUserData", "dummyData", "updateOnlineStatus", "e", "type", "reload", "setErrorApp", "setErrorMsgApp", "mounted", "dataLoaded", "noNeedToPermission", "isAdmin", "isPermission", "attrs", "currentUserId", "rooms", "engagement", "participants", "loadingTab", "showAddToHubspot", "showParticipants", "setContactObjects", "roomsLoaded", "messages", "messagesLoaded", "selected<PERSON><PERSON>", "requests", "request", "labels", "showLabels", "<PERSON><PERSON><PERSON><PERSON>", "assigning<PERSON><PERSON><PERSON>", "templates", "errorMessage", "successMessage", "save<PERSON><PERSON>", "showErrorModal", "showCreateModal", "showSuccessModal", "toggleErrorModal", "toggleSuccessModal", "loadingRooms", "unreadCounts", "messageInTransit", "isMsgFetched", "on", "roomActionHandler", "sendMessage", "fetchMessages", "openFile", "toggleLabelsModal", "closeSideBar", "redirectToHubspot", "addTemplateMsg", "style", "cssVars", "showProfile", "room", "roomId", "loadFirstRoom", "roomMessage", "menuActions", "messageActions", "showSendIcon", "showFiles", "showAudio", "audioBitRate", "audioSampleRate", "showEmojis", "showReactionEmojis", "showNewMessagesDivider", "showFooter", "t", "singleRoom", "showRoomsList", "textFormatting", "linkOptions", "textareaActionEnabled", "acceptedFiles", "templatesText", "editMessage", "deleteMessage", "sendMessageReaction", "typingMessage", "textareaActionHandler", "carousel<PERSON><PERSON><PERSON>", "$event", "$emit", "forwardTemplateMsg", "scopedSlots", "_u", "_l", "$scopedSlots", "i", "key", "fn", "_t", "showCarousel", "closeCarousel", "carouselD<PERSON>", "previewImage", "previewMessage", "closeImageViewer", "show", "_s", "sender<PERSON>ame", "imageTime", "_id", "images", "activeImage", "msg_id", "close", "currentImage", "image", "index", "id", "activateImage", "thumb", "prevImage", "nextImage", "_e", "reason", "size", "svgId", "svgItem", "path", "path2", "props", "String", "default", "param", "search", "add", "toggle", "menu", "cancel", "file", "paperclip", "send", "emoji", "document", "pencil", "checkmark", "wait", "eye", "error", "dropdown", "deleted", "microphone", "item", "SvgIcon", "Boolean", "Function", "Array", "emits", "big", "username", "imageName", "date", "timestamp", "length", "imageIndex", "fetch", "blob", "link", "createElement", "createObjectURL", "download", "click", "revokeObjectURL", "directives", "rawName", "value", "expression", "errorIcon", "preventDefault", "apply", "arguments", "ErrorIcon", "heading", "content", "Object", "required", "msg", "a", "target", "split", "pop", "console", "log", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "disableFooter", "isMobile", "touchStart", "showNoRoom", "textMessages", "ROOM_EMPTY", "handleMessageSearch", "addTemplate", "ref", "onContainerScroll", "loadingMessages", "showNoMessages", "MESSAGES_EMPTY", "showMessagesStarted", "CONVERSATION_STARTED", "loadMoreMessages", "proxy", "m", "indexId", "editedMessage", "users", "$refs", "roomFooter", "newMessages", "hideOptions", "msgSearchQuery", "isGroup", "onMessageAdded", "messageActionHandler", "replyMsgHandler", "idx", "scrollIcon", "scrollToBottom", "scrollMessagesCount", "shadowFooter", "filteredEmojis", "selectEmojiItem", "activeUpOrDownEmojis", "select<PERSON><PERSON><PERSON>", "messageReply", "resetMessage", "files", "isRecording", "stopRecorder", "recordedTime", "toggleRecorder", "showUploadModal", "removeFile", "emojiOpened", "addEmoji", "launchFilePicker", "staticStyle", "onFileChange", "position", "maxHeight", "onChangeInput", "indexOf", "_k", "keyCode", "escapeTextarea", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "selectItem", "updateActiveUpOrDown", "onPasteImage", "isMessageEmpty", "sendIconDisabled", "sendIcon", "toggleUploadModal", "handleUpload", "emojiReaction", "openEmoji", "height", "emojiPickerHeight", "top", "positionTop", "emojiPickerTop", "right", "emojiPickerRight", "display", "roomFooter<PERSON>ef", "HTMLDivElement", "positionRight", "val", "setTimeout", "addCustomStyling", "querySelector", "addEventListener", "detail", "unicode", "picker", "nav", "searchBox", "textContent", "emojiPicker", "shadowRoot", "ev", "setEmojiPickerPosition", "clientY", "view", "innerWidth", "innerHeight", "mobileSize", "roomFooterTop", "getBoundingClientRect", "pickerTopPosition", "is_dragover", "stopPropagation", "handleDragDrop", "dropIcon", "extension", "pdfIcon", "docIcon", "checkIcon", "DropIcon", "PdfIcon", "DocIcon", "CheckIcon", "infoIcon", "infoMsg", "avatar", "dummy<PERSON><PERSON>ar", "userName", "typingUsers", "config", "baseURL", "userId", "portalId", "ExternalIcon", "template<PERSON><PERSON>ler", "rotateSvg", "templateOpened", "DropDownIcon", "closeTemplates", "localTemplates", "searchTemplate", "domProps", "composing", "debouncedHandleKeypressWhatsApp", "searchIcon", "loading", "filteredTemplates", "template", "openModal", "showNoDataMessage", "isModalOpen", "selectedTemplate", "object_id", "defaultHeaderTemplateUrl", "closeModal", "filter", "status", "IS_TYPING", "map", "join", "closeIcon", "<PERSON><PERSON><PERSON><PERSON>", "language", "hasHeaderParam", "tokenModalHandler", "activeTokenModal", "searchProperties", "properties", "property", "label", "addToken", "templateFields", "$set", "params", "matchedDependency", "dependentFieldNames", "field", "values", "keys", "endsWith", "tokenValue", "defaultUrl", "updateField", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendtemplateError", "hideErrorModal", "process", "axios", "Loader", "ErrorModal", "roomPhone", "tokens", "objectId", "Number", "templateData", "find", "dep", "controllingFieldValue", "created", "getHubspotProperties", "handler", "newVal", "dependancyKey", "object<PERSON>ey", "immediate", "deep", "event", "decodedValue", "decodeURIComponent", "selectedData", "searchValue", "trim", "toLowerCase", "tokenItems", "parentElement", "querySelectorAll", "notfound", "found", "token", "tokenName", "getAttribute", "includes", "phone", "message", "templateId", "fields", "reqData", "post", "then", "response", "ok", "catch", "err", "results", "TemplateModal", "clickOutside", "vClickOutside", "refreshIcon", "RefreshMsg", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menuOpened", "SearchIcon", "currentIdx", "typingText", "filtered", "firstName", "lastName", "debounce", "handleKeypressWhatsApp", "newFilteredTemplates", "newTemplates", "func", "timeout", "args", "context", "clearTimeout", "searchText", "isMatch", "localMatches", "filters", "parse", "matchTemplates", "uniqueNewTemplates", "newTemplate", "some", "existingTemplate", "concat", "newItem", "closeMenu", "checkTemplateHeaderUrl", "file_url", "bottom", "$parent", "clientHeight", "isPdf", "isPdfFile", "require", "RoomFile", "isImage", "firstFile", "isVideo", "isOtherFile", "singleLine", "filteredLinkifiedMessage", "parts", "part", "tag", "bold", "italic", "strike", "underline", "inline", "multiline", "reply", "rel", "queries", "formattedContent", "linkify", "text", "doLinkify", "preprocessText", "json", "compileToJSON", "html", "compileToHTML", "result", "linkifyResult", "replace", "validBoldMatches", "matchAll", "cleaned", "skipNext", "isPartOfBold", "start", "slice", "typeMarkdown", "pseudoMarkdown", "end", "allowed_chars", "str", "minIndexOf", "minIndexOfKey", "links", "minIndexFromLink", "for<PERSON>ach", "startingValue", "io", "strLeft", "substr", "strLink", "strRight", "push", "char", "match", "RegExp", "object", "types", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "it", "array", "arr", "spaces", "TextHighlight", "linkifiedMessage", "formatString", "formatTags", "disabled", "cleanText", "checkType", "checkImageType", "groupInlineMessages", "isArray", "grouped", "buffer", "flushBuffer", "test", "lastIndexOf", "slashIndex", "substring", "isMedia", "IMAGE_TYPES", "setImageSize", "Image", "onLoad", "img", "removeEventListener", "ratio", "width", "Math", "round", "src", "firstTag", "secondTag", "usertags", "initialContent", "replaceAll", "openTag", "u", "isImageFile", "isVideoFile", "FormatMessage", "activeItem", "activeUpOrDown", "showDate", "NEW_MESSAGES", "system", "roomUsers", "fromMe", "messageOffset", "isMessageHover", "saved", "onHoverMessage", "onLeaveMessage", "replyMessage", "MESSAGE_DELETED", "isAudio", "progressTime", "hoverAudioProgress", "edited", "isCheckmarkVisible", "distributed", "seen", "showTimeStamp", "failed", "messageHover", "hoverMessageId", "optionsOpened", "openForwardModal", "replyUsername", "containsFile", "fileIcon", "FileIcon", "imageFiles", "handleCarousel", "videoFiles", "otherFiles", "localUrl", "downloadIcon", "downloadIconDark", "imageHover", "inUploadState", "imageResponsive", "isAudioFile", "UploadState", "imageLoading", "checkImgLoad", "clientWidth", "loaderTop", "action", "isImageVideoFile", "MessageFile", "DownloadIcon", "DownloadIconDark", "filteredMessageActions", "isMessageActions", "isMessageReactions", "openOptions", "closeOptions", "menuOptionsTop", "title", "optionsClosing", "disableActions", "disableReactions", "onlyMe", "<PERSON><PERSON><PERSON><PERSON>", "menuOptions", "actionIcon", "actionIconTop", "optionsTopPosition", "updateMessageHover", "reactions", "reaction", "float", "playback", "isPlaying", "progress", "onUpdateProgress", "playerUniqId", "audioSource", "onMouseDown", "percentage", "isMouseDown", "left", "seekPos", "calculateLineHeadPosition", "onMouseMove", "onMouseUp", "element", "progressWidth", "leftPosition", "pos", "clientX", "AudioControl", "duration", "convertTimeMMSS", "playedTime", "_uid", "resetProgress", "player", "getElementById", "updateProgressTime", "onTimeUpdate", "seconds", "Date", "toISOString", "pause", "play", "currentTime", "messagesValidation", "AudioPlayer", "MessageReply", "MessageFiles", "MessageActions", "MessageReactions", "newMessage", "reduce", "res", "obj", "canEditMessage", "messageId", "remove", "items", "prop1", "prop2", "startsWith", "v", "string", "normalize", "lamejs", "_", "missing", "Mp3Encoder", "constructor", "Error", "bitRate", "sampleRate", "dataBuffer", "encoder", "encode", "arrayBuffer", "maxSamples", "samples", "_convertBuffer", "remaining", "subarray", "encodeBuffer", "Int8Array", "finish", "flush", "Blob", "now", "_floatTo16BitPCM", "input", "output", "s", "max", "min", "Float32Array", "out", "Int16Array", "options", "beforeRecording", "pauseRecording", "afterRecording", "micFailed", "encoderOptions", "bufferSize", "records", "isPause", "volume", "_duration", "constraints", "video", "audio", "channelCount", "echoCancellation", "navigator", "mediaDevices", "getUserMedia", "_micCaptured", "bind", "_micError", "<PERSON><PERSON><PERSON><PERSON>", "stop", "stream", "getTracks", "track", "disconnect", "processor", "record", "AudioContext", "webkitAudioContext", "createMediaStreamSource", "createScriptProcessor", "onaudioprocess", "sample", "inputBuffer", "getChannelData", "sum", "parseFloat", "toFixed", "sqrt", "connect", "destination", "detectMobile", "iOSDevice", "delay", "inDebounce", "InfiniteLoading", "EmojiPickerContainer", "RoomHeader", "RoomFiles", "RoomMessageReply", "Room<PERSON><PERSON><PERSON>s", "Message", "UploadModal", "Info", "SendIcon", "SendIconDisabled", "infiniteState", "loadingMoreMessages", "fileDialog", "keepKeyboardOpen", "filteredUsersTag", "selectedUsersTag", "filteredTemplatesText", "selectUsersTagItem", "selectTemplatesTextItem", "activeUpOrDownUsersTag", "activeUpOrDownTemplatesText", "textareaCursorPosition", "cursorRangePosition", "emojisDB", "Database", "recorder", "initRecorder", "format", "noRoomSelected", "getTextareaRef", "loaded", "focusTextarea", "oldVal", "onRoomChanged", "new", "getLastMessageFromOther", "complete", "updateFooterList", "resetFooterList", "<PERSON><PERSON><PERSON><PERSON>", "reversedMessages", "reverse", "lastMessageFromOther", "time", "messageTime", "hoursDifference", "roomTextarea", "blur", "txt", "touchEvent", "changedTouches", "posXStart", "posYStart", "touchEnd", "once", "posXEnd", "posYEnd", "swippedRight", "swippedVertically", "abs", "unwatch", "$watch", "scrollContainer", "scrollTo", "scrollHeight", "autoScrollOffset", "offsetHeight", "getBottomScroll", "bottomScroll", "tagChar", "selectionStart", "char<PERSON>t", "beforeTag", "notLetterNumber", "query", "updateEmojis", "updateShowUsersTag", "updateShowTemplatesText", "getCharPosition", "cursorPosition", "endPosition", "emojis", "getEmojiBySearchQuery", "filteredItems", "selectUserTag", "editMode", "space", "selectTemplateText", "direction", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initRoom", "resetTextareaSize", "preventKeyboardFromClosing", "focus", "setSelectionRange", "newContent", "usersTag", "messageReaction", "messageContent", "resizeTextarea", "scrollTop", "classList", "behavior", "el", "padding", "getComputedStyle", "getPropertyValue", "pasteEvent", "clipboardData", "getAsFile", "existingFilesArr", "validFiles", "from", "filesArr", "Set", "fileURL", "blobFile", "typeIndex", "fileName", "rawData", "splice", "Recorder", "recording", "dataTransfer", "border", "scrollIntoView", "ROOMS_EMPTY", "TYPE_MESSAGE", "SEARCH", "IS_ONLINE", "LAST_SEEN", "defaultThemeStyles", "light", "general", "color", "backgroundInput", "colorPlaceholder", "colorCaret", "colorSpinner", "borderStyle", "backgroundScrollIcon", "container", "borderRadius", "boxShadow", "header", "background", "colorRoomName", "colorRoomInfo", "footer", "borderStyleInput", "borderInputSelected", "backgroundReply", "backgroundTagActive", "backgroundTag", "sidemenu", "backgroundHover", "backgroundActive", "colorActive", "borderColorSearch", "backgroundMe", "colorStarted", "backgroundDeleted", "colorDeleted", "colorUsername", "colorTimestamp", "backgroundDate", "colorDate", "backgroundSystem", "colorSystem", "backgroundMedia", "colorReplyUsername", "colorReply", "colorTag", "backgroundImage", "colorNewMessages", "backgroundS<PERSON><PERSON><PERSON><PERSON><PERSON>", "colorScrollCounter", "backgroundReaction", "borderStyleReaction", "backgroundReactionHover", "borderStyleReactionHover", "colorReactionCounter", "backgroundReactionMe", "borderStyleReactionMe", "backgroundReactionHoverMe", "borderStyleReactionHoverMe", "colorReactionCounterMe", "backgroundAudioRecord", "backgroundAudioLine", "backgroundAudioProgress", "backgroundAudioProgressSelector", "colorFileExtension", "markdown", "colorMulti", "colorMessage", "colorStateOnline", "colorStateOffline", "backgroundCounterBadge", "colorCounterBadge", "icons", "closeImage", "closeOutline", "sendDisabled", "checkmarkSeen", "dropdownMessage", "dropdownMessageBackground", "dropdownRoom", "dropdownScroll", "audioPlay", "audioPause", "audioCancel", "audioConfirm", "dark", "cssThemeVars", "roomsValidation", "partcipantsValidation", "Room", "AppCarousel", "SuccessModal", "ImageViewer", "theme", "styles", "roomsListOpened", "roomActions", "showSearch", "showAddRoom", "sidebarVisible", "forwardMessage", "loadingMoreRooms", "startchat", "locales", "defaultStyles", "customStyles", "r", "fetchRoom", "entries", "axiosSource", "source", "did", "toggleProfile", "startChat", "closeChat", "onPage", "getTimestamp", "toLocaleString", "hour", "minute", "hour12", "randomId", "random", "toString", "parseTimestamp", "zeroPad", "getHours", "getMinutes", "month", "year", "day", "Intl", "DateTimeFormat", "num", "pad", "padStart", "isSameDay", "d1", "d2", "getFullYear", "getMonth", "getDate", "formatDate", "toLocaleDateString", "ChatWindow", "Spinner", "Avatar", "currentChatId", "origin", "errorMessagwhatshive_logoe", "fetchMsgData", "loader", "<PERSON><PERSON><PERSON><PERSON>", "fetchPermissions", "initialRequest", "subscribe", "urlParams", "URLSearchParams", "permissionsCache", "permissions", "permission", "admin", "dialogId", "pusher", "<PERSON><PERSON><PERSON>", "forceTLS", "cluster", "authEndpoint", "auth", "connection", "channel", "payload", "chatId", "formatMessage", "findIndex", "tmp_id", "status_reason", "resetCounts", "templateReq", "responses", "Promise", "all", "formatTimestamp", "timestampFormat", "selected", "handlePin", "itemIndex", "pinned", "req", "unshift", "combinedMessages", "combineFiles", "reset", "currentTimeSeconds", "cancelToken", "logResponseErrors", "isMsgFetche", "elems", "getElementsByClassName", "elem", "innerHTML", "clearOldRequest", "allMessages", "lastIndex", "lastFileIndex", "inc", "quotedMsgId", "quotedFiles", "quotedMsgBody", "caption", "getCounts", "countInStorage", "localStorage", "getItem", "JSON", "setCounts", "counts", "setUnreadCounts", "countsStr", "stringify", "setItem", "sendFileLocal", "sendText", "tempMsgId", "currentMsgIndex", "getTime", "savedMessage", "sentMessage", "sendFileApi", "formData", "FormData", "append", "updateFileState", "open", "toggleMenuBar", "<PERSON><PERSON>", "use", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "routes", "alias", "Activities", "redirect", "router", "base", "linkExactActiveClass", "Vuex", "errorApp", "errorMsgApp", "showBanner", "showConflict", "bannerContent", "mutations", "logoutUser", "setBanner", "setConflict", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "actions", "logout", "ctx", "commit", "productionTip", "FloatingVue", "store", "h", "App", "$mount", "VIDEO_TYPES", "AUDIO_TYPES", "roomsValidate", "validate", "every", "prop", "validType", "checkObjectValid", "participantsValidate", "messagesValidate", "prototype", "hasOwnProperty", "call", "undefined", "checkMediaType", "userAgent", "getUserAgent", "userAgentPart", "vendor", "opera", "platform", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "exports", "module", "__webpack_modules__", "deferred", "O", "chunkIds", "priority", "notFulfilled", "Infinity", "fulfilled", "j", "n", "getter", "__esModule", "d", "definition", "o", "defineProperty", "enumerable", "g", "globalThis", "Symbol", "toStringTag", "p", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "chunkLoadingGlobal", "self", "__webpack_exports__"], "sourceRoot": ""}