<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Http\JsonResponse;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;
use PHPOpenSourceSaver\JWTAuth\Exceptions\JWTException;
use PHPOpenSourceSaver\JWTAuth\Exceptions\TokenExpiredException;
use PHPOpenSourceSaver\JWTAuth\Exceptions\TokenInvalidException;

class JwtMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $user = JWTAuth::parseToken()->authenticate();

            if (!$user) {
                return response()->json(['error' => 'User not found'], 200);
            }
        } catch (TokenExpiredException $e) {
            return new JsonResponse(['error' => 'Token expired'], 200);
        } catch (TokenInvalidException $e) {
            return new JsonResponse(['error' => 'Token invalid'], 200);
        } catch (JWTException $e) {
            return new JsonResponse(['error' => 'Token absent'], 200);
        } catch (Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 200);
        }
        
        return $next($request);
    }
}
