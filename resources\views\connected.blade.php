<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Successfully Connected</title>
</head>
<body>

	<script type="text/javascript">
		let data = {
			success:true,
			portalId: "<?=$portalId?>",
			wabaPhoneId: "<?=$phone_id?>",
		}

		if (window.opener && !window.opener.closed) {
			window.opener.processChildMessage(JSON.stringify(data));
			window.close();
		} else {
			window.location.href = '<?=env('APP_URL')?>'+'/last-step?portalId='+data.portalId+'&waba_phone_id='+data.wabaPhoneId
		}
	</script>
</body>
</html>
