<?php

namespace App\Http\Controllers;

use App\Helpers\Waba;
use Illuminate\Http\Request;
use App\Models\PortalOtpAccount;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Config;
use App\Services\Whatsapp\TemplateService;

class WhatsAppOtpController extends Controller
{
    public function __construct()
    {
        parent::__construct('otp');
    }

    public function sendOtpTemplate(Request $request)
    {
        $input = $request->validate([
            'phone' => 'required',
            'portalId' => 'required',
        ]);

        $otpLength = Config::get('app.otp.length', 6);
        $portalOtpAccount = PortalOtpAccount::where('portal_id', $input['portalId'])->orderBy('id')->first();
        if (! $portalOtpAccount) {
            return response()->json([
                'status' => 'error',
                'message' => 'No account found for this portal.',
            ], 404);
        }
        $accountId = $portalOtpAccount->account_id;
        $templateName = $portalOtpAccount->template_name;

        $otp = str_pad(mt_rand(0, pow(10, $otpLength) - 1), $otpLength, '0', STR_PAD_LEFT);
        $timestamp = time();
        $otpWithTimestamp = $otp.'|'.$timestamp;
        $encryptedOtp = Crypt::encrypt($otpWithTimestamp);

        $options = [
            'phone' => $input['phone'],
            'params' => [
                [
                    'type' => 'text',
                    'text' => $otp,
                ],
            ],
            'dynamic_button' => $otp,
        ];

        $waba = new Waba($accountId, $this->requestId);

        $filters['name'] = $templateName;
        $template = $waba->fetchTemplatesWithFilters($filters, 1);
        if (! $template->data || count($template->data) == 0) {
            Log::error("[WhatsappOtpController:sendOtpTemplate] Request ID : $this->requestId, Phone: {$input['phone']}, Template not found");

            return response()->json([
                'status' => 'error',
                'message' => 'Template not found',
            ], 404);
        }

        $template = $template->data[0];
        $templateService = new TemplateService($template);
        $template = $templateService->analyzeTemplate();
        $options['template'] = $template;

        $res = $waba->sendMediaTemplate($options);
        if ($res === false || ! isset($res->customData['messageId'])) {
            Log::error("[WhatsappOtpController:sendOtpTemplate] Request ID : $this->requestId, Phone: {$input['phone']}, unable to send text message");

            return response()->json([
                'status' => 'error',
                'message' => 'unable to send text message',
            ], 500);
        }

        Log::info("[WhatsappOtpController:sendOtpTemplate] Request ID : $this->requestId, Phone: {$input['phone']}");

        return response()->json([
            'status' => 'success',
            'id' => $encryptedOtp,
        ]);
    }

    public function verifyOtp(Request $request)
    {
        $input = $request->validate([
            'phone' => 'required',
            'otp' => ['required', 'digits:'.Config::get('app.otp.length', 6)],
            'timeId' => 'required',
        ]);

        try {
            $otpWithTimestamp = Crypt::decrypt($input['timeId']);
            $otpParts = explode('|', $otpWithTimestamp);
            $otp = $otpParts[0];
            $timestamp = $otpParts[1];
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }

        // Compare the timestamp with the current time, if the difference is more than 10 minutes, return error
        $currentTime = time();
        $otpExpirationTime = Config::get('app.otp.expiration_time', 600);
        if ($currentTime - $timestamp > $otpExpirationTime) {
            return response()->json([
                'status' => 'error',
                'message' => 'OTP expired',
            ], 400);
        }

        // Check if the provided OTP matches the generated OTP
        if (! hash_equals($otp, $input['otp'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Incorrect OTP',
            ], 400);
        }

        // if the otp is correct, return success
        return response()->json([
            'status' => 'success',
            'message' => 'OTP verified',
        ], 200);
    }
}
