<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AppController;
use App\Http\Controllers\FlowController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\LabelController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\StripeController;
use App\Http\Controllers\HubspotController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\FacebookController;
use App\Http\Controllers\BlacklistController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\WhatsAppOtpController;
use App\Http\Controllers\HsConversationController;
use App\Http\Controllers\WhatsAppTemplateController;
use App\Http\Controllers\HsEventSubscriptionsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

Route::get('/banner', [AppController::class, 'banner']);
Route::get('/users/{email}', [UserController::class, 'show']); // get a specific user
Route::post('/users/refresh-token', [UserController::class, 'refreshToken']);

Route::middleware(['sameOrigin', 'jwt.auth','auth.user'])->group(function () {

    // fetch template data
    Route::get('/users', [UserController::class, 'index']); // get all users
    
    Route::post('/users', [UserController::class, 'store']); // create a user
    Route::put('/users/{email}', [UserController::class, 'update']); // update user
    Route::post('/check_media', [AppController::class, 'checkMedia']); // checkmedia
    Route::get('template/media/{id}', [AppController::class, 'getTemplateMediaUrl']);

    Route::get('/permissions', [PermissionController::class, 'index']); // Get all permissions
    Route::get('/permissions/{id}', [PermissionController::class, 'show']); // Get a specific permission
    Route::post('/permissions', [PermissionController::class, 'store']); // Create a new permission
    Route::put('/permissions/{id}', [PermissionController::class, 'update']); // Update a permission
    Route::delete('/permissions/{id}', [PermissionController::class, 'destroy']); // Delete a permission

    Route::get('/roles', [RoleController::class, 'index']); // Get all roles
    Route::get('/roles/{id}', [RoleController::class, 'show']); // Get a single role by ID
    Route::post('/roles', [RoleController::class, 'store']); // Create a new role
    Route::put('/roles/{id}', [RoleController::class, 'update']); // Update a role by ID
    Route::delete('/roles/{id}', [RoleController::class, 'destroy']); // Delete a role by ID

    Route::get('/reports', [ReportController::class, 'index']);
    Route::get('/message-analytics', [ReportController::class, 'getMessagesAnalytics']);
    Route::get('/workflow/report', [ReportController::class, 'workflow']);

    Route::get('/chat', [AppController::class, 'chat']);
    Route::get('/dialogs', [AppController::class, 'dialogs']);
    Route::get('/account', [AppController::class, 'account']);
    Route::post('/pusher/auth', [AppController::class, 'pusherAuth']);
    Route::get('/v1/whatsapp/templates', [AppController::class, 'whatsappTemplates']);

    // send messages to hubspot list feature
    Route::get('/lists', [AppController::class, 'getLists']);
    Route::post('/lists', [AppController::class, 'storeLists']);
    Route::post('/lists/{id}', [AppController::class, 'updateLists']);

    Route::post('send', [AppController::class, 'send']);
    Route::post('upload', [AppController::class, 'upload']);
    Route::post('reply', [AppController::class, 'reply']);
    Route::post('dialog/{id}', [AppController::class, 'changeDialog']);

    // Whatsapp flows
    Route::get('/flows', [FlowController::class, 'index']);
    Route::post('/flows', [FlowController::class, 'store']);
    Route::patch('/flows/{flowId}', [FlowController::class, 'update']);
    Route::delete('/flows/{flowId}', [FlowController::class, 'delete']);
    Route::get('whatsapp/flows/asset/{id}', [FlowController::class, 'flowAsset']);
    Route::get('whatsapp/flows/templates', [FlowController::class, 'flowTemplates']);

    // settings routes
    Route::get('/settings', [SettingController::class, 'index']);
    Route::post('/settings', [SettingController::class, 'store']);

    // label routes
    Route::get('/label', [LabelController::class, 'index']);
    Route::post('/label', [LabelController::class, 'store']);
    Route::delete('/label/{id}', [LabelController::class, 'delete']);
    Route::get('/user/labels', [LabelController::class, 'userLabels']);
    Route::post('/label/assign', [LabelController::class, 'assignLabel']);

    // blacklist routes
    Route::get('blacklist', [BlacklistController::class, 'index']);
    Route::post('blacklist', [BlacklistController::class, 'store']);
    Route::delete('blacklist/{id}', [BlacklistController::class, 'delete']);

    Route::post('v1/send-template', [AppController::class, 'sendTemplate']);

    Route::get('record', [AppController::class, 'storeActiveUserAnalytics']);

    Route::prefix('whatsapp/template')->group(function () {
        Route::post('/', [WhatsAppTemplateController::class, 'index']);
        Route::post('/create', [WhatsAppTemplateController::class, 'create']);
        Route::put('/update/{template_id}', [WhatsAppTemplateController::class, 'update']);
        Route::delete('/delete/{template_name}', [WhatsAppTemplateController::class, 'delete']);
        Route::get('/get/{template_id}', [WhatsAppTemplateController::class, 'getTemplateById']);
        Route::post('/upload', [WhatsAppTemplateController::class, 'upload']);
        Route::get('/analytics', [AppController::class, 'getTemplateAnalytics']);
    });
});

Route::middleware(['verifyOrigin'])->group(function () {
    Route::post('/verify-otp', [WhatsAppOtpController::class, 'verifyOtp']);
    Route::post('/send-otp', [WhatsAppOtpController::class, 'sendOtpTemplate']);
});
Route::post('/deploy', [AppController::class, 'deploy']);

// facebook
Route::get('/facebook/exchange', [FacebookController::class, 'exchange']);
Route::post('/facebook/exchange', [FacebookController::class, 'exchange']);
Route::post('/facebook/save', [FacebookController::class, 'save']);
Route::post('/facebook/webhook', [FacebookController::class, 'webhook']);
Route::post('/facebook/register', [FacebookController::class, 'register']);
Route::get('/facebook/webhook', [FacebookController::class, 'webhookVerifyCode']);

// hubspot routes
Route::get('hubspot/crm', [HubspotController::class, 'crm']);
Route::get('hubspot/owners', [HubspotController::class, 'owners']);
Route::get('hubspot/lists', [HubspotController::class, 'hubspotLists']);
Route::get('hubspot/properties', [HubspotController::class, 'hubspotProperties']);
Route::post('hubspot/webhook', [HsEventSubscriptionsController::class, 'webhook']);
Route::post('hubspot/conversation/save', [HsConversationController::class, 'save']);
Route::post('hubspot/conversation/webhook', [HsConversationController::class, 'webhook']);
Route::post('hubspot/createContactList', [HubspotController::class, 'createContactList']);

// hubspot workflow routes
Route::post('hubspot/workflow/text', [HubspotController::class, 'workflow']);
Route::post('hubspot/workflow/media', [HubspotController::class, 'workflowMedia']);
Route::post('v1/hubspot/workflow/media', [HubspotController::class, 'workflowMediaV1']);
Route::post('hubspot/workflow/accounts', [HubspotController::class, 'accounts']);
Route::post('hubspot/workflow/media/type', [HubspotController::class, 'mediaType']);
Route::post('hubspot/workflow/button/type', [HubspotController::class, 'buttonType']);
Route::post('v1/hubspot/workflow/media/type', [HubspotController::class, 'mediaTypeV1']);
Route::post('hubspot/workflow/message/types', [HubspotController::class, 'messageTypes']);
Route::post('hubspot/workflow/templates', [HubspotController::class, 'workflowTemplates']);
Route::post('v1/hubspot/workflow/templates', [HubspotController::class, 'workflowTemplatesV1']);
Route::post('hubspot/workflow/template/params', [HubspotController::class, 'workflowParams']);
Route::post('hubspot/workflow/template/params/header', [HubspotController::class, 'workflowParamsHeader']);

// stripe routes
Route::get('stripe/config', [StripeController::class, 'config']);
Route::post('stripe/save', [StripeController::class, 'save']);
Route::post('stripe/webhook', [StripeController::class, 'webhook']);
Route::post('dummy_sub', [StripeController::class, 'dummySub'])->name('dummy_sub');
Route::post('stripe/create-subscription', [StripeController::class, 'createSubscription']);
