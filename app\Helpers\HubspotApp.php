<?php

namespace App\Helpers;

use Log;
use Illuminate\Support\Facades\Http;

class HubspotApp
{
    public $app;

    public $hsApp;

    public $model;

    public $requestId;

    public $portalTokens;

    public function __construct($app, $model, $requestId)
    {
        $this->app = $app;
        $this->model = $model;
        $this->requestId = $requestId;
        if (env('APP_ENV') == 'development') {
            $this->hsApp = config('hsapp.wabadev');
        } else {
            $this->hsApp = config('hsapp.'.$app);
        }
    }

    public function getClient($portalId)
    {
        $tokens = $this->getTokens($portalId);

        return HubspotClient::createFactory($tokens['access_token']);
    }

    public function getTokens($portal_id, $onlyGetAccessToken = false)
    {
        if ($this->portalTokens) {
            return $onlyGetAccessToken ? $this->portalTokens['access_token'] : $this->portalTokens;
        }

        $portal_id = explode('.', $portal_id)[0] ?? $portal_id;
        $tokens = $this->model::where('portal_id', $portal_id)->first();
        if (! $tokens) {
            return false;
        }

        $passed_time = time() - strtotime($tokens->updated_at);

        $this->portalTokens = $passed_time > $tokens->expires_in ? $this->refreshToken($tokens) : $tokens;

        return $onlyGetAccessToken ? $this->portalTokens['access_token'] : $this->portalTokens;
    }

    public function refreshToken($tokens)
    {
        $hsApp = $this->hsApp['auth'];
        $hsApp['grant_type'] = 'refresh_token';
        $hsApp['refresh_token'] = $tokens['refresh_token'];
        unset($hsApp['code']);
        unset($hsApp['redirect_uri']);

        $newTokens = Http::asForm()->post('https://api.hubapi.com/oauth/v1/token', $hsApp)->object();

        if (isset($newTokens->status)) {
            $newTokens->refresh_token = $tokens['refresh_token'];
            Log::error("[HubspotApp:refreshToken] for $this->requestId, Error: ".json_encode($newTokens));

            return $tokens;
        }

        if (isset($newTokens->access_token)) {
            $this->model::where('portal_id', $tokens['portal_id'])
                ->update([
                    'access_token' => $newTokens->access_token,
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
            $tokens['access_token'] = $newTokens->access_token;
        }

        return $tokens;
    }

    public function getAssociations($objectId, $portalId, $definitionId)
    {
        $tokens = $this->getTokens($portalId);
        $hub_url = "https://api.hubapi.com/crm-associations/v1/associations/$objectId/HUBSPOT_DEFINED/".$definitionId;

        $associations = Http::withToken($tokens['access_token'])->get($hub_url)->object();

        if (isset($associations->status)) {
            Log::error("[HubspotApp:getAssociations] for $this->requestId, error:".json_encode($associations));
        }

        return $associations->results ?? false;
    }

    public function buildContactProps($data)
    {
        $props['properties'] = [];
        foreach ($data as $key => $value) {
            $props['properties'][] = [
                'property' => $key,
                'value' => $value,
            ];
        }

        return $props;
    }

    public function fetchTokenInfo($access_token)
    {
        return Http::get("https://api.hubapi.com/oauth/v1/access-tokens/$access_token")->object();
    }

    public function createOrUpdateContact($portal_id, $properties, $email)
    {
        $tokens = $this->getTokens($portal_id);
        $hub_url = "https://api.hubapi.com/contacts/v1/contact/createOrUpdate/email/$email";

        $response = Http::withToken($tokens['access_token'])->post($hub_url, $properties)->object();
        if (! isset($response->vid)) {
            Log::error('[HubspotApp]Error in createOrUpdateContact: '.json_encode($response));

            return false;
        }

        return true;
    }

    public function createContactProperty($portalId, $property)
    {
        $tokens = $this->getTokens($portalId);
        $apiUrl = 'https://api.hubapi.com/properties/v1/contacts/properties';

        $res = Http::withToken($tokens['access_token'])->post($apiUrl, $property)->object();

        if (isset($res->status)) {
            $propertiesErrorCode = $res->propertiesErrorCode ?? null;
            if ($propertiesErrorCode == 'PROPERTY_EXISTS') {
                return true;
            }
            Log::error("[HubspotApp:createContactProperty] $this->requestId:$portalId, Error: ".json_encode($res));

            return false;
        }

        return $res;
    }

    public function updateStatusTimeline($portalId, $events)
    {
        $eventUrl = 'timeline/event';
        if (count($events) > 1) {
            $eventUrl .= '/batch';
        }

        if (count($events) == 1) {
            $event = $events[0];
            $data = [
                'id' => $event['id'],
                'eventTypeId' => $this->hsApp['config']['message_sent'],
                'status' => $event['data']['status'],
                'message' => $event['data']['message'],
                'objectId' => $event['objectId'],
            ];
        } else {
            $count = 0;
            $data['eventWrappers'] = [];
            foreach ($events as $event) {
                $event = [
                    'id' => $event['id'].$event['objectId'],
                    'eventTypeId' => $this->hsApp['config']['message_sent'],
                    'status' => $event['data']['status'],
                    'message' => $event['data']['message'],
                    'objectId' => $event['objectId'],
                ];
                $data['eventWrappers'][] = $event;
                $count++;
            }
        }

        $tokens = $this->getTokens($portalId);
        $res = Http::withToken($tokens['access_token'])->put($this->hsApp['url'].$eventUrl, $data)->object();

        if (isset($res->status)) {
            Log::error("[HubspotApp:updateTimeline] for $this->requestId, error: ".json_encode($res));

            return false;
        }

        return true;
    }
}
