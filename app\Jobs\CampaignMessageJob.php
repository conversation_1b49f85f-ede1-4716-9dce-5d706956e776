<?php

namespace App\Jobs;

use Log;
use Exception;
use Throwable;
use App\Helpers\Func;
use App\Models\Dialog;
use App\Models\HubList;
use App\Models\Message;
use App\Hubspot\Hubspot;
use App\Models\Campaign;
use App\Helpers\HelperTrait;
use Illuminate\Bus\Queueable;
use App\Services\Whatsapp\SendService;
use Illuminate\Queue\InteractsWithQueue;
use App\Services\Whatsapp\TemplateService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class CampaignMessageJob implements ShouldQueue
{
    use Dispatchable;
    use HelperTrait;
    use InteractsWithQueue;
    use Queueable;

    protected $contact;

    protected $template;

    protected $accountId;

    protected $requestId;

    protected $hubspotApp;

    protected $properties;

    protected $campaignId;

    public function __construct($data, $requestId)
    {
        $this->requestId = $requestId;
        $this->campaignId = $data->campaignId;
        $this->contact = $data->contact;
        $this->template = $data->template;
        $this->accountId = $data->accountId;
        $this->properties = $data->properties;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("[CampaignMessageJob:handle] Running for $this->requestId, AccountId: $this->accountId");

        $waba = new SendService($this->accountId, $this->requestId);

        $options = [];
        $portal = $waba->account;
        $hsApp = new Hubspot($portal->portal_id, $this->requestId);
        $phone = $this->contact->properties->phone ?? $this->contact->properties->mobilephone ?? '';

        $email = $this->contact->properties->email ?? '';
        $firstname = $this->contact->properties->firstname ?? '';
        $lastname = $this->contact->properties->lastname ?? '';
        $name = trim("$firstname $lastname");

        // build campaign data
        $campaign = [
            'campaign_id' => $this->campaignId,
            'account_id' => $this->accountId,
            'portal_id' => $portal->portal_id,
            'object_id' => $this->contact->id,
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'status' => 'sent',
        ];

        try {
            if (! $phone) {
                throw new Exception("contact doesn't have phone number", 1); // NOSONAR
            }

            $phone = $options['phone'] = Func::makeChatId($phone);
            $template = $waba->fetchTemplateById($this->template->id);

            if (! $template) {
                throw new Exception('template not found', 1); // NOSONAR
            }

            $templateService = new TemplateService($template);
            $template = $templateService->analyzeTemplate();

            $options['params'] = [];
            $options['templateData'] = [];
            $options['template'] = $template;

            // build params
            $fields = (array) $this->template->fields ?? [];
            $namedParams = $template->named ?? [];
            $paramCount = $template->params;
            for ($i = 0; $i < $paramCount; $i++) {
                $key = $i + 1;
                $param = [
                    'type' => 'text',
                    'text' => $fields['placeholder_'.$key],
                ];
                if (isset($namedParams[$i])) {
                    $param['parameter_name'] = $namedParams[$i];
                }
                $options['params'][] = $param;
                $options['templateData'][] = $fields['placeholder_'.$key];
            }

            $options = array_merge($options, (array) $fields);
            Log::info('[CampaignMessageJob:handle] options'.json_encode($options));
            if ($template->type && $template->type != 'text') {
                $res = $waba->sendMediaTemplate($options);
            } else {
                $res = $waba->sendTemplate($options);
            }

            $campaign['request_data'] = $waba->request;
            $campaign['response_data'] = $waba->response;

            if ($res === false || ! isset($res->customData['messageId'])) {
                throw new Exception('unable to send text message', 1); // NOSONAR
            }
            $campaign['message_id'] = $res->customData['messageId'];

            $messageBody = $template->body;
            for ($i = 0; $i < count($options['templateData']); $i++) {
                $key = $i + 1;
                $messageBody = str_replace('{{'.$key.'}}', $options['templateData'][$i], $messageBody);
            }

            // $name = $this->getNameFromProps($properties);
            Dialog::updateOrCreate(['chatId' => $options['phone'], 'account_id' => $this->accountId], [
                'object_id' => $this->contact->id,
                'phone' => $phone,
                'name' => $name,
                'time' => time(),
            ]);

            $type = $template->type ?? 'text';
            if (! in_array($type, ['image', 'audio', 'video', 'document'])) {
                $type = 'text';
            }
            $message = [
                'account_id' => $this->accountId,
                'id' => $res->customData['messageId'],
                'chatId' => $options['phone'],
                'type' => strtolower($type),
                'body' => $messageBody,
                'from' => $res->customData['from'],
                'to' => $phone,
                'fromMe' => 1,
                'status' => 'sent',
                'time' => time(),
            ];
            $flowId = $res->customData['flowId'] ?? null;
            $flowId && $message['flow_id'] = $flowId;

            $urlKey = strtolower($type).'_url';
            $fileUrl = $fields[$urlKey] ?? null;
            $type && $message['file_type'] = strtolower($type);
            $fileUrl && $message['file_url'] = $fileUrl;
            $fileUrl && $message['file_name'] = basename($fileUrl);
            $savedMessage = Message::create($message);

            // update hubspot timeline
            $hsApp->timeline()->update([
                'id' => $res->customData['messageId'],
                'objectId' => $this->contact->id,
                'data' => [
                    'status' => 'sent',
                    'phone' => $portal->waba_phone,
                    'message' => Func::messageToTimeline($savedMessage),
                ],
            ], true);

            Log::info("[CampaignMessageJob:handle] success for $this->requestId");
            HubList::where(['account_id' => $this->accountId, 'id' => $this->campaignId])->increment('sent');

            Campaign::create($campaign);
        } catch (Exception $e) {
            HubList::where(['account_id' => $this->accountId, 'id' => $this->campaignId])->increment('failed');
            $campaign['status'] = 'failed';
            $campaign['message_id'] = $this->requestId;
            $campaign['status_reason'] = $e->getMessage();
            Campaign::create($campaign);
            Log::error("[CampaignMessageJob:handle] $this->requestId, Exception: ".$e->getMessage().', Line: '.$e->getLine().', File: '.$e->getFile().', Trace: '.$e->getTraceAsString());
        }
    }

    public function failed(Throwable $e)
    {
        Log::error("[CampaignMessageJob:failed] $this->requestId, Exception: ".$e->getMessage());
    }
}
