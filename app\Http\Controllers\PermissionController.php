<?php

namespace App\Http\Controllers;

use Exception;
use App\Models\Permission;
use Illuminate\Http\Request;

class PermissionController extends Controller
{
    public function __construct()
    {
        parent::__construct('waba');
    }

    /**
     * Get all permissions.
     */
    public function index()
    {
        try {
            $permissions = Permission::all();

            return $this->jsonOk([
                'permissions' => $permissions,
            ]);
        } catch (Exception $e) {
            return $this->jsonError([
                'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Get a specific permission by ID.
     */
    public function show($id)
    {
        try {
            $permission = Permission::findOrFail($id);

            return $this->jsonOk([
                'permission' => $permission,
            ]);
        } catch (Exception $e) {
            return $this->jsonError([
                'message' => $e->getMessage()], 404);
        }
    }

    /**
     * Create a new permission.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
        ]);

        try {
            $permission = Permission::create($validated);

            return $this->jsonOk([
                'message' => 'Permission created successfully.',
                'permission' => $permission,
            ], 201);
        } catch (Exception $e) {
            return $this->jsonError([
                'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Update an existing permission.
     */
    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
        ]);

        try {
            $permission = Permission::findOrFail($id);
            $permission->update($validated);

            return $this->jsonOk([
                'message' => 'Permission updated successfully.',
                'permission' => $permission,
            ]);
        } catch (Exception $e) {
            return $this->jsonError([
                'message' => $e->getMessage()], 404);
        }
    }

    /**
     * Delete a permission by ID.
     */
    public function destroy($id)
    {
        try {
            $permission = Permission::findOrFail($id);
            $permission->delete();

            return $this->jsonOk([
                'message' => 'Permission deleted successfully.',
            ]);
        } catch (Exception $e) {
            return $this->jsonError([
                'message' => $e->getMessage()], 404);
        }
    }
}
