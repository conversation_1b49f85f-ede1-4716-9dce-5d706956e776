<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use App\Helpers\Waba;
use Illuminate\Http\Request;
use App\Services\Whatsapp\TemplateService;

class WhatsAppTemplateController extends Controller
{
    public function __construct()
    {
        parent::__construct('waba');
    }

    public function index(Request $request)
    {
        try {
            $input = $request->validate([
                'user_id' => 'required',
            ]);
            $account_id = readUserId($input['user_id']);
            $waba = new Waba($account_id, $this->requestId);
            $filters = $request->get('filters');
            $limit = $request->get('limit');
            $after = $request->get('after');
            $before = $request->get('before');
            $whatsapp_template_data = $waba->fetchTemplatesWithFilters($filters, $limit, $before, $after);
            Log::info("[WhatsAppTemplateController:index] $this->requestId, Response: ".json_encode($whatsapp_template_data));

            $response = $whatsapp_template_data;
            if (isset($filters['parse'])) {
                $parsed = [];
                $templates = $response->data ?? [];
                foreach ($templates as $template) {
                    $templateService = new TemplateService($template);
                    $parsed[] = $templateService->analyzeTemplate();
                }
                $parsed && $response->data = $parsed;
            }

            return $this->jsonOk(['templates' => $response]);
        } catch (Exception $e) {
            Log::error("[WhatsAppTemplateController:index] $this->requestId, Error: ".$e->getMessage());

            return $this->jsonError(['message' => $e->getMessage()]);
        }
    }

    public function getTemplateById(Request $request, $template_id)
    {
        try {
            $input = $request->validate([
                'user_id' => 'required',
            ]);
            $waba = new Waba(null, $this->requestId);
            $single_template_data = $waba->fetchTemplateById($template_id);
            Log::info("[WhatsAppTemplateController:getTemplateById] $this->requestId, Response: ".json_encode($single_template_data));

            return $this->jsonOk(['data' => $single_template_data ? $single_template_data : []]);
        } catch (Exception $e) {
            Log::error("[WhatsAppTemplateController:getTemplateById] $this->requestId, Error: ".$e->getMessage());

            return $this->jsonError(['message' => $e->getMessage()]);
        }
    }

    public function create(Request $request)
    {
        try {
            $input = $request->validate([
                'user_id' => 'required',
                'template' => 'required',
            ]);
            $account_id = readUserId($input['user_id']);
            Log::info("[WhatsAppTemplateController:create] $this->requestId, account_id: $account_id");
            $waba = new Waba($account_id, $this->requestId);
            $template_data = $waba->createTemplate($input['template']);
            Log::info("[WhatsAppTemplateController:create] $this->requestId, Response: ".json_encode($template_data));

            return $this->jsonOk(['data' => $template_data ? $template_data : []]);
        } catch (Exception $e) {
            Log::error("[WhatsAppTemplateController:create] $this->requestId, create: ".$e->getMessage());

            return $this->jsonError(['message' => $e->getMessage()]);
        }
    }

    public function update(Request $request, $template_id)
    {
        try {
            $input = $request->validate([
                'user_id' => 'required',
                'template' => 'required',
            ]);
            $account_id = readUserId($input['user_id']);
            $waba = new Waba($account_id, $this->requestId);
            $template_data = $waba->updateTemplate($input['template'], $template_id);
            Log::info("[WhatsAppTemplateController:update] $this->requestId, Response: ".json_encode($template_data));

            return $this->jsonOk(['data' => $template_data ? $template_data : []]);
        } catch (Exception $e) {
            Log::error("[WhatsAppTemplateController:update] $this->requestId, update: ".$e->getMessage());

            return $this->jsonError(['message' => $e->getMessage()]);
        }
    }

    public function delete(Request $request, $template_name)
    {
        try {
            $input = $request->validate([
                'user_id' => 'required',
            ]);
            $account_id = readUserId($input['user_id']);
            $waba = new Waba($account_id, $this->requestId);
            $template_data = $waba->deleteTemplate($template_name);
            Log::info("[WhatsAppTemplateController:delete] $this->requestId, Response: ".json_encode($template_data));

            return $this->jsonOk(['data' => $template_data ? $template_data : []]);
        } catch (Exception $e) {
            Log::error("[WhatsAppTemplateController:delete] $this->requestId, delete: ".$e->getMessage());

            return $this->jsonError(['message' => $e->getMessage()]);
        }
    }

    public function upload(Request $request)
    {
        try {
            $input = $request->validate([
                'file' => 'required|file|mimes:jpeg,png,gif,mp4,mov,pdf,doc,docx,xls,xlsx|max:5120',
                'user_id' => 'required',
            ]);
            $file = $request->file('file');
            $account_id = readUserId($input['user_id']);
            $waba = new Waba($account_id, $this->requestId);
            $file_data = $waba->uploadWhatsAppFile($file);

            return $this->jsonOk(['data' => $file_data]);
        } catch (Exception $e) {
            Log::error("[WhatsAppTemplateController:upload] $this->requestId, upload: ".$e->getMessage());

            return $this->jsonError(['message' => $e->getMessage()]);
        }
    }
}
