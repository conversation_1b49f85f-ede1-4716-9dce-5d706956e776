@extends('layouts.app')
@section('title')
    Vira - Access Token
@endsection
@section('content')
<div class="container-sm mt-4">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Access Token</h5>
            <button class="btn btn-sm btn-primary" id="copyBtn" onclick="copyToken()">Copy <PERSON></button>
        </div>
        <div class="card-body">
            <div class="form-group">
                <textarea id="tokenField" class="form-control font-monospace" style="resize: none; height: 100px;" readonly>{{ $token }}</textarea>
            </div>
            <div id="copyMessage" class="alert alert-success mt-2 d-none">
                Token copied to clipboard!
            </div>
        </div>
    </div>
</div>

<script>
function copyToken() {
    const tokenField = document.getElementById('tokenField');
    tokenField.select();
    document.execCommand('copy');

    // Show the success message
    const copyMessage = document.getElementById('copyMessage');
    copyMessage.classList.remove('d-none');

    // Hide the message after 3 seconds
    setTimeout(() => {
        copyMessage.classList.add('d-none');
    }, 3000);
}
</script>
@endsection
