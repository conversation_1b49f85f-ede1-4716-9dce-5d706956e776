<?php

namespace App\Helpers;

use Log;
use Exception;
use App\Models\Account;
use App\Models\Setting;
use Illuminate\Support\Facades\Http;
use App\Services\Whatsapp\TemplateStore;
use Netflie\WhatsAppCloudApi\WhatsAppCloudApi;

class Waba
{
    public $fbApp;

    public $account;

    public $baseUrl;

    public $request;

    public $response;

    public $messageUrl;

    public $error = null;

    private $templateStore;

    public function __construct(
        Account|int|null $account = null,
        public string $requestId
    ) {
        $configKey = (env('APP_ENV') === 'development' || env('APP_ENV') === 'staging') ? 'wabadev' : 'waba';
        $this->fbApp = config("fbapp.{$configKey}");
        $this->baseUrl = "https://graph.facebook.com/{$this->fbApp['version']}/";

        $this->account = match (true) {
            $account instanceof Account => $account,
            is_int($account) => Account::where('id', $account)->where('paid', '!=', 0)->first(),
            default => null
        };

        $this->messageUrl = $this->account
            ? "{$this->baseUrl}{$this->account->waba_phone_id}/messages"
            : null;

        $this->templateStore = new TemplateStore($this);
    }

    public function setAccount(Account $account)
    {
        $this->account = $account;
    }

    public function getTemplateStore()
    {
        return $this->templateStore;
    }

    public function consentPropertySchema()
    {
        return [
            'name' => 'whatsapp_consent_given',
            'label' => 'WhatsApp Consent Given',
            'description' => 'Property to store WhatsApp consent given',
            'groupName' => 'contactinformation',
            'fieldType' => 'booleancheckbox',
            'type' => 'bool',
            'options' => [
                [
                    'label' => 'Yes',
                    'value' => true,
                ],
                [
                    'label' => 'No',
                    'value' => false,
                ],
            ],
        ];
    }

    public function debugToken($accessToken)
    {
        $appToken = $this->fbApp['token'];
        $apiUrl = $this->baseUrl.'debug_token?input_token='.$accessToken;

        $response = Http::withToken($appToken)->get($apiUrl)->object();
        $this->response = json_encode($response);

        Log::info("[Waba:debugToken] $this->requestId, response: ".json_encode($response));
        if (! isset($response->data)) {
            Log::error("[Waba:debugToken] $this->requestId, AppToken:$appToken, Error: ".json_encode($response));

            return false;
        }

        return $response->data;
    }

    public function getAuthUrl()
    {
        $version = $this->fbApp['version'];
        $params = [
            'response_type' => 'code',
            'scope' => $this->fbApp['scope'],
            'client_id' => $this->fbApp['client_id'],
            'redirect_uri' => $this->fbApp['redirect_uri'],
        ];

        $url = 'https://www.facebook.com/'.$version.'/dialog/oauth?';

        return $url.http_build_query($params).'&extras={"feature":"whatsapp_embedded_signup","setup":{}}';
    }

    public function fetchAccessToken($code)
    {
        $apiUrl = $this->baseUrl.'oauth/access_token?'.http_build_query([
            'client_id' => $this->fbApp['client_id'],
            'redirect_uri' => $this->fbApp['redirect_uri'],
            'client_secret' => $this->fbApp['client_secret'],
            'code' => $code,
        ]);

        $response = Http::get($apiUrl)->object();
        $this->response = json_encode($response);
        Log::info("[Waba:fetchAccessToken] $this->requestId, payload: ".json_encode($response));
        if (! $response || ! isset($response->access_token)) {
            Log::error("[Waba:fetchAccessToken] $this->requestId, Error: ".json_encode($response));

            return false;
        }

        // fetch permanent token now
        return $this->fetchLongLivedToken($response->access_token);
    }

    public function fetchLongLivedToken($accessToken)
    {
        $apiUrl = $this->baseUrl.'oauth/access_token?'.http_build_query([
            'client_id' => $this->fbApp['client_id'],
            'grant_type' => 'fb_exchange_token',
            'client_secret' => $this->fbApp['client_secret'],
            'fb_exchange_token' => $accessToken,
            'set_token_expires_in_60_days' => 'false',
        ]);

        $response = Http::get($apiUrl)->object();
        $this->response = json_encode($response);
        Log::info("[Waba:fetchLongLivedToken] $this->requestId, response: ".json_encode($response));
        if (! $response || ! isset($response->access_token)) {
            return false;
        }

        return $response;
    }

    public function fetchNumbers($id, $token)
    {
        $apiUrl = $this->baseUrl.$id.'/phone_numbers';
        $response = Http::withToken($token)->get($apiUrl)->object();

        Log::info("[Waba:fetchNumbers] $this->requestId, Response: ".json_encode($response));
        if (isset($response->error) || ! isset($response->data) || ! $response->data) {
            Log::error("[Waba:fetchNumbers] $this->requestId, Error: ".json_encode($response));

            return false;
        }

        return $response;
    }

    public function fetchTemplates($extra_query_params = '')
    {
        // For backward compatibility with existing code
        $options = [];

        if (! empty($extra_query_params)) {
            parse_str($extra_query_params, $parsedParams);
            $options = $parsedParams;
        }

        $response = $this->templateStore->fetchTemplates($options);

        // Return just the data property for backward compatibility
        return is_object($response) && isset($response->data) ? $response->data : $response;
    }

    public function getTemplateAnalytics($start, $end, $templateIds, $after = '')
    {
        $apiUrl = $this->baseUrl.$this->account->waba_id.'/template_analytics?'
            .'start='.strtotime($start)
            .'&end='.strtotime($end)
            .'&granularity=daily'
            .'&metric_types=cost,clicked,delivered,read,sent'
            .'&template_ids='.json_encode($templateIds);
        if (! empty($after)) {
            $apiUrl .= "&after=$after";
        }
        $response = Http::timeout(120)->withToken(env('WABA_TOKEN'))->get($apiUrl)->object();
        Log::info("[Waba:getTemplateAnalytics] $this->requestId, wabaId:{$this->account->waba_id}, response: ".json_encode($response));

        if (isset($response->error)) {
            Log::error('[SyncTemplateAnalytics:getTemplateAnalytics], Error: '.json_encode($response));

            return false;
        }

        return $response;
    }

    public function fetchTemplatesWithFilters($filters, $limit = 20, $before = '', $after = '')
    {
        $options = [
            'filters' => $filters,
            'limit' => $limit,
            'before' => $before,
            'after' => $after,
        ];

        return $this->templateStore->fetchTemplates($options);
    }

    public function fetchTemplateById($templateId)
    {
        $apiUrl = $this->baseUrl.$templateId;
        $response = Http::withToken(env('WABA_TOKEN'))->get($apiUrl)->object();

        if (isset($response->error)) {
            Log::error("[Waba:fetchTemplateById] $this->requestId, Error: ".json_encode($response));

            return false;
        }

        return $response;
    }

    public function createTemplate($template)
    {
        $apiUrl = $this->baseUrl.$this->account->waba_id.'/message_templates';
        $response = Http::withToken(env('WABA_TOKEN'))->post($apiUrl, $template);

        Log::info("[Waba:createTemplate] $this->requestId, Response: ".json_encode($response->json()));

        if (! $response->successful()) {
            $error_response = $response->json();
            Log::error("[Waba:createTemplate] $this->requestId, Error: ".json_encode($error_response));

            throw new Exception($this->sendErrorResponse($error_response));
        }

        return $response->json();
    }

    public function sendErrorResponse($error)
    {
        if (isset($error['error_user_msg']) && ! empty($error['error_user_msg'])) {
            return $error['error_user_msg'];
        } elseif (isset($error['error']['error_user_msg']) && ! empty($error['error']['error_user_msg'])) {
            return $error['error']['error_user_msg'];
        } elseif (isset($error['error']['message']) && ! empty($error['error']['message'])) {
            return $error['error']['message'];
        } else {
            return 'Something went wrong. Please try again later.';
        }
    }

    public function updateTemplate($template, $template_id)
    {
        $apiUrl = $this->baseUrl.$template_id;
        $response = Http::withToken(env('WABA_TOKEN'))->post($apiUrl, $template);

        Log::info("[Waba:updateTemplate] $this->requestId, response: ".json_encode($response));
        if (isset($response->error) || ! isset($response->data)) {
            $error_response = $response->json();
            Log::error("[Waba:createTemplate] $this->requestId, Error: ".json_encode($error_response));

            throw new Exception($this->sendErrorResponse($error_response));
        }

        return $response->json();
    }

    public function deleteTemplate($templateName)
    {
        $apiUrl = $this->baseUrl.$this->account->waba_id.'/message_templates?name='.$templateName;
        $response = Http::withToken(env('WABA_TOKEN'))->delete($apiUrl);

        Log::info("[Waba:deleteTemplate] $this->requestId, response: ".json_encode($response));
        if (isset($response->error) && ! empty($response->error)) {
            $error_response = $response->json();
            Log::error("[Waba:createTemplate] $this->requestId, Error: ".json_encode($error_response));

            throw new Exception($this->sendErrorResponse($error_response));
        }

        return $response->json();
    }

    public function uploadWhatsAppFile($file)
    {
        $apiUrl = $this->baseUrl.$this->fbApp['client_id'].'/uploads?file_length='.$file->getSize().'&file_type='.$file->getMimeType();
        $response = Http::withToken(env('WABA_TOKEN'))->post($apiUrl);
        $sessionResponse = $response->json();
        Log::info("[Waba:uploadWhatsAppFile] $this->requestId, response: ".json_encode($response));
        if (isset($response->error) || ! isset($sessionResponse['id'])) {
            $error_response = $response->json();
            Log::error("[Waba:uploadWhatsAppFile] $this->requestId, Error: ".json_encode($error_response));

            throw new Exception($this->sendErrorResponse($error_response));
        }

        $uploadApiUrl = $this->baseUrl.$sessionResponse['id'];
        $uploadResponse = Http::withHeaders([
            'Authorization' => 'OAuth '.env('WABA_TOKEN'),
            'file_offset' => '0',
        ])->withBody(file_get_contents($file->getPathname()), 'application/octet-stream')->post($uploadApiUrl);
        $uploadResponseConverted = $uploadResponse->json();
        if (isset($uploadResponseConverted['error']) || ! isset($uploadResponseConverted['h'])) {
            Log::error("[Waba:uploadWhatsAppFile] $this->requestId, Error: ".json_encode($uploadResponseConverted));

            throw new Exception($this->sendErrorResponse($uploadResponseConverted));
        }

        return $uploadResponseConverted;
    }

    public function getPortalSettings()
    {
        return Setting::where('portal_id', $this->account->portal_id)->first();
    }

    public function sendText($options)
    {
        $data = [
            'messaging_product' => 'whatsapp',
            'recipient_type' => 'individual',
            'to' => $options['phone'],
            'type' => 'text',
            'text' => [
                'body' => $options['message'],
            ],
        ];
        if (isset($options['quotedMsgId'])) {
            $data['context'] = [
                'message_id' => $options['quotedMsgId'],
            ];
        }

        $this->request = json_encode($data);
        Log::info("[Waba:sendText] $this->requestId, request payload: ".$this->request);

        $response = Http::withToken(env('WABA_TOKEN'))->post($this->messageUrl, $data)->object();
        $this->response = json_encode($response);
        Log::info("[Waba:sendText] $this->requestId, response payload: ".$this->response);

        if (! isset($response->messages)) {
            return false;
        }

        $response->customData = $this->makeCustomData($response);

        return $response;
    }

    public function sendMedia($options)
    {
        if ($options['file_type'] == 'application') {
            $options['file_type'] = 'document';
        }

        $fileType = $options['file_type'];

        $filePayload = [
            'link' => $options['file_url'],
        ];

        if (! in_array($options['file_type'], ['image', 'video'])) {
            $filePayload['filename'] = $options['filename'];
        }

        $data = [
            'messaging_product' => 'whatsapp',
            'recipient_type' => 'individual',
            'to' => $options['phone'],
            'type' => $fileType,
            $fileType => $filePayload,
        ];

        if (isset($options['message']) && $fileType != 'audio') {// caption is not allowed for audio files
            $data[$fileType]['caption'] = $options['message'];
        }

        $this->request = json_encode($data);
        Log::info("[Waba:sendMedia] $this->requestId, request payload: ".$this->request);

        $response = Http::withToken(env('WABA_TOKEN'))->post($this->messageUrl, $data)->object();
        $this->response = json_encode($response);
        Log::info("[Waba:sendMedia] $this->requestId, response payload: ".$this->response);

        if (! isset($response->messages)) {
            return false;
        }

        $response->customData = $this->makeCustomData($response);

        return $response;
    }

    public function sendTemplate($options)
    {
        $data = [
            'messaging_product' => 'whatsapp',
            'recipient_type' => 'individual',
            'to' => $options['phone'],
            'type' => 'template',
            'template' => [
                'name' => $options['template']->name,
                'language' => [
                    'code' => $options['template']->language,
                ],
            ],
        ];

        $components = [];
        if (isset($options['header_text_1'])) {
            $components[] = [
                'type' => 'header',
                'parameters' => [
                    [
                        'type' => 'text',
                        'text' => $options['header_text_1'],
                    ],
                ],
            ];
        }

        if (isset($options['params'])) {
            $components[] = [
                'type' => 'body',
                'parameters' => $options['params'],
            ];
        }

        $components && $data['template']['components'] = $components;
        $this->request = json_encode($data);
        Log::info("[Waba:sendTemplate] $this->requestId, request payload: ".$this->request);

        $response = Http::withToken(env('WABA_TOKEN'))->post($this->messageUrl, $data)->object();
        $this->response = json_encode($response);
        Log::info("[Waba:sendTemplate] $this->requestId, response payload: ".$this->response);

        if (! isset($response->messages)) {
            return false;
        }

        $response->customData = $this->makeCustomData($response);

        return $response;
    }

    public function sendMediaTemplate($options)
    {
        $data = [
            'messaging_product' => 'whatsapp',
            'recipient_type' => 'individual',
            'to' => $options['phone'],
            'type' => 'template',
            'template' => [
                'name' => $options['template']->name,
                'language' => [
                    'code' => $options['template']->language,
                ],
            ],
        ];

        $components = [];
        // handle files
        if (isset($options['template']->header)) {
            $header = [];
            $headerType = strtolower($options['template']->header->format);
            if ($headerType == 'location') {
                $location = [];
                isset($options['latitude']) && $location['latitude'] = $options['latitude'];
                isset($options['longitude']) && $location['longitude'] = $options['longitude'];
                isset($options['location_name']) && $location['name'] = $options['location_name'];
                isset($options['location_address']) && $location['address'] = $options['location_address'];
                $header['location'] = $location;
            } else {
                $link = $options[$headerType.'_url'] ?? null;
                if ($link) {
                    $link && $headerTypeData = ['link' => $link];
                    if ($headerType == 'document' && isset($headerTypeData['link'])) {
                        $headerTypeData['filename'] = basename($headerTypeData['link']);
                    }
                    $header[$headerType] = $headerTypeData;
                }
            }

            ($headerType != 'text') && $components[] = ['type' => 'header', 'parameters' => [
                array_merge(['type' => $headerType], $header),
            ]];
        }

        // handle params
        if (isset($options['params'])) {
            $params = [
                'type' => 'body',
                'parameters' => $options['params'],
            ];
            $components[] = $params;
        }

        // handle buttons
        $buttonFlowId = null;
        if (isset($options['template']->buttons)) {
            foreach ($options['template']->buttons as $index => $buttonData) {
                $button = [];
                if ($buttonData->type == 'FLOW') {
                    $button = [
                        'type' => 'button',
                        'index' => $index,
                        'sub_type' => 'flow',
                        'parameters' => [
                            [
                                'type' => 'text',
                                'text' => $buttonData->text,
                            ],
                        ],
                    ];
                    $buttonFlowId = $buttonData->flow_id ?? null;
                }

                if ($buttonData->type == 'URL') {
                    $paramCount = preg_match_all('/\{\{\d+\}\}/', $buttonData->url);
                    if (! $paramCount) {
                        continue;
                    }

                    $button = [
                        'type' => 'button',
                        'index' => $index,
                        'sub_type' => 'url',
                        'parameters' => [
                            [
                                'type' => 'text',
                                'text' => $options['dynamic_button'] ?? '',
                            ],
                        ],
                    ];
                }
                $button && ($components[] = $button);
            }
        }
        $data['template']['components'] = $components;
        $this->request = json_encode($data);
        Log::info("[Waba:sendMediaTemplate] $this->requestId, request payload: ".$this->request);

        $response = Http::withToken(env('WABA_TOKEN'))->post($this->messageUrl, $data)->object();
        $this->response = json_encode($response);
        Log::info("[Waba:sendMediaTemplate] $this->requestId, response payload: ".$this->response);

        if (! isset($response->messages)) {
            return false;
        }

        $response->customData = $this->makeCustomData($response);
        $buttonFlowId && $response->customData['flowId'] = $buttonFlowId;

        return $response;
    }

    public function subscribeToWebhook($id)
    {
        $apiUrl = $this->baseUrl.$id.'/subscribed_apps';
        $response = Http::withToken($this->fbApp['token'])->post($apiUrl)->object();

        $this->response = json_encode($response);
        $success = $response->success ?? false;

        if (! $success) {
            Log::error("[Waba:subscribeToWebhook] $this->requestId, id: $id, Error: ".$this->response);

            return false;
        }

        return true;
    }

    public function getMedia($media, $hsApp)
    {
        if (get_class($media) != 'Netflie\WhatsAppCloudApi\WebHook\Notification\Media') {
            Log::info("[Waba:getMedia] $this->requestId, Invalid class");

            return false;
        }

        try {
            $whatsApp = new WhatsAppCloudApi([
                'from_phone_number_id' => $this->account->waba_phone_id,
                'access_token' => env('WABA_TOKEN'),
                'graph_version' => $this->fbApp['version'],
            ]);
            $response = $whatsApp->downloadMedia($media->imageId());

            $temp = tmpfile();
            fwrite($temp, $response->body());
            $path = stream_get_meta_data($temp)['uri'];

            $mimeType = explode('/', $media->mimeType());
            $file = (object) [
                'path' => $path,
                'mimeType' => $media->mimeType(),
                'filename' => $media->imageId().'-'.time().'.'.($mimeType[1] ?? ''),
            ];
            $file = $hsApp->uploadFile($file, 'waba');
            fclose($temp);
            if (! $file) {
                throw new Exception('file not found', 1); // NOSONAR
            }

            return $file;
        } catch (Exception $e) {
            Log::error("[Waba:getMedia] $this->requestId, Exception: ".$e->getMessage()); // NOSONAR

            return false;
        }
    }

    public function register($wabaPhoneId, $pin, $region = null)
    {
        $apiUrl = $this->baseUrl.$wabaPhoneId.'/register';

        $payload = [
            'pin' => $pin,
            'messaging_product' => 'whatsapp',
        ];

        // Include region only if provided (e.g. for migrated numbers from India)
        if ($region) {
            $payload['data_localization_region'] = $region;
        }

        $response = Http::withToken(env('WABA_TOKEN'))->post($apiUrl, $payload)->object();

        Log::info("[Waba:register] $this->requestId, response: ".json_encode($response));

        if (! $response) {
            return false;
        }

        if (isset($response->error)
            && isset($response->error->error_data)
            && isset($response->error->error_data->details)
        ) {
            $this->error = $response->error->error_data->details;
        }

        return $response->success ?? false;
    }

    public function fetchSettings($wabaPhoneId)
    {
        $apiUrl = $this->baseUrl."{$wabaPhoneId}/settings";
        $response = Http::withToken(env('WABA_TOKEN'))
            ->get($apiUrl)
            ->object();

        Log::info("[Waba:fetchSettings] $this->requestId, response: ".json_encode($response));

        return $response;
    }

    public function fetchFlowAsset($flowId, $withFlowJson = true)
    {
        $apiUrl = $this->baseUrl.$flowId.'/assets';

        try {
            $response = Http::withToken(env('WABA_TOKEN'))->get($apiUrl)->object();
            if (! isset($response->data)) {
                throw new Exception('data not found', 1); // NOSONAR
            }

            if (! $withFlowJson) {
                return $response->data;
            }

            $jsonDownloadUrl = $response->data[0]->download_url ?? null;
            if (! $jsonDownloadUrl) {
                throw new Exception('No jsonDownloadUrl found', 1); // NOSONAR
            }

            return Http::get($jsonDownloadUrl)->object();
        } catch (Exception $e) {
            Log::error("[Waba:fetchFlowAsset] $this->requestId, Exception: ".$e->getMessage());

            return null;
        }
    }

    public function makeCustomData($response)
    {
        return [
            'from' => $this->account->waba_phone,
            'messageId' => $response->messages[0]->id,
            'waba_id' => $response->contacts[0]->wa_id ?? null,
        ];
    }
}
