<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Helpers\Func;
use App\Models\HubList;
use Illuminate\Console\Command;
use App\Jobs\ProcessCampaignJob;

class ScheduleCampaignCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'schedule:campaign';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Enable campaigns that have reached their scheduled time';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Fetch campaigns where schedule_time is not null and enabled is 0
        $campaigns = HubList::whereNotNull('schedule_time')
            ->where('enabled', 0)
            ->get();

        foreach ($campaigns as $campaign) {
            // Adjust the schedule time based on the campaign's timezone
            $tzInput = $campaign->timezone;

            if (is_numeric($tzInput)) {
                // It's an offset like -300 or 330
                $timezone = Func::getTimeZone((int) $tzInput);
            } else {
                // Assume it's a timezone string like "Asia/Kolkata"
                $timezone = $tzInput;
            }
            $scheduleTime = Carbon::parse($campaign->schedule_time, $timezone);

            // Check if the schedule time is less than or equal to the current time
            if (Carbon::now($campaign->timezone)->greaterThanOrEqualTo($scheduleTime)) {
                // Enable the campaign
                $campaign->enabled = 1;
                $campaign->save();

                dispatch(new ProcessCampaignJob($campaign->id))->onQueue('lists');
                $this->info("Campaign {$campaign->id} is now enabled.");
            }
        }
    }
}
