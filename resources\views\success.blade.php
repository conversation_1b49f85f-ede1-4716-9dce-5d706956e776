@extends('layouts.new')
@section('title')
	{{$title}}
@endsection

@section('content')
	<header>
	    <div class="container">
	        <div class="logo_main">
	            <a href="/">
	            	<img src="/img/niswey-logo.png" width="60" alt="niswey-logo" />
	            </a>
	        </div>
	    </div>
	</header>

	<section id="content" class="container">
	    <div class="flex-container">
	        <div class="leftcontent">
	            <h1>{{env('APP_NAME')}} Create the buzz around your business</h1>
	            <p class="paratext">
	            	Integrate your WhatsApp Business Accounts (WABA) with HubSpot. Send out WhatsApp marketing campaigns to your HubSpot contacts lists, track all WhatsApp conversations in HubSpot, and do it all on the free HubSpot plan or enterprise, or anything in between
				</p>
	        </div>
	        <div class="rightcontent">
	        	<img src="/img/authorize.png" alt="{{env('APP_NAME')}}" />
	        </div>
	    </div>
	</section>
	<section class="container margin_top pb-5">
	    <div class="custom-progress-bar">
	        <div class="custom-progress-step  custom-progress-step-active custom-progress-step-complete" data-title="AUTHORIZE"></div>
	        <div class="custom-progress-connector active"></div>
	        <div class="custom-progress-step custom-progress-step-active custom-progress-step-complete" data-title="WHATSAPP CREDENTIALS"></div>
	        <div class="custom-progress-connector active"></div>
	        <div class="custom-progress-step" data-title="Registration"></div>
	    </div>
	    <div class="accwrap">
	        <button class="accordion disabled completed">Authorize your HubSpot portal</button>
	        <button class="accordion disabled completed">Enter your WhatsApp credentials</button>
	        <button class="accordion active">Registration</button>
	        <div class="panel active">
	        	<div class="panelbox">
	        		<div class="container-sm">
						<h4>
							Register your WhatsApp number
						</h4>

						<div class="portal-info mb-2">
							@if(!empty($portalId))
								<div>
									If your verified business phone number already has two-step verification enabled, set this value to your number's 6-digit two-step verification PIN. If you cannot recall your PIN, you can update it.
								</div>
								<br />
								<div>
									If your verified business phone number does not have two-step verification enabled, set this value to a 6-digit number. This will be the newly verified business phone number's two-step verification PIN.
								</div>
								<div>
									read more <a href="https://developers.facebook.com/docs/whatsapp/cloud-api/reference/registration/" target="_blank">here</a>
								</div>
								<div class="row">
									<div class="col-md-6">
										<form class="mt-3" method="post" onsubmit="width">
											@csrf
											<div class="form-group mb-3">
												<label class="form-label" for="2pin">PIN</label>
												<input required type="number" class="form-control" id="2pin" name="pin">
											</div>
											<input type="hidden" name="portalId" value="{{$portalId}}" />
											<input type="hidden" name="wabaPhoneId" value="{{$wabaPhoneId}}" />
											<div class="form-group text-center">
												<input type="submit" class="btn btn-primary" value="Register">
											</div>
										</form>
									</div>
								</div>
							@else
								{{$message}}
							@endif
						</div>
					</div>
	        	</div>
	        </div>
	    </div>
	</section>

	<script type="text/javascript">
		window.onload = () => {
			let activeAccordion = document.querySelector('.accordion.active');
			activeAccordion && activeAccordion.scrollIntoView({ block: 'start',  behavior: 'smooth' });
		}
	</script>

@endsection('content')
