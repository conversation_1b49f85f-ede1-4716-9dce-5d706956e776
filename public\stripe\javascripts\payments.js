/**
 * payments.js
 * Stripe Payments Demo. Created by <PERSON><PERSON> (@romainhuet)
 * and <PERSON><PERSON> (@thorwebdev).
 *
 * This modern JavaScript file handles the checkout process using Stripe.
 *
 * 1. It shows how to accept card payments with the `card` Element, and
 * the `paymentRequestButton` Element for Payment Request and Apple Pay.
 * 2. It shows how to use the Stripe Sources API to accept non-card payments,
 * such as iDEAL, SOFORT, SEPA Direct Debit, and more.
 */

(async () => {
	"use strict";

	// Retrieve the configuration for the store.
	const config = await store.getConfig();

	// Create references to the main form and its submit button.
	const form = document.getElementById("payment-form");
	const submitButton = form.querySelector("button[type=submit]");

	// Global variable to store the submit button text.
	let submitButtonPayText = "Pay";

	const updateSubmitButtonPayText = (newText) => {
		submitButton.textContent = newText;
		submitButtonPayText = newText;
	};
	// Global variable to store the PaymentIntent object.
	let paymentIntent;

	/**
	 * Setup Stripe Elements.
	 */

	// Create a Stripe client.
	const stripe = Stripe(config.publishableKey);

	// Create an instance of Elements.
	const elements = stripe.elements();

	// Prepare the styles for Elements.
	const style = {
		base: {
			iconColor: "#666ee8",
			color: "#31325f",
			fontWeight: 400,
			fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
			fontSmoothing: "antialiased",
			fontSize: "15px",
			"::placeholder": {
				color: "#aab7c4",
			},
			":-webkit-autofill": {
				color: "#666ee8",
			},
		},
	};

	/**
	 * Implement a Stripe Card Element that matches the look-and-feel of the app.
	 *
	 * This makes it easy to collect debit and credit card payments information.
	 */

	// Create a Card Element and pass some custom styles to it.
	const card = elements.create("card", { style, hidePostalCode: true });

	// Mount the Card Element on the page.
	card.mount("#card-element");

	// Monitor change events on the Card Element to display any errors.
	card.on("change", ({ error }) => {
		const cardErrors = document.getElementById("card-errors");
		if (error) {
			cardErrors.textContent = error.message;
			cardErrors.classList.add("visible");
		} else {
			cardErrors.classList.remove("visible");
		}
		// Re-enable the Pay button.
		submitButton.disabled = false;
	});

	/**
	 * Implement a Stripe IBAN Element that matches the look-and-feel of the app.
	 *
	 * This makes it easy to collect bank account information.
	 */

	// Create a IBAN Element and pass the right options for styles and supported countries.
	const ibanOptions = {
		style,
		supportedCountries: ["SEPA"],
	};
	const iban = elements.create("iban", ibanOptions);

	// Mount the IBAN Element on the page.
	iban.mount("#iban-element");

	// Monitor change events on the IBAN Element to display any errors.
	iban.on("change", ({ error, bankName }) => {
		const ibanErrors = document.getElementById("iban-errors");
		if (error) {
			ibanErrors.textContent = error.message;
			ibanErrors.classList.add("visible");
		} else {
			ibanErrors.classList.remove("visible");
			if (bankName) {
				updateButtonLabel("sepa_debit", bankName);
			}
		}
		// Re-enable the Pay button.
		submitButton.disabled = false;
	});

	/**
	 * Add an iDEAL Bank selection Element that matches the look-and-feel of the app.
	 *
	 * This allows you to send the customer directly to their iDEAL enabled bank.
	 */

	// Create a iDEAL Bank Element and pass the style options, along with an extra `padding` property.
	const idealBank = elements.create("idealBank", {
		style: { base: Object.assign({ padding: "10px 15px" }, style.base) },
	});

	// Mount the iDEAL Bank Element on the page.
	idealBank.mount("#ideal-bank-element");

	// Listen to changes to the user-selected country.
	form.querySelector("select[name=country]").addEventListener("change", (event) => {
		event.preventDefault();
		selectCountry(event.target.value);
	});

	// Submit handler for our payment form.
	form.addEventListener("submit", async (event) => {
		event.preventDefault();

		// Retrieve the user information from the form.
		const payment = form.querySelector("input[name=payment]:checked").value;
		const name = form.querySelector("input[name=name]").value;
		const country = form.querySelector("select[name=country] option:checked").value;
		const email = form.querySelector("input[name=email]").value;
		const phone = form.querySelector("input[name=phone]").value;
		const hubspotEmail = form.querySelector("input[name=hubspotEmail]").value;
		const portalId = form.querySelector("input[name=portalId]").value;
		const data = {
			name,
			email,
			phone,
			address: {
				line1: form.querySelector("input[name=address]").value,
				city: form.querySelector("input[name=city]").value,
				postal_code: form.querySelector("input[name=postal_code]").value,
				state: form.querySelector("input[name=state]").value,
				country,
			},
		};
		// Disable the Pay button to prevent multiple click events.
		submitButton.disabled = true;
		submitButton.textContent = "Processing…";

		stripe
		.createPaymentMethod({
			type: "card",
			card: card,
			billing_details: data,
		})
		.then((result) => {
			if (result.error) {
				//displayError(result);
				throw(result.error);
			} else {
				data['paymentMethodId'] = result.paymentMethod.id;
				data['priceId'] = PRICE_ID;
				data['portalId'] = portalId;
				data['hubspotEmail'] = hubspotEmail;
				REFERRED_BY && (data['referred_by'] = REFERRED_BY);
				// Create the subscription
				createSubscription(data);
			}
		})
		.catch((error) => {
			// An error has happened. Display the failure to the user here.
			// We utilize the HTML element we created.
			console.log(error)

			const mainElement = document.getElementById("main");
			const confirmationElement = document.getElementById("confirmation");
			confirmationElement.querySelector(".error-message").innerText = error.message;

			mainElement.classList.remove('checkout');
			mainElement.classList.add('error');
		})
	});

	function handlePaymentThatRequiresCustomerAction({ subscription, invoice, priceId, paymentMethodId, isRetry }) {
		if (subscription && subscription.status === "active") {
			// subscription is active, no customer actions required.
			return { subscription, priceId, paymentMethodId };
		}

		if(subscription && subscription.status == 'error') {
			throw subscription;
		}	
		// If it's a first payment attempt, the payment intent is on the subscription latest invoice.
		// If it's a retry, the payment intent will be on the invoice itself.
		let paymentIntent = invoice ? invoice.payment_intent : subscription.latest_invoice.payment_intent;

		if (paymentIntent && (paymentIntent.status === "requires_action" || (isRetry === true && paymentIntent.status === "requires_payment_method"))) {
			return stripe
				.confirmCardPayment(paymentIntent.client_secret, {
					payment_method: paymentMethodId,
				})
				.then((result) => {
					if (result.error) {
						// start code flow to handle updating the payment details
						// Display error message in your UI.
						// The card was declined (i.e. insufficient funds, card has expired, etc)
						throw result;
					} else {
						if (result.paymentIntent.status === "succeeded") {
							// There's a risk of the customer closing the window before callback
							// execution. To handle this case, set up a webhook endpoint and
							// listen to invoice.paid. This webhook endpoint returns an Invoice.
							return {
								priceId: priceId,
								subscription: subscription,
								invoice: invoice,
								paymentMethodId: paymentMethodId,
							};
						}
					}
				});
		} else {
			// No customer action needed
			return { subscription, priceId, paymentMethodId };
		}
	}

	function handleRequiresPaymentMethod({ subscription, paymentMethodId, priceId }) {
		if (subscription.status === "active") {
			// subscription is active, no customer actions required.
			return { subscription, priceId, paymentMethodId };
		} else if (subscription.latest_invoice.payment_intent && subscription.latest_invoice.payment_intent.status === "requires_payment_method") {
			// Using localStorage to store the state of the retry here
			// (feel free to replace with what you prefer)
			// Store the latest invoice ID and status
			localStorage.setItem("latestInvoiceId", subscription.latest_invoice.id);
			localStorage.setItem("latestInvoicePaymentIntentStatus", subscription.latest_invoice.payment_intent.status);
			throw { error: { message: "Your card was declined." } };
		} else {
			return { subscription, priceId, paymentMethodId };
		}
	}

	function onSubscriptionComplete(result) {
		if(typeof result.subscription.metadata.portalId !== 'undefined'){
			window.location.href = 'https://www.niswey.com/thank-you-for-purchasing-vira-whatsapp-automation';
		}else{
			console.log('there was an issue');
			const mainElement = document.getElementById("main");
			const confirmationElement = document.getElementById("confirmation");
			confirmationElement.querySelector(".error-message").innerText ="Could not create subscription.Please try again.";

			mainElement.classList.remove('checkout');
			mainElement.classList.add('error');
		}
		return;
	}

	const createSubscription = (data) => {
		return (
			fetch(APP_URL+"/api/stripe/create-subscription", {
				method: "post",
				headers: {
					"Content-type": "application/json",
				},
				body: JSON.stringify(data),
			})
			.then((response) => {
				return response.json();
			})
			// If the card is declined, display an error to the user.
			.then((result) => {
				if (result.error) {
					// The card had an error when trying to attach it to a customer
					throw result;
				}
				return result;
			})
			// Normalize the result to contain the object returned
			// by Stripe. Add the addional details we need.
			.then((result) => {
				return {
					// Use the Stripe 'object' property on the
					// returned result to understand what object is returned.
					subscription: result,
					paymentMethodId: data.paymentMethodId,
					priceId: data.priceId,
				};
			})
			// Some payment methods require a customer to do additional
			// authentication with their financial institution.
			// Eg: 2FA for cards.
			.then((result) => handlePaymentThatRequiresCustomerAction(result))
			// If attaching this card to a Customer object succeeds,
			// but attempts to charge the customer fail. You will
			// get a requires_payment_method error.
			.then((result) => handleRequiresPaymentMethod(result))
			// No more actions required. Provision your service for the user.
			.then((result) => onSubscriptionComplete(result))
			.catch((error) => {
				// An error has happened. Display the failure to the user here.
				// We utilize the HTML element we created.
				console.log(error)

				const mainElement = document.getElementById("main");
				const confirmationElement = document.getElementById("confirmation");
				confirmationElement.querySelector(".error-message").innerText = error.message;

				mainElement.classList.remove('checkout');
				mainElement.classList.add('error');
			})
		);
	}

	// Handle new PaymentIntent result
	const handlePayment = (paymentResponse) => {
		const { paymentIntent, error } = paymentResponse;

		const mainElement = document.getElementById("main");
		const confirmationElement = document.getElementById("confirmation");

		if (error && error.type === "validation_error") {
			mainElement.classList.remove("processing");
			mainElement.classList.remove("receiver");
			submitButton.disabled = false;
			submitButton.textContent = submitButtonPayText;
		} else if (error) {
			mainElement.classList.remove("processing");
			mainElement.classList.remove("receiver");
			confirmationElement.querySelector(".error-message").innerText = error.message;
			mainElement.classList.add("error");
		} else if (paymentIntent.status === "succeeded") {
			// Success! Payment is confirmed. Update the interface to display the confirmation screen.
			mainElement.classList.remove("processing");
			mainElement.classList.remove("receiver");
			// Update the note about receipt and shipping (the payment has been fully confirmed by the bank).
			confirmationElement.querySelector(".note").innerText = "We just sent your receipt to your email address, and your items will be on their way shortly.";
			mainElement.classList.add("success");
		} else if (paymentIntent.status === "processing") {
			// Success! Now waiting for payment confirmation. Update the interface to display the confirmation screen.
			mainElement.classList.remove("processing");
			// Update the note about receipt and shipping (the payment is not yet confirmed by the bank).
			confirmationElement.querySelector(".note").innerText = "We’ll send your receipt and ship your items as soon as your payment is confirmed.";
			mainElement.classList.add("success");
		} else {
			// Payment has failed.
			mainElement.classList.remove("success");
			mainElement.classList.remove("processing");
			mainElement.classList.remove("receiver");
			mainElement.classList.add("error");
		}
	};

	// Handle activation of payment sources not yet supported by PaymentIntents
	const handleSourceActiviation = (source) => {
		const mainElement = document.getElementById("main");
		const confirmationElement = document.getElementById("confirmation");
		switch (source.flow) {
			case "none":
				// Normally, sources with a `flow` value of `none` are chargeable right away,
				// but there are exceptions, for instance for WeChat QR codes just below.
				if (source.type === "wechat") {
					// Display the QR code.
					const qrCode = new QRCode("wechat-qrcode", {
						text: source.wechat.qr_code_url,
						width: 128,
						height: 128,
						colorDark: "#424770",
						colorLight: "#f8fbfd",
						correctLevel: QRCode.CorrectLevel.H,
					});
					// Hide the previous text and update the call to action.
					form.querySelector(".payment-info.wechat p").style.display = "none";
					let amount = store.formatPrice(store.getPaymentTotal(), config.currency);
					updateSubmitButtonPayText(`Scan this QR code on WeChat to pay ${amount}`);
					// Start polling the PaymentIntent status.
					pollPaymentIntentStatus(paymentIntent.id, 300000);
				} else {
					console.log("Unhandled none flow.", source);
				}
				break;
			case "redirect":
				// Immediately redirect the customer.
				submitButton.textContent = "Redirecting…";
				window.location.replace(source.redirect.url);
				break;
			case "code_verification":
				// Display a code verification input to verify the source.
				break;
			case "receiver":
				// Display the receiver address to send the funds to.
				mainElement.classList.add("success", "receiver");
				const receiverInfo = confirmationElement.querySelector(".receiver .info");
				let amount = store.formatPrice(source.amount, config.currency);
				switch (source.type) {
					case "ach_credit_transfer":
						// Display the ACH Bank Transfer information to the user.
						const ach = source.ach_credit_transfer;
						receiverInfo.innerHTML = `
			  <ul>
				<li>
				  Amount:
				  <strong>${amount}</strong>
				</li>
				<li>
				  Bank Name:
				  <strong>${ach.bank_name}</strong>
				</li>
				<li>
				  Account Number:
				  <strong>${ach.account_number}</strong>
				</li>
				<li>
				  Routing Number:
				  <strong>${ach.routing_number}</strong>
				</li>
			  </ul>`;
						break;
					case "multibanco":
						// Display the Multibanco payment information to the user.
						const multibanco = source.multibanco;
						receiverInfo.innerHTML = `
			  <ul>
				<li>
				  Amount (Montante):
				  <strong>${amount}</strong>
				</li>
				<li>
				  Entity (Entidade):
				  <strong>${multibanco.entity}</strong>
				</li>
				<li>
				  Reference (Referencia):
				  <strong>${multibanco.reference}</strong>
				</li>
			  </ul>`;
						break;
					default:
						console.log("Unhandled receiver flow.", source);
				}
				// Poll the PaymentIntent status.
				pollPaymentIntentStatus(paymentIntent.id);
				break;
			default:
				// Customer's PaymentIntent is received, pending payment confirmation.
				break;
		}
	};

	/**
	 * Monitor the status of a source after a redirect flow.
	 *
	 * This means there is a `source` parameter in the URL, and an active PaymentIntent.
	 * When this happens, we'll monitor the status of the PaymentIntent and present real-time
	 * information to the user.
	 */

	const pollPaymentIntentStatus = async (paymentIntent, timeout = 30000, interval = 500, start = null) => {
		start = start ? start : Date.now();
		const endStates = ["succeeded", "processing", "canceled"];
		// Retrieve the PaymentIntent status from our server.
		const rawResponse = await fetch(`payment_intents/${paymentIntent}/status`);
		const response = await rawResponse.json();
		if (!endStates.includes(response.paymentIntent.status) && Date.now() < start + timeout) {
			// Not done yet. Let's wait and check again.
			setTimeout(pollPaymentIntentStatus, interval, paymentIntent, timeout, interval, start);
		} else {
			handlePayment(response);
			if (!endStates.includes(response.paymentIntent.status)) {
				// Status has not changed yet. Let's time out.
				console.warn(new Error("Polling timed out."));
			}
		}
	};

	const url = new URL(window.location.href);
	const mainElement = document.getElementById("main");
	if (url.searchParams.get("source") && url.searchParams.get("client_secret")) {
		// Update the interface to display the processing screen.
		mainElement.classList.add("checkout", "success", "processing");

		const { source } = await stripe.retrieveSource({
			id: url.searchParams.get("source"),
			client_secret: url.searchParams.get("client_secret"),
		});

		// Poll the PaymentIntent status.
		pollPaymentIntentStatus(source.metadata.paymentIntent);
	} else if (url.searchParams.get("payment_intent")) {
		// Poll the PaymentIntent status.
		pollPaymentIntentStatus(url.searchParams.get("payment_intent"));
	} else {
		// Update the interface to display the checkout form.
		mainElement.classList.add("checkout");

		// Create the PaymentIntent with the cart details.
		const response = await store.createPaymentIntent(config.currency, store.getLineItems());
		paymentIntent = response.paymentIntent;
	}
	document.getElementById("main").classList.remove("loading");

	/**
	 * Display the relevant payment methods for a selected country.
	 */

	// List of relevant countries for the payment methods supported in this demo.
	// Read the Stripe guide: https://stripe.com/payments/payment-methods-guide
	const paymentMethods = {
		ach_credit_transfer: {
			name: "Bank Transfer",
			flow: "receiver",
			countries: ["US"],
			currencies: ["usd"],
		},
		alipay: {
			name: "Alipay",
			flow: "redirect",
			countries: ["CN", "HK", "SG", "JP"],
			currencies: ["aud", "cad", "eur", "gbp", "hkd", "jpy", "nzd", "sgd", "usd"],
		},
		bancontact: {
			name: "Bancontact",
			flow: "redirect",
			countries: ["BE"],
			currencies: ["eur"],
		},
		card: {
			name: "Card",
			flow: "none",
		},
		eps: {
			name: "EPS",
			flow: "redirect",
			countries: ["AT"],
			currencies: ["eur"],
		},
		ideal: {
			name: "iDEAL",
			flow: "redirect",
			countries: ["NL"],
			currencies: ["eur"],
		},
		giropay: {
			name: "Giropay",
			flow: "redirect",
			countries: ["DE"],
			currencies: ["eur"],
		},
		multibanco: {
			name: "Multibanco",
			flow: "receiver",
			countries: ["PT"],
			currencies: ["eur"],
		},
		sepa_debit: {
			name: "SEPA Direct Debit",
			flow: "none",
			countries: ["FR", "DE", "ES", "BE", "NL", "LU", "IT", "PT", "AT", "IE", "FI"],
			currencies: ["eur"],
		},
		sofort: {
			name: "SOFORT",
			flow: "redirect",
			countries: ["DE", "AT"],
			currencies: ["eur"],
		},
		wechat: {
			name: "WeChat",
			flow: "none",
			countries: ["CN", "HK", "SG", "JP"],
			currencies: ["aud", "cad", "eur", "gbp", "hkd", "jpy", "nzd", "sgd", "usd"],
		},
	};

	// Update the main button to reflect the payment method being selected.
	const updateButtonLabel = (paymentMethod, bankName) => {
		let amount = store.formatPrice(store.getPaymentTotal(), config.currency);
		let name = paymentMethods[paymentMethod].name;
		let label = `Pay ${amount}`;
		if (paymentMethod !== "card") {
			label = `Pay ${amount} with ${name}`;
		}
		if (paymentMethod === "wechat") {
			label = `Generate QR code to pay ${amount} with ${name}`;
		}
		if (paymentMethod === "sepa_debit" && bankName) {
			label = `Debit ${amount} from ${bankName}`;
		}
		updateSubmitButtonPayText(label);
	};

	const selectCountry = (country) => {
		const selector = document.getElementById("country");
		selector.querySelector(`option[value=${country}]`).selected = "selected";
		selector.className = `field ${country.toLowerCase()}`;

		// Trigger the methods to show relevant fields and payment methods on page load.
		showRelevantFormFields();
		showRelevantPaymentMethods();
	};

	// Show only form fields that are relevant to the selected country.
	const showRelevantFormFields = (country) => {
		if (!country) {
			country = form.querySelector("select[name=country] option:checked").value;
		}
		const zipLabel = form.querySelector("label.zip");
		// Only show the state input for the United States.
		zipLabel.parentElement.classList.toggle("with-state", ["AU", "US"].includes(country));
		// Update the ZIP label to make it more relevant for each country.
		const zipInput = form.querySelector("label.zip input");
		const zipSpan = form.querySelector("label.zip span");
		switch (country) {
			case "US":
				zipSpan.innerText = "ZIP";
				zipInput.placeholder = "94103";
				break;
			case "GB":
				zipSpan.innerText = "Postcode";
				zipInput.placeholder = "EC1V 9NR";
				break;
			case "AU":
				zipSpan.innerText = "Postcode";
				zipInput.placeholder = "3000";
				break;
			default:
				zipSpan.innerText = "Postal Code";
				zipInput.placeholder = "94103";
				break;
		}

		// Update the 'City' to appropriate name
		const cityInput = form.querySelector("label.city input");
		const citySpan = form.querySelector("label.city span");
		switch (country) {
			case "AU":
				citySpan.innerText = "City / Suburb";
				cityInput.placeholder = "Melbourne";
				break;
			default:
				citySpan.innerText = "City";
				cityInput.placeholder = "San Francisco";
				break;
		}
	};

	// Show only the payment methods that are relevant to the selected country.
	const showRelevantPaymentMethods = (country) => {
		if (!country) {
			country = form.querySelector("select[name=country] option:checked").value;
		}
		const paymentInputs = form.querySelectorAll("input[name=payment]");
		for (let i = 0; i < paymentInputs.length; i++) {
			let input = paymentInputs[i];
			input.parentElement.classList.toggle(
				"visible",
				input.value === "card" || (config.paymentMethods.includes(input.value) && paymentMethods[input.value].countries.includes(country) && paymentMethods[input.value].currencies.includes(config.currency))
			);
		}

		// Hide the tabs if card is the only available option.
		const paymentMethodsTabs = document.getElementById("payment-methods");
		paymentMethodsTabs.classList.toggle("visible", paymentMethodsTabs.querySelectorAll("li.visible").length > 1);

		// Check the first payment option again.
		paymentInputs[0].checked = "checked";
		form.querySelector(".payment-info.card").classList.add("visible");
		form.querySelector(".payment-info.ideal").classList.remove("visible");
		form.querySelector(".payment-info.sepa_debit").classList.remove("visible");
		form.querySelector(".payment-info.wechat").classList.remove("visible");
		form.querySelector(".payment-info.redirect").classList.remove("visible");
		// updateButtonLabel(paymentInputs[0].value);
	};

	// Listen to changes to the payment method selector.
	for (let input of document.querySelectorAll("input[name=payment]")) {
		input.addEventListener("change", (event) => {
			event.preventDefault();
			const payment = form.querySelector("input[name=payment]:checked").value;
			const flow = paymentMethods[payment].flow;

			// Update button label.
			updateButtonLabel(event.target.value);

			// Show the relevant details, whether it's an extra element or extra information for the user.
			form.querySelector(".payment-info.card").classList.toggle("visible", payment === "card");
			form.querySelector(".payment-info.ideal").classList.toggle("visible", payment === "ideal");
			form.querySelector(".payment-info.sepa_debit").classList.toggle("visible", payment === "sepa_debit");
			form.querySelector(".payment-info.wechat").classList.toggle("visible", payment === "wechat");
			form.querySelector(".payment-info.redirect").classList.toggle("visible", flow === "redirect");
			form.querySelector(".payment-info.receiver").classList.toggle("visible", flow === "receiver");
			document.getElementById("card-errors").classList.remove("visible", payment !== "card");
		});
	}

	// Select the default country from the config on page load.
	let country = config.country;
	// Override it if a valid country is passed as a URL parameter.
	const urlParams = new URLSearchParams(window.location.search);
	let countryParam = urlParams.get("country") ? urlParams.get("country").toUpperCase() : config.country;
	if (form.querySelector(`option[value="${countryParam}"]`)) {
		country = countryParam;
	}
	selectCountry(country);
})();
