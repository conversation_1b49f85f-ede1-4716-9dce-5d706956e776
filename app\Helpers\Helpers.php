<?php

if (! function_exists('utf8_converter')) {
    function utf8_converter($array)
    {
        array_walk_recursive($array, function (&$item, $key) {
            if (! mb_detect_encoding($item, 'utf-8', true)) {
                $item = utf8_encode($item);
            }
        });

        return $array;
    }
}

if (! function_exists('id2portal')) {
    function id2portal($user_id)
    {
        $user = explode('|', base64_decode($user_id));

        return $user[1] ?? false;
    }
}

if (! function_exists('id2email')) {
    function id2email($user_id)
    {
        $user = explode('|', base64_decode($user_id));

        return $user[0] ?? false;
    }
}

if (! function_exists('simple_crypt')) {
    function simple_crypt($action, $string)
    {
        $secret_key = 'waba-niswey';
        $secret_iv = 'XNOgR7lgAEHlUzy4QN48UgabNI2YJmM9j0Csxu8Ykgyo';

        $output = false;
        $encrypt_method = 'AES-256-CBC';
        $key = hash('sha256', $secret_key);
        $iv = substr(hash('sha256', $secret_iv), 0, 16);

        if ($action == 'e') {
            $output = base64_encode(openssl_encrypt($string, $encrypt_method, $key, 0, $iv));
        } elseif ($action == 'd') {
            $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
        }

        return $output;
    }
}

if (! function_exists('res')) {
    function res($status, $message = '', $data = [])
    {
        $response = ['status' => $status];
        if ($message) {
            $response['message'] = $message;
        }
        if ($data) {
            $response[$data[0]] = $data[1];
        }

        return response()->json($response);
    }
}

if (! function_exists('timeHash')) {
    function timeHash()
    {
        return simple_crypt('e', time());
    }
}

if (! function_exists('readUserId')) {

    function readUserId($userId, $type = 'id')
    {
        $indexes = [
            'id' => 0,
            'portalId' => 1,
            'phone' => 2,
            'time' => 3,
        ];

        $data = explode(':', simple_crypt('d', $userId));
        if ($type != 'account') {
            $value = $data[$indexes[$type]] ?? null;

            return in_array($type, ['id', 'portalId']) ? (int) $value : $value;
        }

        $portalId = $data[0] ?? null;
        $phone = $data[1] ?? null;

        return $portalId.'.'.$phone;
    }

}

if (! function_exists('property_consent')) {
    function property_consent($properties)
    {
        $container = [];
        foreach ($properties as $item) {
            $hidden = $item->hidden ?? null;
            $fieldType = $item->fieldType ?? null;
            $readOnlyValue = $item->readOnlyValue ?? null;

            if ($readOnlyValue === true || $hidden === true) {
                continue;
            }

            if ($fieldType != 'booleancheckbox') {
                continue;
            }

            $container[] = [
                'name' => $item->name,
                'label' => htmlspecialchars($item->label, ENT_QUOTES),
            ];
        }

        return $container;
    }
}

function encryptSodium($plaintext)
{
    $key = base64_decode(env('SODIUM_SECRET_KEY', ''));
    $nonce = random_bytes(SODIUM_CRYPTO_SECRETBOX_NONCEBYTES);
    $encrypted = sodium_crypto_secretbox($plaintext, $nonce, $key);

    return base64_encode($nonce.$encrypted);
}

function decryptSodium($encrypted)
{
    $key = base64_decode(env('SODIUM_SECRET_KEY', ''));
    $decoded = base64_decode($encrypted);
    $nonce = substr($decoded, 0, SODIUM_CRYPTO_SECRETBOX_NONCEBYTES);
    $ciphertext = substr($decoded, SODIUM_CRYPTO_SECRETBOX_NONCEBYTES);

    return sodium_crypto_secretbox_open($ciphertext, $nonce, $key);
}

function calculateProcessingTime($createdAt, $updatedAt)
{
    // Calculate the time difference in seconds for more precision
    if ($createdAt && $updatedAt && $createdAt <= $updatedAt) {
        $diffInSeconds = $createdAt->diffInSeconds($updatedAt);
    } else {
        $diffInSeconds = 0; // Default if timestamps are invalid
    }

    // Less than a minute
    if ($diffInSeconds < 60) {
        if ($diffInSeconds == 0) {
            return 'Less than a second';
        } elseif ($diffInSeconds == 1) {
            return '1 second';
        } else {
            return $diffInSeconds.' seconds';
        }
    }
    // Less than an hour
    elseif ($diffInSeconds < 3600) {
        $minutes = floor($diffInSeconds / 60);
        $seconds = $diffInSeconds % 60;

        $result = $minutes.' minute'.($minutes != 1 ? 's' : '');
        if ($seconds > 0) {
            $result .= ' '.$seconds.' second'.($seconds != 1 ? 's' : '');
        }

        return $result;
    }
    // Less than a day
    elseif ($diffInSeconds < 86400) {
        $hours = floor($diffInSeconds / 3600);
        $minutes = floor(($diffInSeconds % 3600) / 60);

        $result = $hours.' hour'.($hours != 1 ? 's' : '');
        if ($minutes > 0) {
            $result .= ' '.$minutes.' minute'.($minutes != 1 ? 's' : '');
        }

        return $result;
    }
    // More than a day
    else {
        $days = floor($diffInSeconds / 86400);
        $hours = floor(($diffInSeconds % 86400) / 3600);
        $minutes = floor(($diffInSeconds % 3600) / 60);

        $result = $days.' day'.($days != 1 ? 's' : '');
        if ($hours > 0) {
            $result .= ' '.$hours.' hour'.($hours != 1 ? 's' : '');
        }
        if ($minutes > 0 && $days < 2) { // Only show minutes if less than 2 days for readability
            $result .= ' '.$minutes.' minute'.($minutes != 1 ? 's' : '');
        }

        return $result;
    }
}
