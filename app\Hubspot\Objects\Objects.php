<?php

namespace App\Hubspot\Objects;

use Log;
use Exception;

class Objects
{
    public function __construct(
        protected $client,
        protected $type = '',
        protected $typeCap = '',
        protected string $requestId = '',
    ) {
        $this->typeCap = ucfirst($this->type);
        $this->requestId = $this->client->requestId;
    }

    protected function doSearch($data)
    {
        $groups = [];
        $filterClass = "HubSpot\\Client\\Crm\\{$this->typeCap}\\Model\\Filter";
        $filterGroupClass = "HubSpot\\Client\\Crm\\{$this->typeCap}\\Model\\FilterGroup";
        $requestClass = "HubSpot\\Client\\Crm\\{$this->typeCap}\\Model\\PublicObjectSearchRequest";
        $request = new $requestClass;

        foreach ($data['filters'] as $filter) {
            $filter = (object) $filter;

            $f = new $filterClass;
            $f->setOperator($filter->operator);
            $f->setPropertyName($filter->propertyName);
            $f->setValue($filter->value);

            $group = new $filterGroupClass;
            $group->setFilters([$f]);
            $groups[] = $group;
        }

        $request->setFilterGroups($groups);
        isset($data['properties']) && $request->setProperties($data['properties']);

        try {
            $response = $this->client->crm()
                ->{$this->type}()
                ->searchApi()
                ->doSearch($request);

            return json_decode(json_encode($response));
        } catch (Exception $e) {
            Log::error("[$this->type:search] $this->requestId, Exception: ".$e->getMessage());

            return null;
        }
    }

    protected function getAllProperties()
    {
        try {
            $response = $this->client->crm()->properties()->coreApi()->getAll($this->type);

            return json_decode(json_encode($response));
        } catch (Exception $e) {
            Log::error("[$this->type:getProperties] $this->requestId, Exception: ".$e->getMessage());

            return [];
        }
    }
}
