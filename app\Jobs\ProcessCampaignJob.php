<?php

namespace App\Jobs;

use Log;
use Exception;
use App\Helpers\Func;
use App\Models\HubList;
use App\Models\Setting;
use App\Hubspot\Hubspot;
use App\Models\Campaign;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class Process<PERSON>ampaignJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    /**
     * Create a new job instance.
     */

	public $timeout = 5 * 3600;

    protected $campaignId;

    protected $requestId;

    protected $countryCode = null;

    public function __construct($campaignId)
    {
        $this->campaignId = $campaignId;
        $this->requestId = round(microtime(true) * 1000).'-campaign-'.$campaignId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("[ProcessCampaignJob:handle] {$this->requestId}, processing: {$this->campaignId}");
        $campaign = HubList::find($this->campaignId);

        if (! $campaign || ! $campaign->enabled) {
            Log::info("[ProcessCampaignJob] $this->requestId, Campaign not found or not enabled: {$this->campaignId}");

            return;
        }

        $this->processEntireCampaign($campaign);
    }

    protected function processEntireCampaign(HubList $campaign)
    {
        $hsApp = new Hubspot($campaign->portal_id, $this->requestId);
        $campaignData = json_decode($campaign->data);
        $properties = $this->extractProperties($campaignData);

        // Add consent property if configured
        $consentProperty = $this->getConsentProperty($campaign->portal_id);
        if (! $consentProperty) {
            Log::info("[ProcessCampaignJob:processEntireCampaign] $this->requestId, No consent property found");

            return;
        }

        $properties[] = $consentProperty;

        // Initialize pagination variables
        $hasMore = true;
        $processedCount = $campaign->processed ?: 0;
        $offset = $campaign->offset ?: null;

        try {
            // Process contacts in batches until no more are available
            while ($hasMore) {
                // Fetch contacts in list
                $listMembership = $hsApp->lists()->fetchListMembership(
                    $campaign->portal_id,
                    $campaign->listId,
                    $offset
                );

                if (empty($listMembership) || empty($listMembership['results'])) {
                    Log::info("[ProcessCampaignJob] $this->requestId, No results found for campaign: {$this->campaignId}");
                    break;
                }

                // Extract record IDs
                $recordIds = $this->extractRecordIds($listMembership['results']);

                // Filter out already processed contacts
                $filteredRecordIds = $this->filterProcessedContacts($recordIds, $campaign);

                if (count($filteredRecordIds) > 0) {
                    // Fetch contact details
                    $contacts = $hsApp->contacts()->fetchBatchContactList(
                        $filteredRecordIds,
                        $properties
                    );

                    if ($contacts) {
                        // Process each contact
                        foreach ($contacts as $contact) {
                            // Create a fresh copy of campaign data for each contact
                            $contactCampaignData = json_decode($campaign->data);
                            $this->processContact($contact, $campaign, $contactCampaignData, $consentProperty, $properties);
                            $processedCount++;
                        }
                    }
                }

                // Check for more pages
                $hasMore = isset($listMembership['paging']['next']);
                if ($hasMore) {
                    $offset = $listMembership['paging']['next']['after'];
                }

                // Update campaign progress
                $campaign->offset = $offset;
                $campaign->last_processed = time();
                $campaign->processed += count($filteredRecordIds);
                $campaign->save();
            }
            // Update campaign when complete
            $campaign->more = false;
            $campaign->save();

            Log::info("[ProcessCampaignJob] Completed processing campaign: {$this->campaignId}, processed: {$processedCount} contacts");

        } catch (Exception $e) {
            Log::error("[ProcessCampaignJob] Error processing campaign: {$this->campaignId}, Error: {$e->getMessage()}, Trace: {$e->getTraceAsString()}");
        }

    }

    /**
     * Extract properties from campaign data.
     */
    protected function extractProperties(object $campaignData): array
    {
        $properties = [];

        if (! isset($campaignData->fields)) {
            return $properties;
        }

        foreach ($campaignData->fields as $key => $field) {
            if (preg_match("/\[(\w+)\]/", $field, $matches)) {
                $properties[] = $matches[1];
            }
        }

        return array_unique($properties);
    }

    /**
     * Get consent property for the portal.
     */
    protected function getConsentProperty(int $portalId): ?string
    {
        $setting = Setting::where('portal_id', $portalId)
            ->whereNotNull('consent_property')
            ->first();

        if (! $setting || ! $setting->consent_property) {
            Log::info("[ProcessCampaignJob] Consent property not found for portalId: {$portalId}");

            return null;
        }

        return $setting->consent_property;
    }

    /**
     * Extract record IDs from list membership results.
     */
    protected function extractRecordIds(array $results): array
    {
        return array_map(function ($result) {
            return $result['recordId'];
        }, $results);
    }

    /**
     * Filter already processed contacts.
     */
    protected function filterProcessedContacts(array $recordIds, HubList $campaign): array
    {
        // Get already processed contacts
        $existingIds = Campaign::where([
            'account_id' => $campaign->account_id,
            'campaign_id' => $campaign->id,
        ])->whereIn('object_id', $recordIds)->pluck('object_id')->toArray();

        // Return unique, unprocessed contacts
        return array_values(array_unique(array_diff($recordIds, $existingIds)));
    }

    /**
     * Process a single contact.
     */
    protected function processContact(object $contact, HubList $campaign, object $campaignData, ?string $consentProperty, array $properties): void
    {
        // Check consent
        if ($consentProperty) {
            $propertyValue = $contact->properties->{$consentProperty} ?? false;
            $hasConsent = filter_var($propertyValue, FILTER_VALIDATE_BOOLEAN);

            if (! $hasConsent) {
                Log::info("[ProcessCampaignJob] Consent not given for contact: {$contact->id}, campaign: {$campaign->id}");

                return;
            }
        }

        // Replace template variables with contact properties
        $this->replaceTemplateVariables($campaignData, $contact);

        // Process phone number with country code if needed
        $this->formatContactPhoneNumber($campaign->account_id, $contact);

        // Prepare data for message job
        $data = (object) [
            'campaignId' => $campaign->id,
            'accountId' => $campaign->account_id,
            'template' => $campaignData,
            'contact' => $contact,
            'properties' => $properties,
        ];

        // Dispatch to message queue
        dispatch(new CampaignMessageJob($data, $this->requestId))->onQueue('lists');

        Log::info("[ProcessCampaignJob] Dispatched message for contact: {$contact->id}, campaign: {$campaign->id}");
    }

    protected function formatContactPhoneNumber(int $accountId, object $contact): void
    {
        // Get phone number from contact properties
        $phone = $contact->properties->phone ?? $contact->properties->mobilephone ?? '';

        if (! $phone || substr($phone, 0, 1) === '+') {
            return; // No phone or already has country code
        }

        // Get cached country code or fetch it
        if ($this->countryCode === null) {
            $setting = Setting::where('account_id', $accountId)->first();
            if (! $setting || ! $setting->country_code) {
                $this->countryCode = ''; // Cache empty result
            } else {
                $countryCodeParts = explode(':', $setting->country_code);
                $this->countryCode = $countryCodeParts[0] ?? '';
            }
        }

        if (! empty($this->countryCode)) {
            // Format phone with country code
            $formattedPhone = Func::applyCountryCode($phone, $this->countryCode);
            $formattedPhone = Func::makeChatId($formattedPhone);

            // Update contact properties
            $contact->properties->phone = $formattedPhone;
            if (isset($contact->properties->mobilephone)) {
                $contact->properties->mobilephone = $formattedPhone;
            }
        }
    }

    /**
     * Replace template variables with contact properties.
     */
    protected function replaceTemplateVariables(object $campaignData, object $contact): void
    {
        foreach ($campaignData->fields as $key => $field) {
            if (preg_match("/\[(\w+)\]/", $field, $matches)) {
                $propertyName = $matches[1];
                $propertyValue = $contact->properties->{$propertyName} ?? '';

                if ($propertyValue) {
                    $campaignData->fields->{$key} = str_replace("[{$propertyName}]", $propertyValue, $field);
                }
            }
        }
    }
}
