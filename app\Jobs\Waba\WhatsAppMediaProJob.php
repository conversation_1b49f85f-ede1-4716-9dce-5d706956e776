<?php

namespace App\Jobs\Waba;

use Log;
use Exception;
use Throwable;
use App\Helpers\Func;
use App\Models\Dialog;
use App\Models\Message;
use App\Hubspot\Hubspot;
use App\Models\Workflow;
use App\Services\Whatsapp\TemplateService;
use App\Services\Whatsapp\WorkflowService;

class WhatsAppMediaProJob extends WhatsAppJob
{
    public function handle(): void
    {
        Log::info("[WhatsAppMediaProJob:handle] running for $this->requestId");
        extract($this->input);

        try {

            $options = [];
            $accountId = $fields['account'];
            $properties = $object['properties'] ?? [];
            $options = array_merge($options, $fields);
            $phone = $this->getPhoneFromProps($properties);
            $waba = new WorkflowService($accountId, $this->requestId);
            $consentGiven = filter_var($properties['whatsapp_consent_given'] ?? false, FILTER_VALIDATE_BOOLEAN);

            if (! $phone) {
                throw new Exception("contact doesn't have phone number", 1); // NOSONAR
            }

            if (! $consentGiven) {
                throw new Exception("contact doesn't have consent to send message", 1); // NOSONAR
            }

            // apply country code
            $portal = $waba->account;
            if (! $portal || $portal->paid < 1) {
                throw new Exception('Account is de-activated', 1); // NOSONAR
            }

            $portalSettings = $waba->getPortalSettings();
            if (substr($phone, 0, 1) != '+' && $portalSettings->country_code) {
                $countryCode = explode(':', $portalSettings->country_code)[0] ?? '';
                $countryCode && $phone = Func::applyCountryCode($phone, $countryCode);
            }
            $phone = $options['phone'] = Func::makeChatId($phone);

            $template = $waba->fetchTemplateById($fields['template']);
            if (! $template) {
                throw new Exception('template not found', 1); // NOSONAR
            }
            $templateService = new TemplateService($template);
            $template = $this->buildTemplateParams($template);

            $options['params'] = [];
            $options['templateData'] = [];
            $options['template'] = $template;

            $paramcount = intval($fields['paramcount']);
            if ($paramcount && is_int($paramcount)) {
                for ($i = 1; $i <= $paramcount; $i++) {
                    $indexKey = $paramcount;
                    $key = 'placeholder_'.$indexKey.'_'.$i;
                    $options['params'][] = ['type' => 'text', 'text' => $fields[$key]];
                    $options['templateData'][] = $fields[$key];
                }
            }

            $res = $waba->sendMediaTemplate($options);

            if ($res === false || ! isset($res->customData['messageId'])) {
                throw new Exception('unable to send text message', 1); // NOSONAR
            }

            // change phone to correct waba id
            $phone = $this->getWabaId($res) ?: $phone;

            $messageBody = $template->body;
            for ($i = 0; $i < count($options['templateData']); $i++) {
                $key = $i + 1;
                $messageBody = str_replace('{{'.$key.'}}', $options['templateData'][$i], $messageBody);
            }

            // save dialog
            $name = $this->getNameFromProps($properties);
            Dialog::updateOrCreate(['chatId' => $phone, 'account_id' => $accountId], [
                'object_id' => $object['objectId'],
                'phone' => $phone,
                'name' => $name,
                'time' => time(),
            ]);

            // save message
            $type = $fields['media_type'] ?? 'text';
            if (! in_array($type, ['image', 'audio', 'video', 'document'])) {
                $type = 'text';
            }

            $message = [
                'account_id' => $accountId,
                'id' => $res->customData['messageId'],
                'chatId' => $phone,
                'type' => strtolower($type),
                'body' => $messageBody,
                'from' => $res->customData['from'],
                'to' => $phone,
                'fromMe' => 1,
                'status' => 'sent',
                'time' => time(),
            ];
            $flowId = $res->customData['flowId'] ?? null;
            $flowId && $message['flow_id'] = $flowId;

            $urlKey = strtolower($type).'_url';
            $fileUrl = $fields[$urlKey] ?? null;
            $type && $message['file_type'] = strtolower($type);
            $fileUrl && $message['file_url'] = $fileUrl;
            $savedMessage = Message::create($message);

            // update hubspot timeline
            $hsApp = new Hubspot($portal->portal_id, $this->requestId);
            $hsApp->timeline()->update([
                'id' => $res->customData['messageId'],
                'objectId' => $object['objectId'],
                'data' => [
                    'status' => 'sent',
                    'phone' => $portal->waba_phone,
                    'message' => Func::messageToTimeline($savedMessage),
                ],
            ], true);

            $workflowParams = [
                'name' => $name,
                'phone' => $phone,
                'from' => $message['from'],
                'accountId' => $accountId,
                'messageId' => $message['id'],
                'object_id' => $object['objectId'],
                'template_id' => $template->id ?? null,
                'template_name' => $template->name ?? null,
                'messageType' => $message['type'],
                'provider_request' => $waba->request,
                'provider_response' => $waba->response,
            ];
            $workflowData = $this->prepareWorkflowData($workflowParams);

            // Create workflow record
            Workflow::create($workflowData);

            Log::info("[WhatsAppMediaProJob:handle] Success for $this->requestId");
        } catch (Exception $e) {
            $trace = $e->getTraceAsString();
            Log::error("[WhatsAppMediaProJob:handle] $this->requestId, Exception: ".$e->getMessage().', trace: '.$trace);
        }
    }

    public function failed(Throwable $e)
    {
        Log::error("[WhatsAppMediaProJob:failed] $this->requestId, Exception: ".$e->getMessage());
    }
}
