<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use App\Helpers\Waba;
use App\Models\FlowMapping;
use Illuminate\Support\Arr;
use App\Helpers\HelperTrait;
use Illuminate\Http\Request;
use App\Services\Whatsapp\FlowService;

class FlowController extends Controller
{
    use HelperTrait;

    public function __construct()
    {
        parent::__construct('waba');
    }

    public function index(Request $request)
    {
        $input = $request->validate(['user_id' => 'required']);
        $accountId = readUserId($input['user_id']);

        $mappings = FlowMapping::where('account_id', $accountId)->paginate(100);

        return $this->jsonOk(['data' => $mappings]);
    }

    public function store(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'id' => 'required',
            'name' => 'required',
            'mapping' => 'required',
            'portal_id' => 'required',
        ]);
        $data = Arr::except($input, ['user_id']);

        if (gettype($data['mapping']) !== 'string') {
            $data['mapping'] = json_encode($data['mapping']);
        }

        $data = FlowMapping::create($data);

        return $this->jsonOk(['data' => $data]);
    }

    public function update(Request $request, $flowId)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'name' => 'required',
            'mapping' => 'nullable',
            'portal_id' => 'required',
        ]);
        $mapping = $input['mapping'] ?? null;
        ! $mapping && $input['mapping'] = '';

        if (gettype($mapping) !== 'string') {
            $input['mapping'] = json_encode($mapping);
        }

        $accountId = readUserId($input['user_id']);
        $data = FlowMapping::updateOrCreate([
            'account_id' => $accountId,
            'id' => $flowId,
        ], Arr::except($input, ['user_id']));

        return $this->jsonOk(['data' => $data]);
    }

    public function flowTemplates(Request $request)
    {
        $input = $request->validate(['user_id' => 'required', 'search' => 'nullable']);
        $accountId = readUserId($input['user_id']);
        $search = $input['search'] ?? null;
        $filters = [
            'name' => $search,
            'parse' => true,
            'status' => 'APPROVED',
        ];
        try {

            $waba = new Waba($accountId, $this->requestId);
            $templates = $waba->fetchTemplatesWithFilters($filters);
            if (! $templates->data) {
                return $this->jsonOk(['data' => []]);
                // throw new Exception('Error in fetching templates', 1); // NOSONAR
            }

            $flowTemplates = [];
            foreach ($templates->data as $template) {
                $template = $this->buildTemplateParams($template);
                if ($template->subType != 'flow') {
                    continue;
                }

                $flowId = $template->buttons[0]->flow_id ?? null;
                if ($flowId) {
                    $mapping = FlowMapping::where(['account_id' => $accountId, 'id' => $flowId])->first();
                    $mapping && $template->updated_at = $mapping->updated_at;
                    $mapping && $template->mapping = $mapping->mapping ? json_decode($mapping->mapping) : '';
                }
                $flowTemplates[] = $template;
            }

            return $this->jsonOk(['data' => $flowTemplates]);
        } catch (Exception $e) {
            Log::error("[FlowController:flowTemplates] req-$this->requestId, Exception: ".$e->getTraceAsString());

            return $this->jsonError(['message' => $e->getMessage()]);
        }
    }

    public function flowAsset(Request $request, $flowId)
    {
        $input = $request->validate(['user_id' => 'required']);
        $accountId = readUserId($input['user_id']);

        try {
            Log::info("[FlowController:flowAsset] flowId:$flowId, req: $this->requestId: ", $input);
            $waba = new Waba($accountId, $this->requestId);

            $flow = $waba->fetchFlowAsset($flowId);
            if (! $flow) {
                throw new Exception('Flow Asset not found', 1); // NOSONAR
            }

            $flowService = new FlowService;
            $result = $flowService->findInputs($flow);

            return $this->jsonOk(['data' => $result]);
        } catch (Exception $e) {
            Log::error("[FlowController:flowAsset] req-$this->requestId, Exception: ".$e->getMessage());

            return $this->jsonError(['message' => $e->getMessage()]);
        }
    }
}
