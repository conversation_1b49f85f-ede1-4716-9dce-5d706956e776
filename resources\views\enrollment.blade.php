<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Message Delivery Status</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

      :root {
        --primary-color: #3a0ca3;
        --primary-light: #4361ee;
        --primary-dark: #2b0c75;
        --success-color: #0ead69;
        --error-color: #e63946;
        --warning-color: #f77f00;
        --info-color: #4cc9f0;
        --muted-color: #8e9aaf;
        --bg-color: #f7f9fc;
        --card-bg: #ffffff;
        --text-primary: #2b2d42;
        --text-secondary: #6c757d;
        --border-radius: 12px;
        --shadow: 0 10px 20px rgba(58, 12, 163, 0.05);
        --transition: all 0.3s ease;
      }

      body {
        background-color: var(--bg-color);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        color: var(--text-primary);
        padding: 1.5rem;
      }

      .phone-number {
		  font-family: 'SF Mono', 'Consolas', monospace;
		  font-size: 0.9rem;
		  padding: 0.5rem 0.75rem;
		  background-color: rgba(76, 201, 240, 0.08);
		  border-radius: 6px;
		  display: block;
		  color: var(--info-color);
		  font-weight: 500;
		  border-left: 3px solid var(--info-color);
		}
      .status-card {
        max-width: 600px;
        width: 100%;
        border: none;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        background-color: var(--card-bg);
        transition: var(--transition);
        overflow: hidden;
      }

      .card-header {
        background-color: transparent;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.75rem 2rem 1.5rem;
      }

      .card-body {
        padding: 1.75rem 2rem;
      }

      .card-footer {
        background-color: rgba(247, 249, 252, 0.6);
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.25rem 2rem;
      }

      .header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .title-container {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .title-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
        box-shadow: 0 4px 12px rgba(58, 12, 163, 0.15);
      }

      .card-title {
        font-weight: 700;
        margin-bottom: 0.25rem;
        color: var(--text-primary);
      }

      .subtitle {
        font-size: 0.875rem;
        color: var(--text-secondary);
        font-weight: 500;
      }

      .status-badge-lg {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.875rem;
        font-weight: 600;
        letter-spacing: 0.3px;
        text-transform: uppercase;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .badge-success {
        background-color: rgba(14, 173, 105, 0.1);
        color: var(--success-color);
      }

      .badge-danger {
        background-color: rgba(230, 57, 70, 0.1);
        color: var(--error-color);
      }

      .badge-warning {
        background-color: rgba(247, 127, 0, 0.1);
        color: var(--warning-color);
      }

      .badge-primary {
        background-color: rgba(67, 97, 238, 0.1);
        color: var(--primary-light);
      }

      .badge-secondary {
        background-color: rgba(142, 154, 175, 0.1);
        color: var(--muted-color);
      }

      .badge-info {
        background-color: rgba(76, 201, 240, 0.1);
        color: var(--info-color);
      }

      .info-section {
        margin-bottom: 2rem;
      }

      .info-row {
        margin-bottom: 1.25rem;
        display: flex;
        flex-direction: column;
        gap: 0.35rem;
      }

      .row-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-secondary);
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .row-value {
        font-weight: 500;
      }

      .request-id {
        font-family: 'SF Mono', 'Consolas', monospace;
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
        background-color: rgba(58, 12, 163, 0.04);
        border-radius: 8px;
        word-break: break-all;
        display: inline-block;
        color: var(--primary-color);
        font-weight: 500;
        border-left: 3px solid var(--primary-color);
      }

      .alert {
        border-radius: 10px;
        padding: 1rem 1.25rem;
        border: none;
        font-size: 0.95rem;
        margin-bottom: 0;
      }

      .alert-danger {
        background-color: rgba(230, 57, 70, 0.08);
        color: #c1121f;
      }

      .alert-info {
        background-color: rgba(76, 201, 240, 0.08);
        color: #2176ae;
      }

      .timestamp {
        font-size: 0.85rem;
        color: var(--muted-color);
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .pulse {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: currentColor;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% {
          transform: scale(0.95);
          box-shadow: 0 0 0 0 rgba(var(--pulse-color, 58, 12, 163), 0.5);
        }

        70% {
          transform: scale(1);
          box-shadow: 0 0 0 6px rgba(var(--pulse-color, 58, 12, 163), 0);
        }

        100% {
          transform: scale(0.95);
          box-shadow: 0 0 0 0 rgba(var(--pulse-color, 58, 12, 163), 0);
        }
      }

      /* Timeline Styling */
      .timeline-container {
        margin: 0.5rem 0 2rem;
        position: relative;
      }

      .timeline {
        display: flex;
        align-items: center;
        position: relative;
        margin: 2rem 0 1rem;
        z-index: 1;
      }

      .timeline::before {
        content: '';
        position: absolute;
        height: 3px;
        background-color: #e9ecef;
        left: 0;
        right: 0;
        z-index: -1;
      }

      .timeline-step {
        flex: 1;
        text-align: center;
        position: relative;
        padding-top: 2.5rem;
      }

      .timeline-marker {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #fff;
        border: 3px solid #e9ecef;
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        z-index: 2;
        transition: var(--transition);
      }

      .timeline-step.active .timeline-marker {
        background-color: var(--primary-light);
        border-color: var(--primary-light);
        box-shadow: 0 0 0 4px rgba(67, 97, 238, 0.2);
      }

      .timeline-step.completed .timeline-marker {
        background-color: var(--success-color);
        border-color: var(--success-color);
      }

      .timeline-step.failed .timeline-marker {
        background-color: var(--error-color);
        border-color: var(--error-color);
      }

      .timeline-step.completed .timeline-marker::after {
        content: '✓';
        color: #fff;
        font-size: 0.75rem;
        font-weight: bold;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .timeline-step.failed .timeline-marker::after {
        content: '×';
        color: #fff;
        font-size: 1rem;
        font-weight: bold;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .timeline-title {
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--text-secondary);
        margin-bottom: 0.25rem;
      }

      .timeline-step.active .timeline-title {
        color: var(--primary-light);
      }

      .timeline-step.completed .timeline-title {
        color: var(--success-color);
      }

      .timeline-step.failed .timeline-title {
        color: var(--error-color);
      }

      .timeline-date {
        font-size: 0.7rem;
        color: var(--muted-color);
      }

      .timeline-progress {
        position: absolute;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-light), var(--primary-color));
        left: 0;
        z-index: -1;
      }

      /* Extra information section */
      .extra-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 1rem;
      }

      .duration-badge {
        background-color: rgba(58, 12, 163, 0.04);
        color: var(--primary-color);
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.8rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .card-header, .card-body, .card-footer {
          padding: 1.25rem;
        }

        .header-content {
          flex-direction: column;
          align-items: flex-start;
          gap: 1rem;
        }

        .timeline {
          margin: 2.5rem 0 2rem;
        }

        .timeline-step {
          padding-top: 2rem;
        }

        .timeline-title {
          font-size: 0.7rem;
        }

        .timeline-date {
          font-size: 0.65rem;
        }

        .extra-info {
          flex-direction: column;
          align-items: flex-start;
        }
      }

      @media (max-width: 576px) {
        .timeline-step {
          padding-top: 1.75rem;
        }

        .timeline-marker {
          width: 18px;
          height: 18px;
        }
      }
    </style>
  </head>
  <body>
    <div class="card status-card">
      <div class="card-header">
        <div class="header-content">
          <div class="title-container">
            <div class="title-icon">
              <i class="fas fa-paper-plane"></i>
            </div>
            <div>
              <h4 class="card-title">Message Delivery Status</h4>
              <p class="subtitle">Track your message delivery progress</p>
            </div>
          </div>
          <div>
            @if ($status == 'delivered')
              <span class="status-badge-lg badge-success">
                <i class="fas fa-check-circle"></i> Delivered
              </span>
            @elseif ($status == 'failed')
              <span class="status-badge-lg badge-danger">
                <i class="fas fa-times-circle"></i> Failed
              </span>
            @elseif ($status == 'sent')
              <span class="status-badge-lg badge-warning">
                <i class="fas fa-paper-plane"></i> Sent
              </span>
            @elseif ($status == 'read')
              <span class="status-badge-lg badge-primary">
                <i class="fas fa-eye"></i> Read
              </span>
            @else
              <span class="status-badge-lg badge-secondary">
                <div class="pulse" style="--pulse-color: 142, 154, 175"></div>
                Processing
              </span>
            @endif
          </div>
        </div>
      </div>

      <div class="card-body">
        <div class="info-section">
          <div class="info-row">
		  <div class="row-label">
		    <i class="fas fa-phone"></i> Recipient Phone
		  </div>
		  <div class="row-value">
		    <span class="phone-number">{{ $phone }}</span>
		  </div>
		</div>

          <!-- Timeline visualization -->
          <!-- Timeline visualization -->
			<div class="timeline-container">
			  <div class="row-label mb-2">
			    <i class="fas fa-clock-rotate-left"></i> Delivery Progress
			  </div>
			  <div class="timeline">
			    <!-- Timeline Progress Bar (dynamically set width based on status) -->
			    @php
			      $progressWidth = match($status) {
			        'sent' => '33%',
			        'delivered' => '66%',
			        'read' => '100%',  // Ensure 100% width for read status
			        'failed' => '33%',
			        default => '0%'
			      };
			    @endphp
			    <div class="timeline-progress" style="width: {{ $progressWidth }}"></div>

			    <!-- Initiation Step -->
			    <div class="timeline-step completed">
			      <div class="timeline-marker"></div>
			      <div class="timeline-title">Initiated</div>
			      <div class="timeline-date">{{ $createdTime }}</div>
			    </div>

			    <!-- Sent Step -->
			    <div class="timeline-step {{ in_array($status, ['sent', 'delivered', 'read']) ? 'completed' : ($status == 'failed' ? 'failed' : '') }}">
			      <div class="timeline-marker"></div>
			      <div class="timeline-title">Sent</div>
			      <div class="timeline-date">{{ in_array($status, ['sent', 'delivered', 'read', 'failed']) ? $updatedTime : '—' }}</div>
			    </div>

			    <!-- Delivered Step -->
			    <div class="timeline-step {{ in_array($status, ['delivered', 'read']) ? 'completed' : '' }}">
			      <div class="timeline-marker"></div>
			      <div class="timeline-title">Delivered</div>
			      <div class="timeline-date">{{ in_array($status, ['delivered', 'read']) ? $updatedTime : '—' }}</div>
			    </div>

			    <!-- Read Step -->
			    <div class="timeline-step {{ $status == 'read' ? 'completed' : '' }}">
			      <div class="timeline-marker"></div>
			      <div class="timeline-title">Read</div>
			      <div class="timeline-date">{{ $status == 'read' ? $updatedTime : '—' }}</div>
			    </div>
			  </div>
			</div>

			@if ($showReason)
			  <div class="info-row">
			    <div class="row-label">
			      <i class="fas fa-triangle-exclamation"></i> Failure Reason
			    </div>
			    <div class="row-value">
			      <div class="alert alert-danger">
			        {{ $message }}
			      </div>
			    </div>
			  </div>
			@else
			  <div class="alert alert-info">
			    <i class="fas fa-circle-info me-2"></i>
			    {{ $message }}
			  </div>
			@endif

			<div class="extra-info">
			  <div class="duration-badge">
			    <i class="fas fa-stopwatch"></i> Processing time: {{ $duration }}
			  </div>

			  <div class="timestamp">
			    <i class="far fa-calendar-check"></i> Initiated: {{ $createdAt }}
			  </div>
			</div>
      </div>

    </div>
      <div class="card-footer">
	  <div class="timestamp text-end">
	    <i class="far fa-clock"></i> Last Updated: {{ $updatedAt }}
	  </div>
	</div>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Animate entrance of the card
        const card = document.querySelector('.status-card');
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
          card.style.opacity = '1';
          card.style.transform = 'translateY(0)';
        }, 100);
      });
    </script>
  </body>
</html>
