<?php

namespace App\Models;

use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use PHPOpenSourceSaver\JWTAuth\Contracts\JWTSubject;


class User extends Authenticatable implements J<PERSON>TSubject
{
    use HasApiTokens;
    use HasFactory;
    use Notifiable;

    protected $guarded = [];

     public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
          return [
            'portal_id' => $this->portal_id,
            'account_id' => $this->account_id,
            'user_id' => $this->id,
            'email' => $this->email,
        ];
    }
    
    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = strtolower(trim($value));
    }

    /**
     * Define the relationship to permissions.
     */
    public function permissions()
    {
        return $this->belongsToMany(Permission::class, 'user_permissions')
            ->withPivot('account_id')
            ->withTimestamps();
    }

    /**
     * Define the relationship to the role.
     */
    public function role()
    {
        return $this->belongsTo(Role::class, 'role_id');
    }

    protected $hidden = [
        'account_id',
        'created_at',
        'updated_at',
    ];
}
