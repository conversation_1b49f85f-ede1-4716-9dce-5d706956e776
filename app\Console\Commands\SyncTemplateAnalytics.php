<?php

namespace App\Console\Commands;

use Log;
use App\Helpers\Waba;
use Ramsey\Uuid\Uuid;
use App\Models\Account;
use Illuminate\Console\Command;
use App\Models\TemplateAnalytics;

class SyncTemplateAnalytics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:template-analytics {days?}';

    public $waba;

    public $account;

    public $requestId;

    public $accountId;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Template Analytics will be sync for last day';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $uuid = Uuid::uuid4();
        $this->accountId = env('APP_ENV') === 'production' ? 51 : 23;
        $this->requestId = round(microtime(true) * 1000).'-req-'.$uuid->toString();
        $this->waba = new Waba($this->accountId, $this->requestId);
        $this->account = Account::where('id', $this->accountId)->where('paid', '!=', 0)->first();
        if (empty($this->account)) {
            return;
        }
        $templates = $this->fetchTemplates();

        if (empty($templates)) {
            Log::error('[SyncTemplateAnalytics:handle] No templates found.');

            return;
        }

        $this->processTemplateAnalytics($templates);
    }

    public function fetchTemplates()
    {
        $response = $this->waba->fetchTemplates('fields=id,name');
        Log::info('[SyncTemplateAnalytics:fetchTemplates] Response: '.json_encode($response));

        return $response ? array_combine(array_column($response, 'id'), array_column($response, 'name')) : [];
        $response = $this->waba->fetchTemplates('fields=id,name');
        Log::info('[SyncTemplateAnalytics:fetchTemplates], response: '.json_encode($response));
        if (isset($response) && empty($response)) {
            Log::error('[SyncTemplateAnalytics:fetchTemplates], Error: '.json_encode($response));

            return;
        }
    }

    public function processTemplateAnalytics($templates)
    {
        $batchSize = 10;
        $days = (int) ($this->argument('days') ?? 1);
        $startDate = now()->subDays($days)->startOfDay()->toDateTimeString();
        $endDate = now()->subDay(1)->endOfDay()->toDateTimeString();

        collect($templates)
            ->chunk($batchSize)
            ->each(fn ($batch) => $this->getTemplateAnalytics($startDate, $endDate, $batch->toArray()));
    }

    public function getTemplateAnalytics($start, $end, $templates)
    {
        $after = null;
        $templateIds = array_keys($templates);

        do {
            $response = $this->waba->getTemplateAnalytics($start, $end, $templateIds, $after);
            Log::info('[SyncTemplateAnalytics:getTemplateAnalytics] Response: '.json_encode($response));

            if (! isset($response->data[0]->data_points)) {
                // Log::warning('[SyncTemplateAnalytics:getTemplateAnalytics] No data points found.');
                break;
            }

            $this->storeAnalyticsData($response->data[0]->data_points, $templates);
            $after = $response->paging->cursors->after ?? null;
            usleep(2000000);
        } while ($after);
    }

    public function storeAnalyticsData($data_points, $templates)
    {
        foreach ($data_points as $point) {
            $clicked = collect($point->clicked ?? [])
                ->whereIn('type', ['quick_reply_button', 'url_button'])
                ->sum('count');

            $button_count = collect($point->clicked ?? [])
                ->whereIn('type', ['quick_reply_button', 'url_button'])
                ->count();

            TemplateAnalytics::updateOrCreate(
                [
                    'template_id' => $point->template_id,
                    'start_unix_time' => $point->start,
                    'end_unix_time' => $point->end,
                ],
                [
                    'template_name' => $templates[$point->template_id],
                    'account_id' => $this->accountId,
                    'total_buttons' => $button_count,
                    'send_message_count' => $point->sent,
                    'delivered_message_count' => $point->delivered,
                    'read_message_count' => $point->read,
                    'click_button_count' => $clicked,
                    'template_id' => $point->template_id,
                    'start_unix_time' => $point->start,
                    'end_unix_time' => $point->end,
                ]
            );
        }
    }
}
