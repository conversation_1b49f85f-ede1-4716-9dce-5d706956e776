<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AppController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\StripeController;
use App\Http\Controllers\HubspotController;
use App\Http\Controllers\FacebookController;
use App\Http\Middleware\BasicAuthMiddleware;
use App\Http\Controllers\HsConversationController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
*/

// App routes
Route::get('/', [AppController::class, 'index']);
Route::get('/test', [AppController::class, 'test']);
Route::get('/refresh', [AppController::class, 'refresh']);
Route::get('/success', [AppController::class, 'success']);
Route::get('/last-step', [AppController::class, 'lastStep']);
Route::get('/facebook/auth', [FacebookController::class, 'auth']);
Route::get('/facebook/test', [FacebookController::class, 'test']);
Route::get('/facebook/deauth', [FacebookController::class, 'deAuth']);
Route::get('/facebook/delete', [FacebookController::class, 'delete']);
Route::get('/report/export', [ReportController::class, 'export']);
Route::get('/campaign/export', [AppController::class, 'campaignExport']);
Route::get('/workflow/export', [AppController::class, 'workflowExport']);
Route::get('/cancel_account', [StripeController::class, 'cancelAccount']);
Route::get('/workflow/report/export', [ReportController::class, 'workflowExport']);
Route::get('/workflows/enrollment/status/{requestId}', [AppController::class, 'workflowEnrollmentStatus']);

// UI Dashboards
Route::get('/initiate', [AppController::class, 'initiate']);
Route::get('/helpdesk', [AppController::class, 'helpdesk']);
Route::get('/options', [AppController::class, 'options']);
Route::get('/dashboard', [AppController::class, 'dashboard']);
Route::get('/request_access/{email}', [UserController::class, 'requestAccess']);

// hubspot routes
Route::get('/hubspot/auth', [HubspotController::class, 'auth']);
Route::get('/hubspot/conversation/auth', [HsConversationController::class, 'auth']);

Route::get('/ver', function () {
    phpinfo();
})->middleware(BasicAuthMiddleware::class);
Route::get('/add_dummy_subscription', [AppController::class, 'addDummySub'])->middleware(BasicAuthMiddleware::class);

// Stripe routes
Route::get('buy/test', [StripeController::class, 'test']);
Route::get('buy/{plan}', [StripeController::class, 'buy']);
