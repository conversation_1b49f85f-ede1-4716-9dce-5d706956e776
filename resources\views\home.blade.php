@extends('layouts.new')

@section('title')
		Authorize HubSpot
@endsection

@section('content')
	<header>
	    <div class="container">
	        <div class="logo_main">
	            <a href="/">
	            	<img src="/img/niswey-logo.png" width="60" alt="niswey-logo" />
	            </a>
	        </div>
	    </div>
	</header>
	<section id="content" class="container">
	    <div class="flex-container">
	        <div class="leftcontent">
	            <h1>{{env('APP_NAME')}}: Create the buzz around your business</h1>
	            <p class="paratext">
	            	Integrate your WhatsApp Business Accounts (WABA) with HubSpot. Send out WhatsApp marketing campaigns to your HubSpot contacts lists, track all WhatsApp conversations in HubSpot, and do it all on the free HubSpot plan or enterprise, or anything in between
				</p>
	        </div>
	        <div class="rightcontent">
	        	<img src="img/vira-graphic.png" alt="authorize HubSpot" />
	        </div>
	    </div>
	</section>
	<section class="container margin_top pb-5">
	    <div class="custom-progress-bar">
	        <div class="custom-progress-step custom-progress-step-active" data-title="AUTHORIZE"></div>
	        <div class="custom-progress-connector"></div>
	        <div class="custom-progress-step" data-title="WHATSAPP CREDENTIALS"></div>
	        <div class="custom-progress-connector"></div>
	        <div class="custom-progress-step" data-title="SET UP WEBHOOK"></div>
	    </div>
	    <div class="accwrap">
	        <button class="accordion active">Authorize your HubSpot portal</button>
	        <div class="panel active">
	            <div class="panelbox">
	                <p class="paratext">Authorize your HubSpot CRM.</p>
	                <a href="{{$authUrl}}">
	                	<button class="btn1 authorize-btn" id="authorize-btn">Authorize Now</button>
	                </a>
	            </div>
	        </div>
	        <button class="accordion disabled">Enter your WhatsApp credentials</button>
	        <button class="accordion disabled">Setup HubSpot</button>
	    </div>
	    <script type="text/javascript">
	    	let inboxRedirectUrl =  {!! json_encode($inboxRedirectUrl) !!};
            if (inboxRedirectUrl) {
                console.log(inboxRedirectUrl)
                window.localStorage.setItem('inboxRedirectUrl', inboxRedirectUrl);
            }
	    	window.onload = () => {
				let activeAccordion = document.querySelector('.accordion.active');
				activeAccordion && activeAccordion.scrollIntoView({ block: 'start',  behavior: 'smooth' });
			}
	    </script>
	</section>
@endsection('content')

